/*
 * 漳州-业务通用字典
 */

// -------------------------------- 隐患治理 --------------------------------


import {BasicColumn, FormSchema} from "/@/components/Table";
import {genInput, genSelect} from "/@/utils/formUtils";
import {genQueryTimeInput} from "/@/utils/tableUtils";
import {formatToDate} from "/@/utils/dateUtil";
import {FLOW_STATUS_MAP} from "/@/enums/flowEnum";
import {COMMON_OPTION} from "/@/enums/commonEnum";


// -------------------------------- 隐患等级 --------------------------------
export const ISSUE_TYPE_DEGREE_OPTION = [
  {"fullName": "一般", "id": 0},
  {"fullName": "重大", "id": 1},
];

// -------------------------------- 隐患类别 --------------------------------
export enum ISSUE_TYPE_ENUM {
  //隐患类别一
  ISSUE_TYPE_ONE = 'issueTypeOne',
  //隐患类别二
  ISSUE_TYPE_TWO = 'issueTypeTwo',
  //隐患类别三
  ISSUE_TYPE_THREE = 'issueTypeThree',
}




//----------------------------隐患台账列表头(旧)-------------------------------------
// export const issueColumn: BasicColumn[] = [
//   // {title: '序号', dataIndex: 'sort'},
//   {title: '检查单位', dataIndex: 'checkDep'},
//   {title: '检查类别', dataIndex: 'checkType'},
//   {title: '检查名称', dataIndex: 'checkName'},
//   {title: '整改单编号', dataIndex: 'handleFormNum'},
//   {
//     title: '隐患所在部位', children: [
//       {title: '隐患所在机组', dataIndex: 'issueLocaUnitName'},
//       {title: '隐患所在区域', dataIndex: 'issueLocaAreaName'},
//       {title: '隐患具体区域', dataIndex: 'issueLocaAreaDetailed'}
//     ],
//     align: 'center'
//   },
//   {title: '责任单位', dataIndex: 'resDepName'},
//   {title: '责任班组', dataIndex: 'respClassName'},
//   {title: '隐患描述', dataIndex: 'issueDescription'},
//   {title: '整改要求', dataIndex: 'handleRequest'},
//   {title: '隐患分类标题（细类）', dataIndex: 'issueTypeTitleLittleName'},
//   {title: '隐患分类内容（细类）', dataIndex: 'issueTypeContentLittleName'},
//   {title: '隐患分类（大类）', dataIndex: 'issueTypeBigName'},
//   {
//     title: '隐患类别', dataIndex: 'issueTypeDegree',
//     customRender: ({record}) => (ISSUE_TYPE_DEGREE_MAP[record.issueTypeDegree])
//   },
//   {title: '检查日期', dataIndex: 'checkDate', format: 'date|YYYY-MM-DD'},
//   {title: '整改期限', dataIndex: 'handleDateLimit', format: 'date|YYYY-MM-DD'},
//   {
//     title: '整改情况反馈', dataIndex: 'handleSituation',
//     customRender: ({record}) => (record.handleSituation == 1 ? '按期' : '逾期')
//   },
//   {title: '整改人', dataIndex: 'handleUserName'},
//   {title: '验证人', dataIndex: 'validatorUserName'},
//   {title: '关闭日期', dataIndex: 'closeDate', format: 'date|YYYY-MM-DD'},
//   {title: '备注', dataIndex: 'bankContent'},
// ];

const issueClassMap = {
  0:'一般',
  1:'重大'
}

//----------------------------隐患台账列表头-------------------------------------
export const issueColumn: BasicColumn[] = [
  {title: '发现部门/施工队', dataIndex: 'foundOrgName', width: 125},
  {
    title: '发现日期', dataIndex: 'foundDate',
    customRender: ({record}) => (formatToDate(record.foundDate)),
    width: 100,
  },
  {title: '检查类别', dataIndex: 'checkTypeName', width: 80},
  {title: '检查名称', dataIndex: 'checkChildTypeName', width: 80},
  {title: '隐患编号', dataIndex: 'issueNum', width: 165},
  {title: '公司直属单位', dataIndex: 'belongComName'},
  {title: '责任区域分期', dataIndex: 'resAreaTermName', width: 105},
  {title: '责任具体区域', dataIndex: 'resAreaDetailName', width: 220},
  {title: '隐患描述', dataIndex: 'issueDesc'},
  {title: '整改要求', dataIndex: 'handleRequire'},
  {title: '隐患类别一', dataIndex: 'issueTypeOneName', width: 120},
  {title: '隐患类别二', dataIndex: 'issueTypeTwoName', width: 100},
  {title: '隐患类别三', dataIndex: 'issueTypeThreeName', width: 100},
  {
    title: '隐患等级', dataIndex: 'issueClass',
    customRender: ({record}) => (issueClassMap[record.issueClass]), width: 80
  },
  {
    title: '关键隐患', dataIndex: 'isKeyIssue',
    customRender: ({record}) => (record.isKeyIssue ? '是' : '否'), width: 100
  },
  {
    title: '是否红线', dataIndex: 'isRedLine',
    customRender: ({record}) => (record.isRedLine ? '是' : '否'), width: 80
  },
  {title: '事故类型', dataIndex: 'accTypeName', width: 80},
  {title: '责任部门', dataIndex: 'resDepName', width: 100},
  {title: '责任班组', dataIndex: 'resClassName', width: 100},
  {title: '责任人', dataIndex: 'resPerName', width: 100},
  {title: '发现人', dataIndex: 'foundPerName', width: 100},
  {title: '发现人电话', dataIndex: 'foundPerTel', width: 100},
  {
    title: '整改时限', dataIndex: 'handleLimit',
    customRender: ({record}) => (formatToDate(record.handleLimit)), width: 100
  },
  {title: '整改人', dataIndex: 'handlePerName', width: 100},
  {
    title: '整改时间', dataIndex: 'handleDate',
    customRender: ({record}) => (formatToDate(record.handleDate)), width: 100
  },
  {title: '整改描述', dataIndex: 'handleDesc', width: 100},
  {
    title: '现场整改', dataIndex: 'isHandle',
    customRender: ({record}) => (record.isHandle ? '是' : '否'), width: 100
  },
  {title: '复查人', dataIndex: 'reCheckPerName', width: 100},
  {
    title: '是否关闭', dataIndex: 'isClose',
    customRender: ({record}) => (record.isClose ? '是' : '否'), width: 80
  },
  {
    title: '关闭日期', dataIndex: 'closeDate',
    customRender: ({record}) => (formatToDate(record.closeDate)), width: 100
  },
  {
    title: '超期整改', dataIndex: 'isExpireHandle',
    customRender: ({record}) => (record.isExpireHandle ? '是' : '否'), width: 100
  },
  {
    title: '流程状态', dataIndex: 'currentState',
    customRender: ({record}) => (FLOW_STATUS_MAP[record.currentState]), width: 80
  },
  {
    title: '是否被引用', dataIndex: 'isRef',
    customRender: ({record}) => (record.isRef ? '是' : '否'), width: 100
  },
  {title: '引用报告编号', dataIndex: 'RefReportNum', width: 105},
]


//----------------------------隐患台账编辑项-------------------------------------

export const issueFormSchemas: FormSchema[] = [
  genInput('检查单位', 'checkDep', false),
  genInput('检查类别', 'checkType', false),
  genInput('检查名称', 'checkName', false),
  genInput('整改单编号', 'handleFormNum', false),
  genSelect('隐患机组', 'issueLocaUnit', false),
  genSelect('隐患区域', 'issueLocaArea', false),
  genInput('隐患具体区域', 'issueLocaAreaDetailed', false),
  {
    field: 'respDep',
    label: '责任单位',
    component: 'OrganizeSelect',
    componentProps: {placeholder: '选择所属组织'},
    rules: [{required: false, trigger: 'blur', message: '请选择所属组织'}],
  },
  {
    field: 'resClass',
    label: '责任班组',
    component: 'OrganizeSelect',
    componentProps: {placeholder: '选择所属组织'},
    rules: [{required: false, trigger: 'blur', message: '请选择所属组织'}],
  },
  genInput('隐患描述', 'issueDescription', false),
  genInput('整改要求', 'handleRequest', false),
  genSelect('隐患分类标题（细类）', 'issueTypeTitleLittle', false),
  genSelect('隐患分类内容（细类）', 'issueTypeContentLittle', false),
  genSelect('隐患分类（大类）', 'issueTypeBig', false),
  genSelect('隐患类别', 'issueTypeDegree', false, 'string', ISSUE_TYPE_DEGREE_OPTION),
  genQueryTimeInput('检查日期', 'checkDate'),
  genQueryTimeInput('整改期限', 'handleDateLimit'),
  genSelect('整改情况反馈', 'handleSituation', false, 'string', COMMON_OPTION),
  {
    field: 'handleUserId',
    label: '整改人',
    component: 'UserSelect',
    componentProps: {placeholder: '选择整改人'},
    rules: [{required: false, trigger: 'blur', message: '选择整改人'}],
  },
  {
    field: 'validatorUserId',
    label: '验证人',
    component: 'UserSelect',
    componentProps: {placeholder: '选择验证人'},
    rules: [{required: false, trigger: 'blur', message: '选择验证人'}],
  },
  genQueryTimeInput('关闭日期', 'closeDate'),
  genInput('备注', 'bankContent', false),
]

//----------------------------隐患台账导入预览表头-------------------------------------
export const issueTableData = [
  {title: '发现部门/施工队', dataIndex: 'foundOrgName', width: 100},
  {
    title: '发现日期', dataIndex: 'foundDate',
    customRender: ({record}) => (formatToDate(record.foundDate))
    , width: 100
  },
  {title: '检查类别', dataIndex: 'checkTypeName', width: 100},
  {title: '检查名称', dataIndex: 'checkChildTypeName', width: 100},
  {title: '隐患编号', dataIndex: 'issueNum', width: 100},
  {title: '公司直属单位', dataIndex: 'belongComName', width: 100},
  {title: '责任区域分期', dataIndex: 'resAreaTermName', width: 100},
  {title: '责任具体区域', dataIndex: 'resAreaDetailName', width: 100},
  {title: '隐患描述', dataIndex: 'issueDesc', width: 100},
  {title: '整改要求', dataIndex: 'handleRequire', width: 100},
  {title: '隐患类别一', dataIndex: 'issueTypeOneName', width: 100},
  {title: '隐患类别二', dataIndex: 'issueTypeTwoName', width: 100},
  {title: '隐患类别三', dataIndex: 'issueTypeThreeName', width: 100},
  {
    title: '隐患等级', dataIndex: 'issueClassName',
    customRender: ({record}) => (issueClassMap[record.issueClass]), width: 100
  },
  {
    title: '是否关键隐患', dataIndex: 'isKeyIssue',
    customRender: ({record}) => (record.isKeyIssue ? '是' : '否'), width: 100
  },
  {
    title: '是否红线', dataIndex: 'isRedLine',
    customRender: ({record}) => (record.isRedLine ? '是' : '否'), width: 100
  },
  {title: '事故类型', dataIndex: 'accTypeName', width: 100},
  {title: '责任部门', dataIndex: 'resDepName'  ,width: 100},
  {title: '责任班组', dataIndex: 'resClassName'  ,width: 100},
  {title: '责任人', dataIndex: 'resPerName'  ,width: 100},
  {title: '发现人', dataIndex: 'foundPerName'  ,width: 100},
  {title: '发现人电话', dataIndex: 'foundPerTel', width: 100},
  {
    title: '整改时限', dataIndex: 'handleLimit',
    customRender: ({record}) => (formatToDate(record.handleLimit))
    ,width: 100},
  {title: '整改人', dataIndex: 'handlePerName'  ,width: 100},
  {
    title: '整改时间', dataIndex: 'handleDate',
    customRender: ({record}) => (formatToDate(record.handleDate))
    ,width: 100},
  {title: '整改描述', dataIndex: 'handleDesc'  ,width: 100},
  {
    title: '是否现场整改', dataIndex: 'isHandle',
    customRender: ({record}) => (record.isHandle ? '是' : '否')
    ,width: 100},
  {title: '复查人', dataIndex: 'reCheckPerName'  ,width: 100},
  {
    title: '是否关闭', dataIndex: 'isClose',
    customRender: ({record}) => (record.isClose ? '是' : '否')
    ,width: 100},
  {
    title: '关闭日期', dataIndex: 'closeDate',
    customRender: ({record}) => (formatToDate(record.closeDate))
    ,width: 100},
  {
    title: '是否超期整改', dataIndex: 'isExpireHandle',
    customRender: ({record}) => (record.isExpireHandle ? '是' : '否')
    ,width: 100},
  {
    title: '流程状态', dataIndex: 'currentState',
    customRender: ({record}) => (FLOW_STATUS_MAP[record.currentState])
    ,width: 100},
  {
    title: '是否被引用', dataIndex: 'isRef',
    customRender: ({record}) => (record.isRef ? '是' : '否')
    ,width: 100},
  {title: '引用报告编号', dataIndex: 'RefReportNum'  ,width: 100},
]

//----------------------------隐患区域导入预览表头-------------------------------------
export const issueAreaData = [
  {title: '区域分期', dataIndex: 'areaTerm', width: 100},
  {title: '具体区域', dataIndex: 'name', width: 100}
]


//----------------------------隐患相册台账列表头-------------------------------------
export const issuePicColumn: BasicColumn[] = [
  {title: '隐患编号', dataIndex: 'issueNum'},
  {
    title: '发现日期', dataIndex: 'foundDate',
    customRender: ({record}) => (formatToDate(record.foundDate))
    , width: 100
  },
  {title: '隐患类别一', dataIndex: 'issueTypeOneName'},
  {title: '隐患类别二', dataIndex: 'issueTypeTwoName'},
  {title: '隐患类别三', dataIndex: 'issueTypeThreeName'},
  {
    title: '整改时间', dataIndex: 'handleDate',
    customRender: ({record}) => (formatToDate(record.handleDate))
  },
  {title: '隐患照片', dataIndex: 'issuePicList', key: 'issuePicList'},
  {title: '整改照片', dataIndex: 'handlePicList', key: 'handlePicList'},
]


//---------------------------专项检查--------------------------------------
export const specCheckColumn: BasicColumn[] = [
  {title: '检查名称', dataIndex: 'name', width: 80},
  {title: '检查编号', dataIndex: 'num', width: 80},
  {title: '检查地点', dataIndex: 'location', width: 80},
  {
    title: '检查时间', dataIndex: 'checkDate',
    customRender: ({record}) => (formatToDate(record.checkDate)),
    width: 100,
  },
]
