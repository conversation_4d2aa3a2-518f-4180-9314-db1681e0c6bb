/*
 * 漳州-业务通用字典
 */
// -------------------------------- 通用字段 --------------------------------
export const BOOLEAN_OPTIONS = [
  {"fullName": "否", "id": '0'},
  {"fullName": "是", "id": '1'},
]

export const BOOL_OPTIONS = [
  {"fullName": "否", "id": false},
  {"fullName": "是", "id": true},
]

// -------------------------------- 通用业务 --------------------------------
export const BASE_ORGANIZE_OPTIONS = [
  { fullName: '部门', id: 'department' },
  { fullName: '班组', id: 'team' },
]

// -------------------------------- 流程引擎 --------------------------------
/** 流程FLowParser打开opType */
export enum FLOW_OP_TYPE {
  EDIT = '-1', // 编辑
  DETAIL = 0, // 详情
  // DETAIL = 1, // ???
  // DETAIL = 2, // ???
  // DETAIL = 4, // ???
}

/** TODO 流程实例状态 */
export enum FLOW_STATE {

}

// -------------------------------- 行动项 --------------------------------
/** 行动项来源类型 */
export const ZZ_ACT_ITEM_FROM_TYPE = [
  { fullName: '部门', id: '部门' },
  { fullName: '工程周例会', id: '工程周例会' },
  { fullName: '外部周例会', id: '外部周例会' },
  { fullName: '部门例会', id: '部门例会' },
  { fullName: '其他', id: '其他' },
]

export const ZZ_ACT_ITEM_URGENT_LEVEL = [
  { fullName: '一般', id: '一般' },
  { fullName: '重要', id: '重要' },
  { fullName: '紧急', id: '紧急' },
]

export const ZZ_ACT_ITEM_STATUS = [
  { fullName: '进行中', id: '进行中' },
  { fullName: '已完成', id: '已完成' },
]

// -------------------------------- 工程影像 --------------------------------
/** 影像类别 */
export const ZZ_PROJECT_MEDIA_TYPE = [
  { fullName: '照片', id: '照片' },
  { fullName: '视频', id: '视频' },
]

/** 密级 */
export const ZZ_PROJECT_MEDIA_SECURITY = [
  { fullName: '非密', id: '非密' },
  { fullName: '保密', id: '保密' },
  { fullName: '机密', id: '机密' },
  { fullName: '绝密', id: '绝密' },
]

// -------------------------------- 培训 --------------------------------
/** 培训类型 */
export enum STUDY_SPECIAL_TYPE_ENUM {
  SPECIAL_TRAIN = 'zx',
  IN_FACTORY = 'in_factory',
  PROC_STUDY = 'proc_study',
  EXP_FEEDBACK_STUDY = 'exp_feedback_study',
  EDU_TRAIN = 'edu_train',
  ASSEMBLE_TRAIN = 'assemble_train',
  MORNING_MEETING = 'morning_meeting',
  SPECIAL_AUTH = 'special_auth',
  TEAM_AUTH = 'team_auth',
  SAFE_DELIVER = 'safe_deliver',
}

export const STUDY_SPECIAL_TYPE_MAP = {
  [STUDY_SPECIAL_TYPE_ENUM.SPECIAL_TRAIN]: '专项培训',
  [STUDY_SPECIAL_TYPE_ENUM.IN_FACTORY]: '入场培训',
  [STUDY_SPECIAL_TYPE_ENUM.PROC_STUDY]: '程序学习',
  [STUDY_SPECIAL_TYPE_ENUM.EXP_FEEDBACK_STUDY]: '经验反馈学习',
  [STUDY_SPECIAL_TYPE_ENUM.EDU_TRAIN]: '教育培训',
  [STUDY_SPECIAL_TYPE_ENUM.ASSEMBLE_TRAIN]: '集中培训',
  [STUDY_SPECIAL_TYPE_ENUM.MORNING_MEETING]: '早班会',
  [STUDY_SPECIAL_TYPE_ENUM.SPECIAL_AUTH]: '专项授权',
  [STUDY_SPECIAL_TYPE_ENUM.TEAM_AUTH]: '班组授权',
}

/** 试题类型 */
export enum QUESTION_TYPE {
  JUDGE = '0',
  SINGLE = '1',
  MULTIPLY = '2',
}

export const QUESTION_TYPE_OPTIONS = [
  {"fullName": "判断题", "id": QUESTION_TYPE.JUDGE},
  {"fullName": "单选题", "id": QUESTION_TYPE.SINGLE},
  {"fullName": "多选题", "id": QUESTION_TYPE.MULTIPLY},
  // {"fullName": "填空题", "id": 3},
  // {"fullName": "简答题", "id": 4},
  // {"fullName": "材料题", "id": 5}
]

export const QUESTION_TYPE_MAP = {
  [QUESTION_TYPE.JUDGE]: '判断题',
  [QUESTION_TYPE.SINGLE]: '单选题',
  [QUESTION_TYPE.MULTIPLY]: '多选题',
}

export const QUESTION_TYPE_V_MAP = {
  '判断题': QUESTION_TYPE.JUDGE,
  '单选题': QUESTION_TYPE.SINGLE,
  '多选题': QUESTION_TYPE.MULTIPLY,
}


/** 试题类型 */
export const QUESTION_STATUS_MAP = {
  '0': '可用',
  '1': '禁用',
}

export const QUESTION_STATUS_V_MAP = {
  '可用': '0',
  '禁用': '1',
}

/** 培训部门 */
export const ORGANIZE_LIST_OPTION = [
  {fullName: '核与系统工程事业部'},
  {fullName: '漳州项目部'},
  {fullName: '经理部'},
  {fullName: '综合管理部'},
  {fullName: '财务部'},
  {fullName: '商务部'},
  {fullName: '工程部'},
  {fullName: '综合队'},
  {fullName: '技术部'},
  {fullName: '安全监督部'},
  {fullName: '安全环保部'},
  {fullName: '安全质量中心'},
  {fullName: 'QA部'},
  {fullName: 'QC部'},
  {fullName: '物资部'},
  {fullName: '机通队'},
  {fullName: '管焊队'},
  {fullName: '电仪队'},
  {fullName: '预制加工厂'},
  {fullName: '土建队'},
  // {fullName: 'BOP土建队'},
]

/** 培训部门 */
export const ORGANIZE_LIST_OPTION_NEW = [
  {fullName: '核与系统工程事业部'},
  {fullName: '经理部'},
  {fullName: '综合管理部'},
  {fullName: '财务部'},
  {fullName: '商务部'},
  {fullName: '漳州项目部'},
  {fullName: '综合队'},
  {fullName: '技术部'},
  {fullName: '安全监督部'},
  {fullName: '安全环保部'},
  {fullName: '安全质量中心'},
  {fullName: 'QA部'},
  {fullName: 'QC部'},
  {fullName: '物资部'},
  {fullName: '机通队'},
  {fullName: '管焊队'},
  {fullName: '电仪队'},
  {fullName: '预制加工厂'},
  {fullName: '土建队'},
  // {fullName: 'BOP土建队'},
]

export const ZZ_EDU_RECORD_IN_CONFIG_LEVEL = [
  { fullName: '一级', id: 1 },
  { fullName: '二级', id: 2 },
  { fullName: '三级', id: 3 },
]
export const ZZ_EDU_RECORD_IN_CONFIG_LEVEL_MAP = {
  '1': '一级',
  '2': '二级',
  '3': '三级',
}

export const ZZ_EDU_RECORD_IN_CONFIG_ORG_RANGE = [
  { fullName: '当前层级', id: '1' },
  { fullName: '当前层级+子层级', id: '2' },
]

export const ZZ_EDU_RECORD_IN_CONFIG_ORG_RANGE_MAP = {
  '1': '当前层级',
  '2': '当前层级+子层级',
}

export const ZZ_EDU_RECORD_IN_CONFIG_POS_RANGE = [
  { fullName: '全部岗位', id: '1' },
  { fullName: '指定岗位', id: '2' },
]

export const ZZ_EDU_RECORD_IN_CONFIG_POS_RANGE_MAP = {
  '1': '全部岗位',
  '2': '指定岗位',
}

// -------------------------------- 工机具 --------------------------------
export const MACHINE_LEDGER_STATE = [
  {fullName: '在库', id: '在库'},
  {fullName: '在用', id: '在用'},
  {fullName: '封存', id: '封存'},
  {fullName: '报废', id: '报废'},
  {fullName: '待检', id: '待检'},
  {fullName: '遗失', id: '遗失'},
  {fullName: '撤场', id: '撤场'},
]

// -------------------------------- 特种作业 --------------------------------
/** 编审批状态 */
export enum SPECIAL_WORK_AUDIT_STATE {
  DRAFT = '1',
  AUDIT = '2',
  APPROVAL = '3',
  PASS = '4',
}

/** 是否初始文件状态 */
export enum FILE_INITIAL {
  NOT_EDIT = 1,
  HAVE_EDIT = 2,
}

export const SPECIAL_WORK_AUDIT_STATE_MAP = {
  [SPECIAL_WORK_AUDIT_STATE.DRAFT]: '编制',
  [SPECIAL_WORK_AUDIT_STATE.AUDIT]: '审批',
  [SPECIAL_WORK_AUDIT_STATE.APPROVAL]: '批准',
  [SPECIAL_WORK_AUDIT_STATE.PASS]: '通过',
}

// -------------------------------- 车辆相关状态 --------------------------------
/** 车辆业务流程状态 */
export enum VEHICLE_PROCESS_STATE {
  DRAFT = '0',
  PENDING_REVIEW = '1',
  REJECTED = '2',
  ACCEPTED = '3',
  CONFIRMED = '4',
}

export const VEHICLE_PROCESS_STATE_MAP = {
  [VEHICLE_PROCESS_STATE.DRAFT]: '编制',
  [VEHICLE_PROCESS_STATE.PENDING_REVIEW]: '待审核',
  [VEHICLE_PROCESS_STATE.REJECTED]: '退回',
  [VEHICLE_PROCESS_STATE.ACCEPTED]: '已接受',
  [VEHICLE_PROCESS_STATE.CONFIRMED]: '已确认',
}

export const VEHICLE_PROCESS_STATE_OPTIONS = [
  { fullName: '编制', id: '0' },
  { fullName: '待审核', id: '1' },
  { fullName: '退回', id: '2' },
  { fullName: '已接受', id: '3' },
  { fullName: '已确认', id: '4' },
]

/** 车辆业务流程状态颜色映射 */
export const VEHICLE_PROCESS_STATE_COLOR_MAP = {
  [VEHICLE_PROCESS_STATE.DRAFT]: 'default',
  [VEHICLE_PROCESS_STATE.PENDING_REVIEW]: 'processing',
  [VEHICLE_PROCESS_STATE.REJECTED]: 'error',
  [VEHICLE_PROCESS_STATE.ACCEPTED]: 'success',
  [VEHICLE_PROCESS_STATE.CONFIRMED]: 'success',
}

/** 获取车辆业务流程状态颜色 */
export function getVehicleProcessStateColor(status: string | number): string {
  const statusStr = String(status);
  return VEHICLE_PROCESS_STATE_COLOR_MAP[statusStr] || 'default';
}

/** 获取车辆业务流程状态文本 */
export function getVehicleProcessStateText(status: string | number): string {
  const statusStr = String(status);
  return VEHICLE_PROCESS_STATE_MAP[statusStr] || '未知';
}

// -------------------------------- 用车申请相关 --------------------------------
/** 用车申请优先级 */
export enum VEHICLE_APPLICATION_PRIORITY {
  NORMAL = '1',
  URGENT = '2',
}

export const VEHICLE_APPLICATION_PRIORITY_MAP = {
  [VEHICLE_APPLICATION_PRIORITY.NORMAL]: '一般',
  [VEHICLE_APPLICATION_PRIORITY.URGENT]: '紧急',
}

export const VEHICLE_APPLICATION_PRIORITY_OPTIONS = [
  { fullName: '一般', id: '1' },
  { fullName: '紧急', id: '2' },
]

/** 用车申请状态 */
export enum VEHICLE_APPLICATION_STATUS {
  PENDING_REVIEW = '1',
  REVIEWED = '2',
  ASSIGNED = '3',
  IN_USE = '4',
  COMPLETED = '5',
  REJECTED = '6',
}

export const VEHICLE_APPLICATION_STATUS_MAP = {
  [VEHICLE_APPLICATION_STATUS.PENDING_REVIEW]: '待审核',
  [VEHICLE_APPLICATION_STATUS.REVIEWED]: '已审核',
  [VEHICLE_APPLICATION_STATUS.ASSIGNED]: '已分配',
  [VEHICLE_APPLICATION_STATUS.IN_USE]: '使用中',
  [VEHICLE_APPLICATION_STATUS.COMPLETED]: '已完成',
  [VEHICLE_APPLICATION_STATUS.REJECTED]: '已拒绝',
}

export const VEHICLE_APPLICATION_STATUS_OPTIONS = [
  { fullName: '待审核', id: '1' },
  { fullName: '已审核', id: '2' },
  { fullName: '已分配', id: '3' },
  { fullName: '使用中', id: '4' },
  { fullName: '已完成', id: '5' },
  { fullName: '已拒绝', id: '6' },
]

/** 获取用车申请优先级文本 */
export function getVehicleApplicationPriorityText(priority: string | number): string {
  const priorityStr = String(priority);
  return VEHICLE_APPLICATION_PRIORITY_MAP[priorityStr] || '未知';
}

/** 获取用车申请状态文本 */
export function getVehicleApplicationStatusText(status: string | number): string {
  const statusStr = String(status);
  return VEHICLE_APPLICATION_STATUS_MAP[statusStr] || '未知';
}

// -------------------------------- 尾项管理 --------------------------------
/** 下发状态 */
/** 编审批状态 */
export enum LAST_TERM_STATE {
  STAY_ALLOTS = "0",
  CONTACT_PERSON = "1",
  JOB_FOREMAN = "2",
  STAY_INSPECT = "3",
  STAY_AGREE = "4",
  ALREADY_AGREE = "5",
}

export const LAST_TERM_ALLOTS_STATE_MAP = {
  [LAST_TERM_STATE.STAY_ALLOTS]: '待下发',
  [LAST_TERM_STATE.CONTACT_PERSON]: '接口人',
  [LAST_TERM_STATE.JOB_FOREMAN]: '班组长',
  [LAST_TERM_STATE.STAY_INSPECT]: '待检验',
  [LAST_TERM_STATE.STAY_AGREE]: '待同意',
  [LAST_TERM_STATE.ALREADY_AGREE]: '已同意',
}

/** 是否确认 */
export const CONFIRM_OR_NOT  = [
  {fullName: '是', id: "1" },
  {fullName: '否', id: "0" },
]

/** asp1状态 */
export const ASP_1_STATE  = [
  {fullName: '处理', id: "处理" },
  {fullName: '关闭', id: "关闭" },
]

/** 实际完成情况 */
export const ACTUAL_COMPLETION  = [
  {fullName: '现场可执行', id: "现场可执行" },
  {fullName: '已关闭', id: "已关闭" },
  {fullName: '制约', id: "制约" },
]

/** 制约类型 */
export const CONSTRAINT_TYPE  = [
  {fullName: '/', id: "/" },
  {fullName: '逻辑', id: "逻辑" },
  {fullName: '无制约', id: "无制约" },
  {fullName: '已关闭', id: "已关闭" },
]

/** 工单状态 */
export const WORK_ORDER_STATE  = [
  {fullName: '/', id: "/" },
  {fullName: '逻辑', id: "逻辑" },
  {fullName: '无制约', id: "无制约" },
  {fullName: '已关闭', id: "已关闭" },
]

// -------------------------------通用---------------------------------

/* 日期类型 */
export enum DATE_TYPE {
  CURRENT_DAY,
  CURRENT_WEEK,
  CURRENT_MONTH,
  CURRENT_YEAR,
}

