// ---------------------------------------交底档案首页表头---------------------------------------------------
import {BasicColumn} from "/@/components/Table";

// `num` varchar(255) DEFAULT NULL  COMMENT '档案编号',
//   `deliver_class` varchar(255) DEFAULT NULL  COMMENT '交底分类',
//   `deliver_class_name` varchar(255) DEFAULT NULL  COMMENT '交底分类名称',
//
//   `department` varchar(255) DEFAULT NULL  COMMENT '部门',


export const deliverRecordCol: BasicColumn[] = [
  {title: '档案名称', dataIndex: 'name', width: 250},
  {title: '档案编号', dataIndex: 'num', width: 250},
  // {title: '交底分类', dataIndex: 'deliverClassName', width: 250},
  // {title: '部门', dataIndex: 'departmentName', width: 250},
  // {title: '是否需要签到', dataIndex: 'isNeedSign', width: 100,
  //   customRender: ({record}) => (record.isNeedSign === 1 ? '是' : '/')},
  // {
  //   title: '是否签署承诺书', dataIndex: 'isNeedSignCommit', width: 120,
  //   customRender: ({record}) => (record.isNeedSignCommit === 1 ? '是' : '/')
  // },
  // {
  //   title: '人脸识别频率(分钟)', dataIndex: 'recognizeFrequency', width: 120,
  //   customRender: ({record}) => (record.recognizeFrequency ? record.recognizeFrequency / 60 : '')
  // },
  // {title: '预计开始时间', dataIndex: 'planStartTime', width: 250, format: 'date|YYYY-MM-DD'},
  // {title: '预计结束时间', dataIndex: 'planEndTime', width: 250, format: 'date|YYYY-MM-DD'},
  {title: '审批状态', dataIndex: 'currentState', width: 100},
  {title: '当前节点', dataIndex: 'currentNodeName', width: 100},
  {title: '制单人员', dataIndex: 'creatorUser', width: 130},
  {title: '发起时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
]
