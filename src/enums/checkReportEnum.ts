import {BasicColumn, FormSchema} from "/@/components/Table";
import {formatToDate} from "/@/utils/dateUtil";
import {genDate, genInput, genSelect, genUser} from "/@/utils/formUtils";

export const CHECK_REPORT_COLUMNS: BasicColumn[] = [
  {title: '报告编号', dataIndex: 'num'},
  {title: '检查类型', dataIndex: 'type'},
  {title: '检查时间', dataIndex: 'checkDate'},
  {title: '检查地点', dataIndex: 'location'},
  {title: '检查人员', dataIndex: 'joinerNames'},
  {
    title: '创建时间', dataIndex: 'creatorTime',
    customRender: ({record}) => (formatToDate(record.creatorTime))
  },
  {title: '创建者', dataIndex: 'creatorUserId'},
];

export const CHECK_REPORT_SCHEMAS:FormSchema[]=[
  genInput('报告编号', 'num',false),
  genSelect('检查类型', 'type',false),
  genInput('检查目的', 'purpose',false),
  genInput('检查依据', 'basis',false),
  genDate('检查时间', 'checkDate',false),
  genInput('检查地点', 'location',false),
  {
    field: 'joiners',
    label: '检查人员',
    component: 'UserSelect',
    componentProps: {placeholder: `请输入检查人员`},
    // rules: [{message: `请输入培训老师`, type: 'string'}],
  },
  genUser( '检查人员','joiners',false),
  // genSelectMulti('隐患记录','issueRecords',false),
  genSelect('隐患记录','issueRecords',false)
]
