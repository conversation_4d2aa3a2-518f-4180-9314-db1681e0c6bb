import {BasicColumn, FormSchema} from "/@/components/Table";
import {testEduClassTreeApi} from "/@/api";
import {genCommon, genDept, genInput, genSelect} from "/@/utils/formUtils";
import {colKeys} from "/@/utils/file/table";
import {COMMON_OPTION} from "/@/enums/commonEnum";

// ---------------------------------------培训计划首页表头---------------------------------------------------
export const eduRecordColumn: BasicColumn[] = [
  {title: '培训名称', dataIndex: 'eduName', width: 250},
  {title: '培训编号', dataIndex: 'eduNum', width: 210},
  {title: '培训部门', dataIndex: 'organizeNames', width: 100},
  {title: '培训岗位', dataIndex: 'posName', width: 100},
  {title: '培训老师', dataIndex: 'eduTeacherName', width: 100},
  {title: '培训地点', dataIndex: 'eduLocation'},
  {title: '参与人数', dataIndex: 'eduParticipantsSum', width: 80},
  {
    title: '是否需要考试', dataIndex: 'isNeedTest', width: 100,
    customRender: ({record}) => (record.isNeedTest === 1 ? '是' : '/')
  },
  {
    title: '是否需要签到', dataIndex: 'isNeedSign', width: 100,
    customRender: ({record}) => (record.isNeedSign === 1 ? '是' : '/')
  },
  {
    title: '是否签署承诺书', dataIndex: 'isNeedSignCommit', width: 120,
    customRender: ({record}) => (record.isNeedSignCommit === 1 ? '是' : '/')
  },
  // {
  //   title: '人脸识别频率(分钟)', dataIndex: 'recognizeFrequency', width: 120,
  //   customRender: ({record}) => (record.recognizeFrequency ? record.recognizeFrequency / 60 : '')
  // },
  {title: '预计开始时间', dataIndex: 'planStartTime', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '预计结束时间', dataIndex: 'planEndTime', width: 100, format: 'date|YYYY-MM-DD'},
  // {title: '实际开始时间', dataIndex: 'actStartTime', width: 100, format: 'date|YYYY-MM-DD'},
  // {title: '实际结束时间', dataIndex: 'actEndTime', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '创建日期', dataIndex: 'creatorTime', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '创建者', dataIndex: 'creatorUserName', width: 100},
  {
    title: '状态', dataIndex: 'status', width: 100,
    customRender: ({record}) => (record.status == 1 ? '已完成' : '未完成')
  },
];

// ---------------------------------------培训计划合集首页表头---------------------------------------------------
export const eduPackCols: BasicColumn[] = [
  {title: '培训名称', dataIndex: 'packName', width: 210},
  {title: '培训编号', dataIndex: 'packNum', width: 210},
  // {title: '培训组织', dataIndex: 'organizeName'},
  // {title: '培训老师', dataIndex: 'eduTeacherName', width: 100},
  // {title: '培训地点', dataIndex: 'eduLocation'},
  {title: '参与人数', dataIndex: 'eduParticipantsSum', width: 80},
  {
    title: '顺序学习', dataIndex: 'isByOrder', width: 100,
    customRender: ({record}) => (record.isNeedTest === 0 ? '/' : '是')
  },
  {title: '预计开始时间', dataIndex: 'planStartTime', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '预计结束时间', dataIndex: 'planEndTime', width: 100, format: 'date|YYYY-MM-DD'},
  // {title: '实际开始时间', dataIndex: 'actStartTime', width: 100, format: 'date|YYYY-MM-DD'},
  // {title: '实际结束时间', dataIndex: 'actEndTime', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '创建日期', dataIndex: 'creatorTime', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '创建者', dataIndex: 'creatorUserName', width: 100},
  {
    title: '状态', dataIndex: 'status', width: 100,
    customRender: ({record}) => (record.isNeedSign == '0' ? '未完成' : '已完成')
  },
];


// ---------------------------------------培训计划编辑项---------------------------------------------------
export const eduSchemas: FormSchema[] = [
  {
    field: 'eduClassId',
    label: '培训分类',
    component: 'FaCascader',
    componentProps: {placeholder: '请选择培训分类', api: testEduClassTreeApi, showRoot: false},
    rules: [{required: true, trigger: 'change', message: '培训分类不能为空'}],
  },
  genInput('培训计划编号', 'eduNum'),
  genSelect('培训部门', 'organizeIds'),
  genInput('项目部名', 'projName',false),
  {
    field: 'posId',
    label: '培训岗位',
    component: 'PosSelect',
    componentProps: {placeholder: '请选择培训岗位',multiple: true },
    rules: [{required: false, type: "array",trigger: 'change', message: '培训岗位不能为空'}],
  },
  genInput('培训计划名称', 'eduName'),
  {
    field: 'eduTeacherId',
    label: '培训老师',
    component: 'UserSelect',
    componentProps: {placeholder: `请输入培训老师`},
    // rules: [{message: `请输入培训老师`, type: 'string'}],
  },
  genInput('培训地点', 'eduLocation', false),
  genSelect('是否需要考试', 'isNeedTest', true, 'number', COMMON_OPTION),
  genSelect('考试是否需要人脸识别', 'isNeedFaceCheck', true, 'number', COMMON_OPTION),
  genSelect('是否需要签到', 'isNeedSign', true, 'number', COMMON_OPTION),
  genSelect('是否签署承诺书', 'isNeedSignCommit', true, 'number', COMMON_OPTION),
  genSelect('是否签署评估表', 'isNeedEvaluate', true, 'number', COMMON_OPTION),
  genInput('受训人员', 'eduPeople', false),
  genInput('培训内容', 'eduContent', false),
  genInput('培训目的', 'eduPurpose', false),
  genCommon('预计开始时间', 'planStartTime', 'DatePicker',false),
  genCommon('预计结束时间', 'planEndTime', 'DatePicker',false),
  genSelect('是否生成证件', 'isNeedGenerate', true, 'number', COMMON_OPTION),
  {
    ...genSelect('证件类型', 'certificateType', false, 'string'),
    show: ({ values }) => values.isNeedGenerate === 1,
  },
  {
    ...genSelect('定期自动下发', 'isAutoAllot', false, 'number', COMMON_OPTION),
    show: ({ values }) => values.isNeedGenerate === 1,
    helpMessage:'每月一日自动下发'
  },
  genInput('人脸识别频率(分钟)', 'recognizeFrequency', false),
];


// ---------------------------------------培训计划合集编辑项---------------------------------------------------
export const eduPackSchemas: FormSchema[] = [
  {
    field: 'eduClassId',
    label: '培训分类',
    component: 'FaCascader',
    componentProps: {placeholder: '请选择培训分类', api: testEduClassTreeApi, showRoot: false},
    rules: [{required: true, trigger: 'change', message: '培训分类不能为空'}],
  },
  genSelect('培训部门', 'organizeIds',false),
  {
    field: 'posIds',
    label: '培训岗位',
    component: 'PosSelect',
    componentProps: {placeholder: '请选择培训岗位', multiple: true},
    rules: [{trigger: 'change', message: '培训岗位不能为空'}],
  },
  genInput('计划合集编号', 'packNum'),
  genInput('计划合集名称', 'packName'),
  genSelect('是否按顺序学习', 'isByOrder', true, 'string', [
    {"fullName": "不要", "id": "0"},
    {"fullName": "需要", "id": "1"},
  ]),
  genCommon('预计开始时间', 'planStartTime', 'DatePicker', false),
  genCommon('预计结束时间', 'planEndTime', 'DatePicker', false),
];



//-----------------------------培训进度首页表头------------------------------------
export const eduProCol: BasicColumn[] = [
  // {title: '培训名称', dataIndex: 'selectTrainingName'},
  {title: '培训分类', dataIndex: 'approvalNum'},
  {title: '培训分类名称', dataIndex: 'eduClassName'},
  {title: '培训计划编号', dataIndex: 'selectTrainingNum'},
  {title: '培训计划名称', dataIndex: 'selectTrainingName'},
  {title: '总人数', dataIndex: 'totalUserNum'},
  {title: '视频学习已完成人数', dataIndex: 'eduFinishUserNum'},
  {title: '视频学习未完成人数', dataIndex: 'eduUnfinishUserNum'},
  {title: '考试通过人数', dataIndex: 'examPassUserNum'},
  {title: '考试未通过人数', dataIndex: 'examUnpassUserNum'},
];


//-----------------------------培训进度详情表头------------------------------------
export const proDetailsCol:colKeys[] = [
  {
    title: '用户姓名',
    dataIndex: 'userName', // 数据中的键名
    key: 'userName',
  },
  {
    title: '手机号',
    dataIndex: 'mobilePhone', // 数据中的键名
    key: 'mobilePhone',
  },
  // {
  //   title: '电话',
  //   dataIndex: 'telePhone', // 数据中的键名
  //   key: 'telePhone',
  // },
  {
    title: '身份证',
    dataIndex: 'identificationNumber', // 数据中的键名
    key: 'identificationNumber',
  },
  {
    title: '部门名称',
    dataIndex: 'organizeName', // 数据中的键名
    key: 'organizeName',
  },
  {
    title: '文档是否学完',
    dataIndex: 'fileStudyFinishExcel', // 数据中的键名
    key: 'fileStudyFinishExcel',
  },
  {
    title: '观看时间',
    dataIndex: 'formatTotalWatchedTime', // 数据中的键名
    key: 'formatTotalWatchedTime',
  },
  {
    title: '视频总时长',
    dataIndex: 'formatTotalDuration', // 数据中的键名
    key: 'formatTotalDuration',
  },
  {
    title: '视频进度',
    dataIndex: 'eduProcess', // 数据中的键名
    key: 'eduProcess',
  },
  {
    title: '考试成绩',
    dataIndex: 'score', // 数据中的键名
    key: 'score',
  },
  {
    title: '是否及格',
    dataIndex: 'isPassExcel', // 数据中的键名
    key: 'isPassExcel',
  },
]

//-----------------------------培训课时进度导出表表头-----------------------------------------------------
export const exportProCol:colKeys[] = [
  {
    title: '姓名',
    dataIndex: 'userName', // 数据中的键名
    key: 'userName',
  },
  {
    title: '手机号',
    dataIndex: 'mobilePhone', // 数据中的键名
    key: 'mobilePhone',
  },
  // {
  //   title: '电话',
  //   dataIndex: 'telePhone', // 数据中的键名
  //   key: 'telePhone',
  // },
  {
    title: '身份证',
    dataIndex: 'identificationNumber', // 数据中的键名
    key: 'identificationNumber',
  },
  {
    title: '课程标题',
    dataIndex: 'className', // 数据中的键名
    key: 'className',
  },
  {
    title: '课时标题',
    dataIndex: 'videoName', // 数据中的键名
    key: 'videoName',
  },
  {
    title: '课程开始时间',
    dataIndex: 'formatStartTime', // 数据中的键名
    key: 'formatStartTime',
  },
  {
    title: '课程结束时间',
    dataIndex: 'formatFinishTime', // 数据中的键名
    key: 'formatFinishTime',
  },
  {
    title: '课时时长',
    dataIndex: 'formatVideoDuration', // 数据中的键名
    key: 'formatVideoDuration',
  },
  {
    title: '学习时长',
    dataIndex: 'formatVideoWatchedTime', // 数据中的键名
    key: 'formatVideoWatchedTime',
  },
  {
    title: '完成百分比',
    dataIndex: 'videoEduProcess', // 数据中的键名
    key: 'videoEduProcess',
  },
  {
    title: '组织部门',
    dataIndex: 'organizeName', // 数据中的键名
    key: 'organizeName',
  }

]

//-----------------------------培训进度枚举------------------------------------
export enum processType {
  //视频学习已完成人数
  eduFinishUserNum = 'eduFinishUserNum',
  //视频学习未完成人数
  eduUnfinishUserNum = 'eduUnfinishUserNum',
  //考试通过人数
  examPassUserNum = 'examPassUserNum',
  //考试未通过人数
  examUnpassUserNum = 'examUnpassUserNum',
}

export function isProcessType(value: string): value is processType {
  return Object.values(processType).includes(value as processType);
}

//----------------------------培训课件编辑项----------------------------------------------------
export const eduFileSchemas: FormSchema[] = [
  {
    field: 'eduTypeId',
    label: '培训分类',
    component: 'FaCascader',
    componentProps: {placeholder: '请选择培训分类', api: testEduClassTreeApi, showRoot: true},
    rules: [{required: true, trigger: 'change', message: '培训分类不能为空'}],
  },
  genInput('资料编号', 'fileNum'),
  genInput('资料名称', 'fileName'),
  // genSelect('所属部门', 'departmentId', false),
  genDept('所属部门', 'departmentId', false),
  genInput('材料分类(自定义)', 'fileClass',false,false),
  {
    field: 'fileLink',
    label: '文件上传',
    component: 'UploadFileQiniu',
    componentProps: {placeholder: ''},
    helpMessage:'文件大小超过500M，可能影响app端正常播放，建议压缩后上传',
  },
  {
    field: 'fileScreenshotLink',
    label: '视频上传',
    component: 'UploadFileQiniu',
    componentProps: {placeholder: ''},
    helpMessage:'文件大小超过500M，可能影响app端正常播放，建议压缩后上传',
  },
];
