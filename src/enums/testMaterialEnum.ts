import {BasicColumn} from "/@/components/Table";

//----------------------------一人一档首页表头------------------------------------------------------
export const userInfoCol:BasicColumn[] = [
  {title: '劳动关系所在单位', dataIndex: 'sourceUnit',width: 200},
  {title: '姓名', dataIndex: 'realName', width: 100},
  {title: '性别', dataIndex: 'gender', width: 70,
    customRender: ({record}) => (record.gender === 1 ? '男' : record.gender === 2 ? '女' : '未知')},
  {title: '部门', dataIndex: 'organizeFullName', width: 250},
  {title: '岗位', dataIndex: 'positionName',width: 200},
  // {title: '身份证号', dataIndex: 'identificationNumber', width: 180},
  {title: '联系电话', dataIndex: 'mobilePhone', width: 120},
]


//---------------------------------试卷展示首页表头------------------------------------------------------------
export const examCol:BasicColumn[] = [
  {title: '劳动关系所在单位', dataIndex: 'sourceUnit',},
  {title: '姓名', dataIndex: 'realName', width: 100},
  {title: '性别', dataIndex: 'gender', width: 70},
  {title: '部门', dataIndex: 'organizeName', width: 200},
  {title: '岗位', dataIndex: 'positionName',},
  {title: '身份证号', dataIndex: 'identificationNumber', width: 180},
  {title: '联系电话', dataIndex: 'mobilePhone', width: 120},
  // {title: '培训时间', dataIndex: 'eduTime', },
  {title: '培训分类', dataIndex: 'eduTypeName',},
  {title: '培训名称', dataIndex: 'eduName',},
  {title: '培训编号', dataIndex: 'eduNum',},
  // {title: '试卷编号', dataIndex: 'paperNum',},
  {title: '分数', dataIndex: 'score', width: 70},
  {title: '考试次数', dataIndex: 'testTimes', width: 80},
  // {title: '开始时间', dataIndex: 'testStartTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
  // {title: '结束时间', dataIndex: 'testEndTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
  {title: '考试时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
];

//---------------------------------评估表展示首页表头------------------------------------------------------------
export const evaluTabCols: BasicColumn[] = [
  {title: '劳动关系所在单位', dataIndex: 'sourceUnit',},
  {title: '姓名', dataIndex: 'realName', width: 100},
  {title: '性别', dataIndex: 'gender', width: 70},
  {title: '部门', dataIndex: 'organizeName', width: 200},
  {title: '岗位', dataIndex: 'roleName',},
  {title: '身份证号', dataIndex: 'identificationNumber', width: 180},
  {title: '联系电话', dataIndex: 'mobilePhone', width: 120},
  // {title: '培训时间', dataIndex: 'eduTime', },
  {title: '培训分类', dataIndex: 'eduTypeName',},
  {title: '培训名称', dataIndex: 'eduName',},
  {title: '培训编号', dataIndex: 'eduNum',},
  // {title: '试卷编号', dataIndex: 'paperNum',},
  // {title: '分数', dataIndex: 'score', width: 70},
  // {title: '考试次数', dataIndex: 'testTimes', width: 80},
  // {title: '考试时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
];
