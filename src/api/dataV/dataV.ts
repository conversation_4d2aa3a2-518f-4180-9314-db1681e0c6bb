import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {
  getOrgBarData = (orgCode) => this.get(`getOrgBarData/${orgCode}`)
  getEmployPieChartData = (orgCode) => this.get(`getEmployPieChartData/${orgCode}`)
  getInFacChartData = (orgCode) => this.get(`getInFacChartData/${orgCode}`)
  getCenterData = (orgCode) => this.get(`getCenterData/${orgCode}`)
}

export default new Api('/api/blade-visual', 'visualData');
