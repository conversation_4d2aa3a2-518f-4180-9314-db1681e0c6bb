import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineCounterfeit',
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'   + id });
}

// 上传核查单信息预览
export function importCounterfeitPreview(fileId) {
  return defHttp.get({ url: Api.Prefix + `/importCounterfeitPreview/${fileId}` });
}

// 下载模板
export function templateDownload() {
  return defHttp.get({ url: Api.Prefix + '/templateDownload'});
}

// 获取核查单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取核查单子表
export function getCounterfeitList(id) {
  return defHttp.get({ url: Api.Prefix + `/counterfeit/${id}/items` });
}

// 获取台账列表
export function getLedgerList() {
  return defHttp.get({ url: Api.Prefix + `/getLedgerList` });
}

// 分页
export function page(data) {
  return defHttp.post({ url: '/api/example/machineLedger/machineLedgerPage', data });
}

// 在用单
export function inUsePage(data) {
  return defHttp.post({ url: '/api/example/machineLedger/inUseMachineLedgerPage', data });
}

// 在用单
export function effective(data) {
  return defHttp.post({ url: '/api/example/machineLedger/effectiveLedgerPage', data });
}

// 删除入场检查及防造假核查表
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}