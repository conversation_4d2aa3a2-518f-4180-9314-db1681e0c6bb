import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineLedger',
}

// 分页
export function page(data) {
  return defHttp.post({ url: Api.Prefix + '/machineLedgerPage', data });
}

// 删除
export function remove(id) {
  return defHttp.delete({ url: Api.Prefix + '/remove/' + id });
}

// 批量删除
export function batchRemove(data) {
  return defHttp.post({ url: Api.Prefix + '/removeBatchByIds', data });
}

// 查询
export function getById(id) {
  return defHttp.get({ url: Api.Prefix + '/getById/' + id });
}

// 批量查询
export function getByIds(ids) {
  return defHttp.post({ url: Api.Prefix + '/getByIds/', data: ids });
}

// 查询全部
export function all() {
  return defHttp.get({ url: Api.Prefix + '/all' });
}

// 新建
export function save(data) {
  return defHttp.post({ url: Api.Prefix + '/save', data });
}

// 修改
export function update(data) {
  return defHttp.post({ url: Api.Prefix + '/update', data });
}

// 导入
export function importData(data) {
  return defHttp.post({ url: Api.Prefix + `/ImportData`, data });
}

// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: Api.Prefix + `/ImportPreview`, data });
}

// 导出Excel
export function exportExcel(data) {
  return defHttp.requestDownload({ url: Api.Prefix + `/ExportExcelData`, data });
}

// 获取文件
export function getFile(data) {
  return defHttp.get({ url: Api.Prefix + `/getFile/` , data });
}

// 上传文件
export function uploadSubmitFile(data) {
  return defHttp.post({ url: Api.Prefix + `/uploadSubmitFile`, data });
}