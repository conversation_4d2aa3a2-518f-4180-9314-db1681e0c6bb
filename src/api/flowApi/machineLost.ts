import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineLost',
}

// 获取损失遗失单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取损失遗失单子表
export function getLostList(id) {
  return defHttp.get({ url: Api.Prefix + `/lost/${id}/items` });
}

// 删除损失遗失单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}

// 上传损失遗失单
export function importLostPreview(fileId) {
  return defHttp.get({ url: Api.Prefix + `/importLostPreview/${fileId}` });
}

// 下载模板
export function templateDownload() {
  return defHttp.get({ url: Api.Prefix + '/templateDownload'});
}
