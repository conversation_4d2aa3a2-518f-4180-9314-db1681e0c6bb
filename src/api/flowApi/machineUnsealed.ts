import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineUnsealed',
}

// 获取报废单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取报废单子表
export function getUnsealedList(id) {
  return defHttp.get({ url: Api.Prefix + `/unsealed/${id}/items` });
}

// 删除报废单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}