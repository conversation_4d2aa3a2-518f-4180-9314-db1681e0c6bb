import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineInspection',
}

// 获取退库单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取退库单子表
export function getInspectionList(id) {
  return defHttp.get({ url: Api.Prefix + `/inspection/${id}/items` });
}

// 删除退库单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}

// 获取专职计量人员
export function getMeteringList(id) {
  return defHttp.get({ url: Api.Prefix + `/metering/${id}` });
}