import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineReceive',
}

// 获取领用流转单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取领用流转单子表
export function getReceiveList(id) {
  return defHttp.get({ url: Api.Prefix + `/receive/${id}/items` });
}

// 删除领用流转单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}

// 上传领用流转单
export function importReceivePreview(fileId) {
  return defHttp.get({ url: Api.Prefix + `/importReceivePreview/${fileId}` });
}

// 下载模板
export function templateDownload() {
  return defHttp.get({ url: Api.Prefix + '/templateDownload'});
}