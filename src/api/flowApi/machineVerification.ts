import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineVerification',
}

// 获取检定单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取检定单子表
export function getVerificationList(id) {
  return defHttp.get({ url: Api.Prefix + `/verification/${id}/items` });
}

// 删除检定单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}