import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineForewarn',
}

// 获取预警单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取预警单子表
export function getForewarnList(id) {
  return defHttp.get({ url: Api.Prefix + `/forewarn/${id}/items` });
}

// 删除预警单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}