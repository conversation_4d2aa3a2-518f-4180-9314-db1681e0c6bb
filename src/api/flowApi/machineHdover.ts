import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/machineHdover',
}

// 获取转接单列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取转接单子表
export function getHdoverList(id) {
  return defHttp.get({ url: Api.Prefix + `/hdover/${id}/items` });
}

// 删除转接单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}