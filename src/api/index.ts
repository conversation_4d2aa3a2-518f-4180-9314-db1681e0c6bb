// ----------------------- basic -----------------------
export { default as fileSave<PERSON>pi } from './basic/fileSave'
export { default as zzUserSignFaceApi } from './extend/zzUserSignFace'

// ----------------------- demo -----------------------
export { default as msgTestApi } from './extend/msgTest'
export { default as zzDemoTreeApi } from './extend/zzDemoTree'
export { default as zzDemoTreeTableApi } from './extend/zzDemoTreeTable'

// ----------------------- 用户组织 -----------------------
export { default as userMasterApi } from './example/userMaster'
export { default as BaseUserInfoRegistryApi } from './example/BaseUserInfoRegistryApi'
export { default as organizeTree<PERSON>pi } from './permission/organizeTree'
export { default as positionApi } from './permission/positionBiz'

// ----------------------- 用户资政 -----------------------
export { default as zzUserCertificateApi } from './extend/cert/zzUserCertificate'

// ----------------------- 工程影像 -----------------------
export { default as zzProjectContractSubApi } from './extend/zzProjectContractSub'
export { default as zzProjectMediaApi } from './extend/zzProjectMedia'
export { default as zzProjectMediaPlanApi } from './extend/zzProjectMediaPlan'


// ----------------------- 扫码签到 -----------------------
export { default as zzQrCodeTableApi } from './extend/zzQrCodeTable'

// ----------------------- 行动项 -----------------------
export { default as zzActItemApi } from './extend/act/zzActItem'
export { default as zzActItemHisApi } from './extend/act/zzActItemHis'
export { default as zzActItemFlowPostponeApi } from './extend/act/zzActItemFlowPostpone'
export { default as zzActItemFlowCloseApi } from './extend/act/zzActItemFlowClose'
export { default as zzActItemAlertPunishApi } from './extend/act/zzActItemAlertPunish'
export { default as zzActItemCountDayApi } from './extend/act/zzActItemCountDay'

// ----------------------- 施工协调 -----------------------
export { default as zzWorkCoordinationApi } from './extend/zzWorkCoordination'


// ----------------------- 脚手架 -----------------------
export { default as zzScaffoldRemoveApi } from './extend/zzScaffoldRemove'
export { default as zzScaffoldApi } from './extend/zzScaffold'
export { default as zzScaffoldWarningApi } from './extend/zzScaffoldWarning'
export { default as zzScaffoldChangeApi } from './extend/zzScaffoldChange'

// ----------------------- 正式证、临时证 -----------------------
export { default as zzCardApi } from './extend/zzCard/zzCard'
export { default as zzCardTemporaryApi } from './extend/zzCard/zzCardTemporary'


// ----------------------- 学习培训 -----------------------
export { default as testEduClassTreeApi } from './testManage/testEduClassTree'
export { default as testEduFileApi } from './testManage/testEduFile'
export { default as testEduFileDocApi } from './testManage/testEduFileDoc'
export { default as testEduQuestionApi } from './testManage/testEduQuestion'
export { default as testEduRecordApi } from './testManage/testEduRecord'
export { default as testEduMorningMeetingApi } from './testManage/testEduMorningMeeting'
export { default as testUserEduRecordApi } from './testManage/testUserEduRecord'
export { default as testEduThreeCardRecordApi } from './testManage/testEduThreeCardRecord'
export { default as testUserEduRecordVideoApi } from './testManage/testUserEduRecordVideo'
export { default as testUserEduRecordVideoWatchApi } from './testManage/testUserEduRecordVideoWatch'
export { default as testUserEduRecordDocApi } from './testManage/testUserEduRecordDoc'
export { default as testEduRecordInConfigApi } from './testManage/testEduRecordInConfig'
export { default as testEduRecordInOrgPermissApi } from './testManage/testEduRecordInOrgPermiss'
export { default as testEduRecordPackApi } from './testManage/testEduRecordPack'
export { default as testEduRecordPackGroupApi } from './testManage/testEduRecordPackGroup'
export { default as eduPackRecordRelaApi } from './testManage/testEduPackRecordRelation'
export { default as testExamRecordApi } from './testManage/testExamRecord'
export { default as testUserExamFaceCheckApi } from './testManage/testUserExamFaceCheck'
export { default as testGradeApi } from './testManage/testGrade'
export { default as testPaperApi } from './testManage/testPaper'
export { default as testQuestionApi } from './testManage/testQuestion'
export { default as testPaperQuestionApi } from './testManage/testPaperQuestion'
export { default as eduDataCalApi } from './testManage/eduDataCal'

// ----------------------- 培训材料 -----------------------
export { default as testSignApi } from './testManage/testMaterial/testSign'
export { default as testScoreTableApi } from './testManage/testMaterial/testScoreTable'
export { default as testEduCoverApi } from './testManage/testMaterial/testEduCover'
export { default as testUserEvaluationApi } from './testManage/testMaterial/testUserEvaluationTable'
export { default as personDocApi } from './testManage/testMaterial/personDoc'
export { default as threeLevelCardApi } from './testManage/testMaterial/testUserThreeLevelCard'

// ----------------------- 培训审批 -----------------------
export { default as studySpecialApprovalApi } from './study/studySpecialApproval'
export { default as studyPackApi } from './study/studyPackApproval'
export { default as eduProcessApi } from './study/eduProcess'
export { default as eduPackProcessApi } from './study/eduPackProcess'

// ----------------------- 安全交底 -----------------------
export { default as deliverCardApi } from './extend/safeDeliver/deliverCard'
export { default as deliverRecordApi } from './extend/safeDeliver/deliverRecord'
export { default as userDeliverSignApi } from './extend/safeDeliver/userDeliverSign'
export { default as deliverCoverInfoApi } from './extend/safeDeliver/deliverCoverInfo'
export { default as onePageInfoApi } from './extend/safeDeliver/onePageInfo'
export { default as constructRecordInfoApi } from './extend/safeDeliver/constructRecordInfo'

// ----------------------- 隐患治理台账 -----------------------
export { default as issueHandleRecordApi } from './extend/issueHandleRecord'
export { default as issueHandleRecordPicApi } from './extend/issueHandleRecordPic'
export { default as issueAreaApi } from './extend/issueArea'

// ----------------------- 专项检查 -----------------------
export { default as specCheckRecordApi } from './extend/specCheckRecord'

// ----------------------- 检查报告 -----------------------
export { default as checkRecordApi } from './extend/checkRecord'
export { default as checkReportApi } from './extend/checkReport'
export { default as checkReportRecordRelationApi } from './extend/checkReportRecordRelation'


// ----------------------- 资质证书 -----------------------
export { default as certificateTypeApi } from './extend/certificateType'
export { default as certificateManageApi } from './extend/certificateManage'
export { default as certificateManageGradeApi } from './extend/certificateManageGrade'

// ----------------------- 特种作业证件 -----------------------
export { default as specialWorkTypeApi } from './extend/specialWorkType'
export { default as specialWorkApi } from './extend/specialWork'
export { default as specialWorkCertificateApi } from './extend/specialWorkCertificate'

// ----------------------- 系统数据 -----------------------

//数据字典
export { default as dicDataApi } from './systemData/dicData'

// ----------------------- 尾项管理 -----------------------
export { default as lastTermManageApi } from './extend/lastTerm'

// ----------------------- 入场申请 -----------------------
export { default as studyPerDataApi } from './study/studyPerData'
export { default as studyAdmissionApi } from './study/studyAdmission'

// ----------------------- 调薪调岗 -----------------------
export { default as salaryAndPostApi } from './extend/salaryAndPost'

// ----------------------- 班组授权 -----------------------
export { default as zzTeamAuthApi } from './extend/team/zzTeamAuth'

export { default as zzTeamExitApi } from './extend/team/zzTeamAuthExit'
export { default as zzTeamRegisterApi } from './extend/team/zzTeamRegister'

// ----------------------- 定岗调资 -----------------------
export { default as settleWageApi } from './extend/settleWage'

// ----------------------- 农民工实名制 -----------------------
export { default as peasantryInfoApi } from './extend/peasantryInfo'

// ----------------------- 工机具 -----------------------
export { default as expireLedgerApi } from './flowApi/machineExpireLedger'

// ----------------------- 黑名单 -----------------------
export { default as blackListApi } from './extend/blackList'

//--------------------------可视化大屏--------------------------
export { default as dataVApi } from './dataV/dataV'

//-------------------------- OA --------------------------
//-------------------------- OA-QrCode --------------------------
export { default as oaQrItemApi } from './oa/qr/oaQrItem'
export { default as oaFixedQrApi } from './oa/qr/oaFixedQr'
export { default as oaQrItemUserScanApi } from './oa/qr/oaQrItemUserScan'

export { default as userDocumentApi } from './permission/userDocument'

//-------------------------- IOT --------------------------
export { default as videoApi } from './iot/video'
export { default as waterlsApi } from './iot/waterls'
export { default as waterlsAlarmApi } from './iot/waterlsAlarm'

//-------------------------- 临时措施 --------------------------
export { default as interimMeasuresApi } from './extend/interimMeasures/interimMeasures'
export { default as interimMeasuresRemoveApi } from './extend/interimMeasures/interimMeasuresRemove'
export { default as interimMeasuresItemApi } from './extend/interimMeasures/interimMeasuresItem'


//-------------------------- 设备管理 --------------------------
export { default as deviceLedgerApi } from './device/deviceLedger'
export { default as deviceArchiveApi } from './device/deviceArchive'
export { default as deviceInspectionApi } from './device/deviceInspection'
export { default as deviceInspectionItemApi } from './device/deviceInspectionItem'
export { default as deviceInspectionItemTemplateApi } from './device/deviceInspectionItemTemplate'
export { default as deviceInspectionTemplateApi } from './device/deviceInspectionTemplate'
export { default as deviceRepairRecordApi } from './device/deviceRepairRecord'

//-------------------------- 车辆管理 --------------------------
export { default as vehicleInfoApi } from './vehicle/vehicleInfo'
export { default as vehicleApplicationApi } from './vehicle/vehicleApplication'


//-------------------------- 临时用电 --------------------------
export { default as odesignPlanApi } from './tpsr/odesignPlan'
export { default as distributionBoxAcceptanceApi } from './tpsr/distributionBoxAcceptance'
export { default as lightingAcceptanceApi } from './tpsr/lightingAcceptance'
export { default as distributionBoxCoverInfoApi } from './tpsr/distributionBoxCoverInfo'
