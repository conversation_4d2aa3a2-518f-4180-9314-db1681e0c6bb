import BaseApi from '/@/api/base/BaseApi';

/**
 * 人员信息更新登记表实体接口
 */
export interface BaseUserInfoRegistryEntity {
  /** 主键ID */
  id?: string;
  /** 身份证号 */
  identificationNumber?: string;
  /** 原始用户ID */
  originalUserId?: string;
  /** 姓名 */
  realName?: string;
  /** 年龄 */
  age?: number;
  /** 性别 1-男 2-女 */
  gender?: number;
  /** 联系方式 */
  mobilePhone?: string;
  /** 紧急联系人 */
  emergencyContacts?: string;
  /** 紧急联系人电话 */
  emergencyContactsPhone?: string;
  /** 籍贯 */
  nativePlace?: string;
  /** 民族 */
  nation?: string;
  /** 家庭住址 */
  homeAddress?: string;
  /** 政治面貌 */
  politicalOutlook?: string;
  /** 学历 */
  education?: string;
  /** 所学专业 */
  specialty?: string;
  /** 毕业学校 */
  graduationSchool?: string;
  /** 毕业时间 */
  graduationTime?: string;
  /** 首次参加工作时间 */
  joinWorkTime?: string;
  /** 本工种工作年限 */
  seniority?: number;
  /** 婚姻状况 */
  maritalStatus?: string;
  /** 部门 */
  organizeName?: string;
  /** 岗位 */
  positionName?: string;
  /** 岗位序列 */
  positionSequence?: string;
  /** 人员类别 */
  categoryPersonnel?: string;
  /** 用工方式 */
  formEmployment?: string;
  /** 来源单位 */
  sourceUnit?: string;
  /** 进项目时间 */
  goProjectTime?: string;
  /** 备注 */
  remark?: string;
  /** 状态 0-待审核 1-已审核通过 2-已拒绝 3-已应用 */
  status?: number;
  /** 审核人ID */
  reviewerId?: string;
  /** 审核人姓名 */
  reviewerName?: string;
  /** 审核时间 */
  reviewTime?: string;
  /** 审核意见 */
  reviewComment?: string;
  /** 创建时间 */
  creatorTime?: string;
  /** 创建人ID */
  creatorUserId?: string;
  /** 最后修改时间 */
  lastModifyTime?: string;
  /** 最后修改人ID */
  lastModifyUserId?: string;
  /** 删除标记 */
  deleteMark?: number;
  /** 删除时间 */
  deleteTime?: string;
  /** 删除人ID */
  deleteUserId?: string;
}

/**
 * 审核请求参数接口
 */
export interface ReviewRequest {
  /** 记录ID */
  id: string;
  /** 审核状态 1-通过 2-拒绝 */
  status: number;
  /** 审核意见 */
  reviewComment?: string;
}

/**
 * 分页查询参数接口
 */
export interface BaseUserInfoRegistryPageParams {
  /** 当前页码 */
  currentPage?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 身份证号 */
  identificationNumber?: string;
  /** 姓名 */
  realName?: string;
  /** 部门 */
  organizeName?: string;
  /** 状态 */
  status?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

/**
 * 人员信息更新登记表API类
 */
 class Api extends BaseApi {

  /**
   * 审核通过
   * @param params 审核参数
   */
  approve = (params: ReviewRequest): Promise<any> => 
    this.post('approve', params);

  /**
   * 审核拒绝
   * @param params 审核参数
   */
  reject = (params: ReviewRequest): Promise<any> => 
    this.post('reject', params);

  /**
   * 应用到用户表
   * @param id 记录ID
   */
  applyToUser = (id: string): Promise<any> => 
    this.post(`applyToUser/${id}`, {});

  /**
   * 批量审核通过
   * @param ids 记录ID数组
   * @param reviewComment 审核意见
   */
  batchApprove = (ids: string[], reviewComment?: string): Promise<any> => 
    this.post('batchApprove', { ids, reviewComment });

  /**
   * 批量审核拒绝
   * @param ids 记录ID数组
   * @param reviewComment 审核意见
   */
  batchReject = (ids: string[], reviewComment?: string): Promise<any> => 
    this.post('batchReject', { ids, reviewComment });

  /**
   * 根据身份证号查询
   * @param identificationNumber 身份证号
   */
  getByIdentificationNumber = (identificationNumber: string): Promise<BaseUserInfoRegistryEntity> => 
    this.get(`getByIdentificationNumber/${identificationNumber}`);

  /**
   * 获取待审核数量
   */
  getPendingCount = (): Promise<number> => 
    this.get('getPendingCount');

  /**
   * 获取统计信息
   */
  getStatistics = (): Promise<any> => 
    this.get('getStatistics');
}

// 导出API实例
export default new Api('/api/base', 'baseUserInfoRegistry');
