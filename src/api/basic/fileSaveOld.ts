import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/base/admin/fileSave',
}

export const API_UPLOAD = Api.Prefix + '/upload'
export const API_UPLOAD_FORM_URL = Api.Prefix + '/uploadFromUrl'

// 文件分片获取
export function getFile(fileId) {
  return Api.Prefix + `/getFile/${fileId}`;
}

// 图片缩略图
export function getFilePreview(fileId) {
  return Api.Prefix + `/getFilePreview/${fileId}`;
}

// 解析七牛云URL，保存本地数据
export function uploadFile(file: any, callback?: (progressEvent: any) => void) {
  return defHttp.postFile({
    url: Api.Prefix + '/upload',
    onUploadProgress: callback,
  }, file);
}

// 解析七牛云URL，保存本地数据
export function uploadFromUrlQiniu(url) {
  return defHttp.post({ url: Api.Prefix + '/uploadFromUrlQiniu', data: { url } });
}

// 分页
export function page(data) {
  return defHttp.post({ url: Api.Prefix + '/page', data });
}

// 删除
export function remove(id) {
  return defHttp.delete({ url: Api.Prefix + '/remove/' + id });
}

// 查询
export function getById(id) {
  return defHttp.get({ url: Api.Prefix + '/getById/' + id });
}

// 批量查询
export function getByIds(ids) {
  return defHttp.post({ url: Api.Prefix + '/getByIds', data: ids });
}

// 新建
export function save(data) {
  return defHttp.post({ url: Api.Prefix + '/save', data });
}

// 修改
export function update(data) {
  return defHttp.post({ url: Api.Prefix + '/update', data });
}
