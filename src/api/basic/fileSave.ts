import BaseApi from '/@/api/base/BaseApi'


class Api extends BaseApi {

  // 文件分片获取
  getUploadTokenQiniu = () => this.get('getQiniuUploadToken')

  // 文件分片获取
  getFile = (fileId) => this.getUrl(`getFile/${fileId}`)

  // 文件分片获取，适用于漳州内网
  getFileLocal = (fileId) => this.getUrl(`getFileLocal/${fileId}`)

  // 图片缩略图
  getFilePreview = (fileId) => this.getUrl(`getFilePreview/${fileId}`)

  // 图片缩略图
  preview = (fileId) => this.getUrl(`preview/${fileId}`)

  // 上传文件
  uploadFile = (file: any, callback?: (progressEvent: any) => void) => this.postFile('upload', file, { onUploadProgress: callback })

  // 解析七牛云URL，保存本地数据
  uploadFromUrlQiniu = (url: string) => this.post('uploadFromUrlQiniu', { url })

}

export default new Api('/api/base/admin', 'fileSave');
