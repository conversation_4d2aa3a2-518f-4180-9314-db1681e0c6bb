import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

 // 导入
 importData = (params: any): Promise<any> => this.post('ImportData', params);

 // 导入预览
 importPreview = (params: any): Promise<any> => this.get('ImportPreview', params);

 // 下发接口人
 allotsContactPerson = (params: any): Promise<any> => this.post('allotsContactPerson', params);

 // 下发班组长
 allotsJobForeman = (params: any): Promise<any> => this.post('allotsJobForeman', params);

 // 上传附件
 uploadFile = (params: any): Promise<any> => this.post('uploadFile', params);

 // 批量接口人
 submitContactPerson = (params: any): Promise<any> => this.post('submitContactPerson', params);

 // 批量管理员
 submitAdministrator = (params: any): Promise<any> => this.post('submitAdministrator', params);

 // 批量同意
 handleBatchAgree = (params: any): Promise<any> => this.post('handleBatchAgree', params);

 // 同意
 handleAgree = (params: any): Promise<any> => this.post('handleAgree', params);

 // 生成每队剩余尾项情况
 generateLastTerm = (id:String): Promise<any> => this.download('generateLastTerm?id=',id);

}

export default new Api('/api/example', 'lastTermManage');