import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/changeOrgOrder',
}

// 获取转岗转部门列表
export function getRequestList(data) {
  return defHttp.get({ url: Api.Prefix , data });
}

// 获取转岗转部门子表
export function getOrgList(id) {
  return defHttp.get({ url: Api.Prefix + `/orgInfo/${id}/items` });
}

// 删除转岗转部门单
export function delAdmission(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: Api.Prefix + '/deleteById/'  + id });
}

// 上传转岗转部门单信息预览
export function importOrgPreview(fileId) {
  return defHttp.get({ url: Api.Prefix + `/importOrgPreview/${fileId}` });
}