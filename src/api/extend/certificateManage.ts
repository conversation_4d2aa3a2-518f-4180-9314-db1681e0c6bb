import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  // 导入
  importData = (params: any): Promise<any> => this.post('ImportData', params);

  // 导入预览
  importPreview = (params: any): Promise<any> => this.get('ImportPreview', params);

  // 证件生成数据获取
  getCertificateInfo = (ids: string[]): Promise<any> => this.post('getCertificateInfo', { ids });

  // 重新生成证件
  regenerateCertificateInfo = (ids: string[]): Promise<any> => this.post('regenerateCertificateInfo', { ids });

  // 证件数据查看
  viewCertificateInfo = (ids: string[]): Promise<any> => this.post('viewCertificateInfo', { ids });

  // 获取证件照
  officeInfo = (id: any): Promise<any> => this.get(`officeInfo/${id}`);

  // 修改证件照
  updateOfficeInfo = (params: any): Promise<any> => this.post('updateOfficeInfo', params);

  // 生成证件台账
  genCertificateLedger = (id:String): Promise<any> => this.download('genCertificateLedger?id=',id);

}

export default new Api('/api/example', 'certificateManage');
