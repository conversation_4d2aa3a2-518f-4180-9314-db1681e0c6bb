import BaseFlowApi from "/@/api/base/BaseFlowApi";

class Api extends BaseFlowApi {
    // 获取最大序号
    getMaxYearIndex = (year: any,deptId:any): Promise<any> => this.get('getMaxYearIndex', { year,deptId });

    // 导入
    importData = (params: any): Promise<any> => this.post('ImportData', params);

    // 导入预览
    importPreview = (params: any): Promise<any> => this.get('ImportPreview', params);

    //导出Pdf
    exportPdf = (id:String):Promise<any> =>this.download('exportPdf?id=',id);

}

export default new Api('/api/base/ext', 'zzScaffold');
