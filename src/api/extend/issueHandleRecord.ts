import BaseFlowApi from "/@/api/base/BaseFlowApi";

class Api extends BaseFlowApi {

  /** ids批量删除 */
  removeByIds = (ids: any[]): Promise<any> => this.post('removeByIds', ids);

  // /**
  //  * 根据月份统计隐患记录
  //  */
  // getRecordCountByMonth=(): Promise<any> => this.get('getRecordCountByMonth');

  /**
   * 根据时间统计隐患记录
   */
  getIssueTimeRecord=(params:any): Promise<any> => this.post('getIssueTimeRecord',params);


  /**
   * 根据隐患类别统计记录
   */
  getIssueTypeRecord = (params: any): Promise<any> => this.post('getIssueTypeRecord', params);

  /**
   * 根据隐患区域统计记录类别
   */
  getIssueAreaRecordCount = (params: any): Promise<any> => this.post('getIssueAreaRecordCount', params);

  genIssueNum = (areaTerm: string): Promise<any> => this.get('genIssueNum', {areaTerm});
}

export default new Api('/api/base/ext', 'issueHandleRecord');
