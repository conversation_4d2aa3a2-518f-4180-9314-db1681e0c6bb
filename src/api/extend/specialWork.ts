import BaseApi from '/@/api/base/BaseApi';

class Api extends BaseApi {
  // 批量提交
  batchSubmit = (ids: string[]) => this.post('batchSubmit', { ids });

  // 批量退回
  batchBack = (ids: string[]) => this.post('batchBack', { ids });

  // 编制撤回
  compileRevocation = (id: string) => this.get(`compileRevocation/${id}`);

  // 修改审核图片
  updateExamineInfo = (data: any) => this.post('updateExamineInfo', data);

  // 修改特种作业文件
  updateSpecialFile = (data: any) => this.post('updateSpecialFile', data);

  // 审核签名
  updateFileBySignExamine = (data: any) => this.post('updateFileBySignExamine', data);

  // 修改编制图片
  updateCompileImage = (data: any) => this.post('updateCompileImage', data);

  // 批准签名
  updateFileBySignApprove = (data: any) => this.post('updateFileBySignApprove', data);

  // 导入
  compileImportData = (params: any): Promise<any> => this.post('compileImportData', params);

  // 导入预览
  compileImportPreview = (params: any): Promise<any> => this.get('compileImportPreview', params);

  // 导入
  importData = (params: any): Promise<any> => this.post('importData', params);

  // 导入预览
  importPreview = (params: any): Promise<any> => this.get('importPreview', params);

  // 上传文件
  uploadSubmitFile = (params: any): Promise<any> => this.post('uploadSubmitFile', params);

  // 生成台账
  genLedger = (id:String): Promise<any> => this.download('exportLedger?id=',id);
}

export default new Api('/api/example', 'specialWork');
