import BaseApi from './BaseApi';

export default class BaseTreeApi extends BaseApi {
  /** 获取ID的向上路径 */
  path = (id: any): Promise<any> => this.get(`path/${id}`);

  /** 获取所有实体列表Tree */
  allTree = (params?: { level?: number }): Promise<any> => this.get(`allTree`, params);

  /** 获取所有实体列表Tree */
  allTreeForTable = (params?: { level?: number }): Promise<any> => this.get(`allTreeForTable`, params);

  /** 获取所有实体列表Tree */
  // getTree = (params: Fa.BaseQueryParams = {}): Promise<any> => this.post(`getTree`, params);

  /** 改变实体列表位置[排序、父节点] */
  changePos = (list: any[]): Promise<any> => this.post(`changePos`, list);

  /** 给定选中的value，返回value向上查找的节点路径[1, 1-1, 1-1-1] */
  treePathLine = (id: any): Promise<any> => this.get(`treePathLine/${id}`);

  /** 给定parentId，返回当前层级的节点List */
  treeListLayer = (parentId: any): Promise<any> => this.get(`treeListLayer/${parentId}`);

  /** 给定选中的value，返回value向上查找的节点路径xxx，并返回路径xxx的层级的Tree */
  treeFindPath = (id: any): Promise<any> => this.get(`treeFindPath/${id}`);

  /** 获取唯一实体 */
  moveUp = (id: any): Promise<any> => this.get(`moveUp/${id}`);

  /** 获取唯一实体 */
  moveDown = (id: any): Promise<any> => this.get(`moveDown/${id}`);
}
