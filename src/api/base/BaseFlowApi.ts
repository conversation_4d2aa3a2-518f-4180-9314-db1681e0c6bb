import BaseApi from './BaseApi';

/**
 * 流程通用API
 */
export default class BaseFlowApi extends BaseApi {

  /** 流程-获取表单信息 */
  flowInfo = (id: any): Promise<any> => this.get(`flow/${id}`);

  /** 流程-新建 */
  flowCreate = (id: any, entity: any): Promise<any> => this.post(`flow/${id}`, entity);

  /** 流程-修改 */
  flowUpdate = (id: any, entity: any): Promise<any> => this.put(`flow/${id}`, entity);

  /** 流程-id删除 */
  flowRemove = (id: any): Promise<any> => this.delete(`flow/remove/${id}`);

}
