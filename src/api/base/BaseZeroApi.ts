import type { AxiosRequestConfig } from 'axios';
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from "/@/hooks/setting";


const globSetting = useGlobSetting();

export default class BaseZeroApi {
  public apiPrefix: string;

  public apiModal: string;

  constructor(apiPrefix: string, apiModal: string) {
    this.apiPrefix = apiPrefix;
    this.apiModal = apiModal;
  }

  protected get = (api: string, params?: any, config?: AxiosRequestConfig) => defHttp.get({
    url: `${this.apiPrefix}/${this.apiModal}/${api}`,
    ...config,
    params
  });

  protected delete = (api: string, config?: AxiosRequestConfig) => defHttp.delete({
    url: `${this.apiPrefix}/${this.apiModal}/${api}`,
    ...config
  });

  protected post = (api: string, data: object, config?: AxiosRequestConfig) => defHttp.post({
    url: `${this.apiPrefix}/${this.apiModal}/${api}`,
    data,
    ...config
  });

  protected put = (api: string, data: object, config?: AxiosRequestConfig) => defHttp.put({
    url: `${this.apiPrefix}/${this.apiModal}/${api}`,
    data,
    ...config
  });

  protected postFile = (api: string, file: any, config?: AxiosRequestConfig) => defHttp.postFile({
    url: `${this.apiPrefix}/${this.apiModal}/${api}`,
    ...config
  }, file);

  protected download = (api: string, data: object, config?: AxiosRequestConfig) => defHttp.requestDownload({
    url: `${this.apiPrefix}/${this.apiModal}/${api}`,
    data,
    ...config
  });

  public getUrl = (api: string) => `${globSetting.apiUrl}${this.apiPrefix}/${this.apiModal}/${api}`;
}
