import BaseZeroApi from './BaseZeroApi';

export default class BaseApi extends BaseZeroApi {
  /** 新增 */
  save = (params: any): Promise<any> => this.post('save', params);

  /** 新增批量 */
  saveBatch = (params: any[]): Promise<any> => this.post('saveBatch', params);

  /** id查询 */
  getById = (id: any): Promise<any> => this.get(`getById/${id}`);

  /** id查询详情 */
  getDetailById = (id: any): Promise<any> => this.get(`getDetailById/${id}`);

  /** id查询详情 */
  // getDetail = (id: KeyType): Promise<any> => this.get(`getDetail/${id}`);

  /** ids集合查询 */
  getByIds = (ids: any[]): Promise<any> => this.post(`getByIds`, ids);

  /** 更新 */
  update = (params: any): Promise<any> => this.post('update', { ...params });

  /** 批量更新 */
  updateBatch = (entityList: any[]): Promise<any> => this.post('updateBatch', entityList);

  /** 新增or更新 */
  saveOrUpdate = (params: any): Promise<any> => this.post('saveOrUpdate', params);

  /** 批量新增or更新 */
  saveOrUpdateBatch = (params: any[]): Promise<any> => this.post('saveOrUpdateBatch', params);

  /** id删除 */
  remove = (id: any): Promise<any> => this.delete(`remove/${id}`);

  /** ids批量删除 */
  removeBatchByIds = (ids: any[]): Promise<any> => this.post('removeBatchByIds', ids);

  removeByIds = (ids: any[]): Promise<any> => this.post('removeByIds', ids);


  /** id永久删除 */
  // removePer = (id: KeyType): Promise<any> => this.delete(`removePer/${id}`);

  /** ids批量永久删除 */
  // removePerBatchByIds = (ids: KeyType[]): Promise<any> => this.post('removePerBatchByIds', ids);

  /** 通过查询条件删除 */
  removeByQuery = (params: any): Promise<any> => this.post('removeByQuery', params);

  /** 获取所有List */
  all = (): Promise<any> => this.get('all');

  /** 获取List，带过滤查询条件 */
  list = (params: any): Promise<any> => this.post('list', params);

  /** 获取List(限定登录用户创建)，带过滤查询条件 */
  // mineList = (params: any): Promise<any> => this.post('mineList', params);

  /** 过滤条件统计数量 */
  // count = (params: any): Promise<any> => this.post('count', params);

  /** 分页获取 */
  page = (params: any): Promise<any> => this.post('page', params);

  /** 个人分页查询 */
  minePage = (params: any): Promise<any> => this.post('minePage', params);

  /** 过滤条件导出Excel */
  exportExcel = (params: any): Promise<any> => this.download('exportExcel', params);

  // 导入
  importData = (params: any): Promise<any> => this.post('ImportData', params);

  // 导入预览
  importPreview = (params: any): Promise<any> => this.get('ImportPreview', params);


  /** 下载导入Excel模版 */
  // exportTplExcel = (): Promise<undefined> => this.download('exportTplExcel', {});

  /** 导入Excel数据 */
  // importExcel = (params: { fileId: string }): Promise<any> => this.post('importExcel', params);
}
