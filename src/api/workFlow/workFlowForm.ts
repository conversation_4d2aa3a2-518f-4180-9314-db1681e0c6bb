import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/workflow/Engine/FlowTask',
}

// 新建表单
export function create(data) {
  return defHttp.post({ url: Api.Prefix, data });
}
// 更新表单
export function update(data) {
  return defHttp.put({ url: Api.Prefix + `/${data.id}`, data });
}
// 获取task
export function getFlowTask(id: string) {
  return defHttp.get({ url: Api.Prefix+ `/getFlowTask/${id}`});
}
// 获取taskNode
export function getFlowTaskNode(id: string) {
  return defHttp.get({ url: Api.Prefix+ `/getFlowTaskNode/${id}`});
}

// 获取流程taskOperator
export function getFlowTaskOperator(taskId: string, nodeNo:string) {
  return defHttp.get({url: Api.Prefix + `/getFlowTaskOperator?taskId=${taskId}&nodeNo=${nodeNo}`});
}
