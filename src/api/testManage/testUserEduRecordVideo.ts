import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  /** 获取视频播放进度 */
  getWatchData = (userEduRecordId, userId) => this.post('getWatchData', { userEduRecordId, userId });

  /** 保存视频播放位置 */
  saveWatchData = (data) => this.post('saveWatchData', data);

  /*视频更新，用户观看视频进度更新 */
  updateUVideoDuration=(videoId:string)=>this.get('updateUVideoDuration',{videoId})
}

export default new Api('/api/example', 'TestUserEduRecordVideo');
