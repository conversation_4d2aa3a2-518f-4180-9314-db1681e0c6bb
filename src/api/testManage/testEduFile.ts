import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  // 已经同步完成，此方法不需要再使用了
  syncOldData = () => this.get('syncOldData')

  //生成课件编号
  genFileNum = (params:any): Promise<any> => this.post(`genFileNum`,params)

  //根据培训计划获取课件
  getFileDataByEduId = (eduId): Promise<any> => this.get(`getFileDataByEduId`, {eduId})


  /* 根据eduFile、eduFileVideo表信息 */
  updateVideo = (params: any): Promise<any> => this.post(`updateVideo`, { ...params })

  /** 分页获取 */
  listByConfig = (params: any): Promise<any> => this.post('listByConfig', params);
}

export default new Api('/api/example', 'TestEduFile');
