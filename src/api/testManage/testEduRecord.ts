import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  genEduNum = (params: any): Promise<any> => this.post('genEduNum', params);

  /** ids批量删除 */
  removeByIds = (ids: any[]): Promise<any> => this.post('removeByIds', ids);

  /**
   * 获取培训类型(枚举)
   * @param id
   */
  getStudyType = (recordId): Promise<any> => this.get(`getStudyType/${recordId}`);

}

export default new Api('/api/example', 'TestEduRecord');
