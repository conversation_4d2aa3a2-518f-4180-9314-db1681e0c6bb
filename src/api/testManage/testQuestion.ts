import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  // 根据分类统计试题数量
  countByType = (questionBankIds: string[]): Promise<any> => this.post('countByType', { questionBankIds });

  // 导入
  importData = (params: any): Promise<any> => this.post('ImportData', params);

  // 导入预览
  importPreview = (params: any): Promise<any> => this.get('ImportPreview', params);

}

export default new Api('/api/example', 'TestQuestion');
