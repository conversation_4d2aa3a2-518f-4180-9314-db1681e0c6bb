import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  /**
   * id查询考试记录的试题列表
   * @param testGradeId 考试记录ID
   */
  getQuestionsById = (testGradeId:string) => this.get(`getQuestionsById/${testGradeId}`)

  /** 获取用户当前考试 */
  getCurTestByUser = (eduRecordId) => this.get('getCurTestByUser', {eduRecordId});

  /** 开始本人考试 */
  startExam = (id) => this.get(`startExam/${id}`, {});

  /** 结束本人考试 */
  endExam = (id) => this.get(`endExam/${id}`, {});

  /** 开始本人下一次考试 */
  startNewExam = (eduRecordId): Promise<any> => this.get(`startNewExam`, {eduRecordId});

  /** 获取用户考试相关试题 */
  // getQuestionByUser = (eduId): Promise<any> => this.get(`getQuestionByUser?eduId=${eduId}`);
  getQuestionByUser = (params): Promise<any> => this.get(`getQuestionByUser`, params)

  /** 获取考试用户列表 */
  getExamUsers = (id: string): Promise<any> => this.get(`getExamUsers/${id}`);

  /** 根据用户获取成绩数据 */
  getGradesByUser = (params: any): Promise<any> => this.post(`getGradesByUser`, params);

  /** 根据培训计划获取用户最高成绩 */
  getUserMaxGrade = (params): Promise<any> => this.post(`getUserMaxGrade`,params);

}

export default new Api('/api/example', 'TestGrade');
