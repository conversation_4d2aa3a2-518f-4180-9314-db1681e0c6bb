import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  getUserEduList = (userId: string): Promise<any> => this.get('getUserEduList', {userId});

  /** 更新用户学习进度 */
  updateProgress = (eduRecordId) => this.put('updateProgress', {eduRecordId});

  /* 确认该三级培训完成情况 */
  checkInEduStatus = (eduRecordId) => this.get('checkInEduStatus', {eduRecordId});

  syncType = () => this.get('syncType', {});

  removeBatchByEdu = (params): Promise<any> => this.post('removeBatchByEdu', params);
}

export default new Api('/api/example', 'TestUserEduRecord');
