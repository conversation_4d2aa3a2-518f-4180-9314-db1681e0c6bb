import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  /** 分页获取 */
  listConfig = (params: { level: string, organizeId: string, posId: string }): Promise<any> => this.post('listConfig', params);

  /** 获取配置关联的培训计划 */
  getEduRecord = (params: { level: string, organizeId: string, posId: string }): Promise<any> => this.post('getEduRecord', params);

  /** 发起培训 */
  start = (params: { organizeId: string, posId: string, userIds: string[] }): Promise<any> => this.post('start', params);

}

export default new Api('/api/example', 'testEduRecordInConfig');
