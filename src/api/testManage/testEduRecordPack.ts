import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  // 获取培训计划编号
  genPackNum = (params: any): Promise<any> => this.post('genPackNum', params);

  // 查询分组下培训计划
  getRecordList = (packId, groupId): Promise<any> => this.post('getRecordList', {packId, groupId});

  // 替换培训计划
  replace = (packId, groupId, recordId, organizeId, posId): Promise<any> => this.post('replace', {packId, groupId, recordId, organizeId, posId});

  // 移出培训计划
  removeFromPack = (packId, groupId, recordId, organizeId, posId): Promise<any> => this.post('removeFromPack', {packId, groupId, recordId, organizeId, posId});

}

export default new Api('/api/example', 'TestEduRecordPack');
