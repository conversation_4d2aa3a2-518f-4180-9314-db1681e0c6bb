import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  /**
   * 获取培训计划完成进度
   * @param eduRecordId
   */
  getEduRecordProcess=(eduRecordId:string):Promise<any>=>this.get(`getEduRecordProcess/${eduRecordId}`);

  /**
   * 同步用户考试状态
   * @param eduRecordId
   */
  syncUserTestStatus=(eduRecordId:string):Promise<any>=>this.get(`syncUserTestStatus/${eduRecordId}`);

  /**
   * 获取指定用户进度列表
   * @param userId
   */
  getUserStatusList = (params): Promise<any> => this.get(`getUserStatusList`,params)
  getAllUserStatusList = (userId: string): Promise<any> => this.get(`getAllUserStatusList`,{userId})

  // 获取计划进度
  getUserStatusListByEdu= (params): Promise<any> => this.get(`getUserStatusListByEdu`,params)

  /**
   * 获取指定用户进度列表
   * @param userId
   */
  getUserStatusInStudyList = (userId: string): Promise<any> => this.get('getUserStatusInStudyList', {userId})


  getCurrentUserInStudyList = (): Promise<any> => this.get('getCurrentUserInStudyList',{})

  // 获取列表
  getList = (parms): Promise<any> => this.post('getList', parms)

  // 用户学习积分
  getUserStudyScore = (): Promise<any> => this.get('getUserStudyScore')

  getProcessListByUsers = (params: any): Promise<any> => this.post('getProcessListByUsers', params)

}

export default new Api('/api/example', 'EduProcess');
