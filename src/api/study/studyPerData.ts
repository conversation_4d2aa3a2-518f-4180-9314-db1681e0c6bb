import BaseApi from '/@/api/base/BaseApi';

class Api extends BaseApi {

  // 获取文件列表
  getFile = (params: any): Promise<any> => this.post('getFile', params);

  // 上传文件
  uploadSubmitFile = (params: any): Promise<any> => this.post('uploadSubmitFile', params);

  // 提交
  submit = (id: string): Promise<any> => this.get(`submit/${id}`);

  // 批量提交
  batchSubmit = (params: any): Promise<any> => this.post('batchSubmit', params);

  // 审批
  review = (id: string): Promise<any> => this.get(`review/${id}`);

  // 批量审批
  batchReview = (params: any): Promise<any> => this.post('batchReview', params);

  // 退回
  withDraw = (id: string): Promise<any> => this.get(`withDraw/${id}`);

  // 选择导出
  chooseExport = (params: any): Promise<any> => this.download('chooseExport', params);
  
}

export default new Api('/api/example', 'studyPerData');
