import BaseApi from '/@/api/base/BaseApi'


class Api extends BaseApi {

  /** 获取培训用户列表 */
  getSelectedPerList = (id: string): Promise<any> => this.get(`getSelectedPerList/${id}`);

  /** 更新培训用户列表 */
  saveSelectedPerList = (data: any): Promise<any> => this.post('saveSelectedPerList', data);

  /** 不过滤参数，获取list */
  listByAllPara = (data: any): Promise<any> => this.post('listByAllPara', data);

  /** 增加培训用户列表 */
  addPer = (data: any): Promise<any> => this.post('addPer', data);

}

export default new Api('/api/example', 'StudyPackApproval');
