import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/app/apk',
}

// 获取APP列表
export function getApkList(data) {
  return defHttp.post({ url: Api.Prefix + '/page', data });
}

// 删除APP
export function delApk(id) {
  return defHttp.delete({ url: Api.Prefix + '/remove/' + id });
}

// 获取APP详情
export function getApkById(id) {
  return defHttp.get({ url: Api.Prefix + '/getById/' + id });
}

// 新建APP
export function create(data) {
  return defHttp.post({ url: Api.Prefix + '/create', data });
}

// 修改APP
export function update(data) {
  return defHttp.post({ url: Api.Prefix + '/update', data });
}

// 解析APK信息
export function getApkInfo(data) {
  return defHttp.post({ url: Api.Prefix + '/getApkInfo', data });
}
