import BaseFlowApi from '/@/api/base/BaseFlowApi';

class Api extends BaseFlowApi {
  /** 检查申请单号是否存在 */
  checkApplicationNo = (applicationNo: string, id?: string): Promise<any> =>
    this.get('checkApplicationNo', { applicationNo, id });

  /** 批量导入 */
  importData = (data: any): Promise<any> => this.post('importData', data);

  /** 验证导入数据 */
  validateImportData = (data: any): Promise<any> => this.post('validateImportData', data);

  /** 导出数据 */
  exportData = (data: any): Promise<any> => this.download('exportData', data);

  /** 获取导入模板 */
  getImportTemplate = (): Promise<any> => this.download('getImportTemplate', {});

  /** 获取可用车辆列表 */
  getAvailableVehicles = (useDate: string): Promise<any> =>
    this.get('getAvailableVehicles', { useDate });

  /** 分配车辆 */
  assignVehicle = (applicationId: string, vehicleId: string): Promise<any> =>
    this.post('assignVehicle', { applicationId, vehicleId });

  /** 开始使用车辆 */
  startUse = (applicationId: string): Promise<any> =>
    this.post('startUse', { applicationId });

  /** 结束使用车辆 */
  endUse = (applicationId: string, actualDuration: number): Promise<any> =>
    this.post('endUse', { applicationId, actualDuration });

  /** 获取申请统计数据 */
  getStatistics = (params: any): Promise<any> =>
    this.get('getStatistics', params);

  /** 获取最大申请单号序号 */
  getMaxApplicationIndex = (year: any, deptId: any): Promise<any> =>
    this.get('getMaxApplicationIndex', { year, deptId });
}

export default new Api('/api/base/vehicle', 'application');
