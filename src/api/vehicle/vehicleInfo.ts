import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {
  /** 检查车牌号是否存在 */
  checkVehicleNumber = (vehicleNumber: string, id?: string): Promise<any> =>
    this.get('checkVehicleNumber', { vehicleNumber, id });

  /** 批量导入 */
  importData = (data: any): Promise<any> => this.post('importData', data);

  /** 验证导入数据 */
  validateImportData = (data: any): Promise<any> => this.post('validateImportData', data);

  /** 导出数据 */
  exportData = (data: any): Promise<any> => this.download('exportData', data);

  /** 获取导入模板 */
  getImportTemplate = (): Promise<any> => this.download('getImportTemplate', {});
}

export default new Api('/api/base/vehicle', 'info');
