import BaseTreeApi from '/@/api/base/BaseTreeApi'

class Api extends BaseTreeApi {

  /** 获取该组织链路上部门 */
  getDepartOrgan = (organizeId: string): Promise<any> => this.get(`getDepartOrgan/${organizeId}`);

  /** 获取该组织链路上班组 */
  getTeamOrgan = (organizeId: string): Promise<any> => this.get(`getTeamOrgan/${organizeId}`);

  /** 批量修改部门的类型 */
  updateBatchCategory = (params: {ids: string[], category: string}) => this.post('updateBatchCategory', params)

  /** 获取节点Tree(公司+部门) */
  allTreeDept = (): Promise<any> => this.get('allTreeDept');
 /** 获取过滤后节点Tree(公司+部门) */
 allFilteredTreeDept = (): Promise<any> => this.get('allFilteredTreeDept');

  syncOldData = () => this.get('syncOldData')

}

export default new Api('/api/permission', 'OrganizeTree');
