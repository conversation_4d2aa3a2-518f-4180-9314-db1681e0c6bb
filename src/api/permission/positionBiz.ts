import BaseApi from '/@/api/base/BaseApi'

class Api extends BaseApi {

  // 导入预览
  importPreview = (params: any): Promise<any> => this.get('importPreview', params);

  // 导入
  importData = (params: any): Promise<any> => this.post('importData', params);

  // 批量移动岗位所属部门
  batchMoveDept = (ids: string[], organizeId: string): Promise<any> => this.post('batchMoveDept', {ids, organizeId});

  // 批量重命名
  batchRename = (ids: string[], name: string): Promise<any> => this.post('batchRename', {ids, name});

  // 复制岗位到指定部门
  copyToDept = (id: string, organizeIds: string[]): Promise<any> => this.post('copyToDept', {id, organizeIds});

}

export default new Api('/api/permission', 'position');
