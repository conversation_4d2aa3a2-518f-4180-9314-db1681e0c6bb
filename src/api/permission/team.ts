import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/example/team',
}

// 分页
export function page(data) {
  return defHttp.post({ url: Api.Prefix + '/teamPage', data });
}

// 批量更新班组
export function batchUpdateTeam(data){
  return defHttp.post({ url: Api.Prefix + '/batchUpdateTeam', data });
}

// 根据id查询
export function getTeamById(id){
  return defHttp.get({ url: Api.Prefix + `/getTeamById/${id}` });
}

// 更新班组
export function updateTeam(data){
  return defHttp.post({ url: Api.Prefix + '/updateTeam', data });
}

// 新增班组
export function saveTeam(data){
  return defHttp.post({ url: Api.Prefix + '/saveTeam', data });
}