<template>
  <div class="camera-page">
    <div class="camera-scroll-container">
      <a-tabs class="camera-tabs">
        <a-tab-pane key="1" tab="直播信息">
          <div class="video-section">
            <div v-if="loading" class="loading-container">
              <a-spin size="large" />
            </div>

            <div v-else-if="error" class="error-container">
              <p class="error-message">{{ error }}</p>
              <pre v-if="debugInfo" class="debug-info">{{ debugInfo }}</pre>
            </div>

            <!-- 视频播放区域 -->
            <div
              v-else-if="detail.deviceId && detail.accessToken && detail.liveUrl"
              class="video-player-wrapper"
            >
              <VideoYs7
                :device-serial="detail.deviceId"
                :access-token="detail.accessToken"
                :url="detail.liveUrl"
                @error="handleVideoError"
              />
            </div>

            <div v-else class="empty-container">
              <p class="empty-message">设备信息不完整，无法显示直播</p>
              <pre v-if="debugInfo" class="debug-info">{{ debugInfo }}</pre>
            </div>

            <!-- 临时添加一些内容来测试滚动效果 -->
            <div class="test-content">
              <h3>设备信息</h3>
              <p>设备ID: {{ detail.deviceId || '未获取' }}</p>
              <p>设备名称: {{ detail.deviceName || '未获取' }}</p>
              <p>设备位置: {{ detail.deviceLocation || '未获取' }}</p>
              <p>负责人: {{ detail.responsiblePerson || '未获取' }}</p>
              <p>直播URL: {{ detail.liveUrl || '未获取' }}</p>


              <h3>操作说明</h3>
              <div class="instructions">
                <p>1. 可以使用控制按钮进行云台操作</p>
                <p>2. 支持移动端和桌面端响应式显示</p>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onErrorCaptured } from 'vue';
import { videoApi } from '/@/api';
import { VideoYs7 } from '/@/components/Video';
import { useRouteQuery } from '@vueuse/router';

const detail = ref<any>({});
const loading = ref(false);
const error = ref('');
const debugInfo = ref('');

const id = useRouteQuery('id');
const isAppEnv = ref(false);

interface InitData {
  id?: string;
  [key: string]: any;
}

const initEnv = () => {
  try {
    isAppEnv.value = window?.appBridge !== undefined;
    debugInfo.value = `当前环境: ${isAppEnv.value ? 'App' : 'Web'}\n路由ID: ${id.value}`;
  } catch (e) {
    console.error('环境检测失败:', e);
  }
};

const init = (data: InitData = {}) => {
  console.log('init', data);
  fetchDetail();
};

async function fetchDetail() {
  loading.value = true;
  error.value = '';

  try {
    if (!id.value) {
      throw new Error('缺少设备ID参数');
    }

    debugInfo.value = `请求参数: id=${id.value}`;

    await new Promise(resolve => setTimeout(resolve, 300));

    const res = await videoApi.getById(id.value);

    if (!res || !res.data) {
      throw new Error('API响应格式错误');
    }

    detail.value = res.data || {};

    if (!detail.value.deviceId) {
      throw new Error('设备ID不存在');
    } else if (!detail.value.accessToken) {
      throw new Error('访问令牌不存在');
    } else if (!detail.value.liveUrl) {
      throw new Error('直播地址不存在');
    }

    debugInfo.value = `API响应: ${JSON.stringify({
      deviceId: detail.value.deviceId,
      hasAccessToken: !!detail.value.accessToken,
      hasLiveUrl: !!detail.value.liveUrl
    }, null, 2)}`;

    console.log('detail:', detail.value);
  } catch (err: any) {
    console.error('获取设备详情失败:', err);
    error.value = err.message || '获取设备详情失败';
    debugInfo.value = `错误详情: ${err.message}\n堆栈: ${err.stack}`;

    if (err.message.includes('CORS') || err.message.includes('cross-origin')) {
      error.value += ' (可能是跨域问题)';
    }
  } finally {
    loading.value = false;
  }
}

const handleVideoError = (err: any) => {
  console.error('视频组件错误:', err);
  error.value = '视频播放失败';
  debugInfo.value = `视频组件错误: ${JSON.stringify(err, null, 2)}`;
};

onErrorCaptured((err, instance, info) => {
  console.error('组件内部错误:', err, info);
  error.value = '组件加载失败';
  debugInfo.value = `组件错误: ${err.message}\n位置: ${info}`;
  return false;
});

onMounted(() => {
  initEnv();
  fetchDetail();
});
</script>

<style lang="less">
.camera-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;

  .camera-scroll-container {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 4px;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }

    // Firefox滚动条样式
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.1);
  }

  .camera-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    :deep(.ant-tabs-content-holder) {
      padding: 16px;
    }
  }
}

.video-section {
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  .error-container {
    text-align: center;
    padding: 32px 16px;

    .error-message {
      color: #ff4d4f;
      font-size: 16px;
      margin-bottom: 16px;
    }

    .debug-info {
      background: #1f1f1f;
      color: #fff;
      padding: 16px;
      border-radius: 4px;
      font-size: 12px;
      text-align: left;
      overflow-x: auto;
      white-space: pre-wrap;
    }
  }

  .empty-container {
    text-align: center;
    padding: 32px 16px;

    .empty-message {
      color: #666;
      font-size: 16px;
      margin-bottom: 16px;
    }

    .debug-info {
      background: #1f1f1f;
      color: #fff;
      padding: 16px;
      border-radius: 4px;
      font-size: 12px;
      text-align: left;
      overflow-x: auto;
      white-space: pre-wrap;
    }
  }

  .video-player-wrapper {
    width: 100%;
    background: transparent;
    border-radius: 8px;
    overflow: visible;
    margin-bottom: 24px;

    // 强制视频组件自适应
    :deep(.jnpf-content-wrapper-center) {
      padding: 0 !important;
      background: transparent !important;
      width: 100% !important;
    }

    :deep(.video-container) {
      width: 100% !important;
    }

    :deep(.video-player) {
      width: 100% !important;
    }
  }

  .test-content {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-top: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;

      &:not(:first-child) {
        margin-top: 32px;
      }
    }

    p {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      margin: 8px 0;
    }

    .debug-section {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 4px;
      border-left: 4px solid #007bff;
    }

    .instructions {
      background: #f0f8ff;
      padding: 16px;
      border-radius: 4px;
      border-left: 4px solid #28a745;

      p {
        color: #495057;
        margin: 4px 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .camera-page {
    .camera-scroll-container {
      padding: 12px;

      // 移动端滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
      }
    }

    .camera-tabs {
      :deep(.ant-tabs-content-holder) {
        padding: 12px;
      }
    }
  }

  .video-section {
    .video-player-wrapper {
      border-radius: 4px;
    }
  }
}

@media (max-width: 480px) {
  .camera-page {
    .camera-scroll-container {
      padding: 8px;
    }

    .camera-tabs {
      :deep(.ant-tabs-content-holder) {
        padding: 8px;
      }
    }
  }
}

// 确保在所有设备上都有足够的内容高度来触发滚动
.video-section {
  min-height: calc(100vh - 200px);
}

// 为了测试滚动效果，临时添加一些内容高度
.video-player-wrapper {
  min-height: 400px;
}

@media (max-width: 768px) {
  .video-player-wrapper {
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .video-player-wrapper {
    min-height: 250px;
  }
}
</style>
