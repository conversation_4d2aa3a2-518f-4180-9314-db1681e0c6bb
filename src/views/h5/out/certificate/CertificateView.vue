<template>
  <div style="width: 100%; height: 100%; overflow: auto;">
    <div id="card">
      <div v-for="item in list" :key="item.id" :id="`user-certificate-card-${item.id}`" class="fa-flex-row fa-mb12">
        <CertificateCard :data="item" class="fa-flex-column" style="max-width: 500px; transform-origin: 0px 0px;" :style="{transform: `scale(${scale})`}" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { certificateManageApi } from "/@/api";
import { useRouteQuery } from '@vueuse/router'
import CertificateCard from "/@/views/extend/certificateManage/cube/CertificateCard.vue";

defineOptions({ name: 'certificateView' });

const id = useRouteQuery('id')
// const id = '0ec4b6cd2c5707cd2ea8db008f033ee3';
const scale = document.body.clientWidth / 500;

const loading = ref(false)
const list = ref<any[]>([])

function getData() {
  loading.value = true
  certificateManageApi.viewCertificateInfo([id.value]).then(res => {
    list.value = res.data
    loading.value = false
  }).catch(() => loading.value = false)
}

onMounted(() => {
  getData()
})
</script>

<style scoped>

</style>
