<template>
  <div class="app-download-page">
    <div class="container">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 应用信息卡片 -->
        <div class="app-info-card">
          <div class="app-icon">
            <img :src="logoUrl" alt="App Icon" class="icon" />
            <div class="version-badge">v{{ appInfo.versionName || appVersion }}</div>
          </div>
          <div class="app-details">
            <h2 class="app-name">{{ appInfo.name || '数智化管理平台' }}</h2>
            <p class="app-description">
              {{ appInfo.remark || '集成项目管理、设备监控、数据分析等功能的综合性移动办公平台' }}
            </p>
            <div v-if="loading" class="loading-text">正在加载APP信息...</div>
          </div>
        </div>

        <!-- 下载按钮区域 -->
        <div class="download-section">
          <div class="download-buttons">
            <a
              :href="getDownloadUrl()"
              class="download-btn android-btn"
              @click="handleDownload('android')"
            >
              <div class="btn-icon">
                <AndroidOutlined />
              </div>
              <div class="btn-text">
                <span class="platform">安卓手机</span>
                <span class="action">点击下载</span>
              </div>
            </a>
          </div>

          <!-- 二维码下载区域 -->
          <div class="qr-download-section">
            <h3 class="qr-title">扫码下载</h3>
            <div class="qr-codes">
              <div class="qr-item">
                <canvas ref="androidQrRef" class="qr-canvas"></canvas>
                <p class="qr-label">安卓手机版本</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能特色 -->
        <div class="features-section">
          <h3 class="features-title">主要功能</h3>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon">
                <DashboardOutlined />
              </div>
              <div class="feature-content">
                <h4>项目管理</h4>
                <p>实时跟踪项目进度，高效协作管理</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <MonitorOutlined />
              </div>
              <div class="feature-content">
                <h4>设备监控</h4>
                <p>远程监控设备状态，及时预警处理</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <BarChartOutlined />
              </div>
              <div class="feature-content">
                <h4>数据分析</h4>
                <p>智能数据分析，辅助决策支持</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <TeamOutlined />
              </div>
              <div class="feature-content">
                <h4>团队协作</h4>
                <p>便捷沟通交流，提升工作效率</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统要求 -->
        <div class="requirements-section">
          <h3 class="requirements-title">系统要求</h3>
          <div class="requirements-grid">
            <div class="requirement-item">
              <AndroidOutlined class="req-icon" />
              <div class="req-content">
                <h4>安卓手机</h4>
                <p>安卓6.0及以上版本</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="footer">
        <p class="copyright">© 2024 数智化管理平台. 保留所有权利.</p>
        <p class="contact">如有问题，请联系技术支持</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick } from 'vue';
  import {
    AndroidOutlined,
    DashboardOutlined,
    MonitorOutlined,
    BarChartOutlined,
    TeamOutlined
  } from '@ant-design/icons-vue';
  import QRCode from 'qrcode';
  import { getApkList } from '/@/api/app/app';
  import { useGlobSetting } from '/@/hooks/setting';

  defineOptions({ name: 'AppDownload' });

  const globSetting = useGlobSetting();

  // 应用信息
  const appVersion = ref('1.0.0');
  const logoUrl = ref('/src/assets/images/logo.png');
  const appInfo = ref<any>({});
  const loading = ref(false);

  // 下载链接配置
  const downloadLinks = ref({
    android: 'https://www.example.com/app-android.apk', // 替换为实际的Android下载链接
  });

  // 二维码引用
  const androidQrRef = ref<HTMLCanvasElement>();

  // 生成二维码
  const generateQRCode = async (canvas: HTMLCanvasElement, url: string) => {
    if (!canvas || !url) return;

    try {
      await QRCode.toCanvas(canvas, url, {
        width: 200,
        height: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      });
    } catch (error) {
      console.error('生成二维码失败:', error);
    }
  };

  // 获取APP信息
  const fetchAppInfo = async () => {
    try {
      loading.value = true;
      const res = await getApkList({ currentPage: 1, pageSize: 1 });
      if (res.data && res.data.list && res.data.list.length > 0) {
        appInfo.value = res.data.list[0];
        // 更新logo URL
        if (appInfo.value.iconId) {
          logoUrl.value = appInfo.value.iconId;
        }
      }
    } catch (error) {
      console.error('获取APP信息失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 获取下载URL
  const getDownloadUrl = () => {
    if (appInfo.value.fileId) {
      try {
        const fileInfo = JSON.parse(appInfo.value.fileId);
        if (fileInfo && fileInfo.length > 0) {
          const fileData = fileInfo[0];
          // 优先使用fullUrl，如果没有则拼接url
          if (fileData.fullUrl) {
            return fileData.fullUrl;
          } else if (fileData.url) {
            // url是相对路径，需要拼接基础URL
            const baseUrl = globSetting.apiUrl || window.location.origin;
            return baseUrl + fileData.url;
          }
        }
      } catch (error) {
        console.error('解析下载链接失败:', error);
      }
    }
    return downloadLinks.value.android;
  };

  // 处理下载点击
  const handleDownload = (platform: string) => {
    console.log(`开始下载 ${platform} 版本`);
    // 这里可以添加下载统计等逻辑
  };

  // 初始化二维码
  const initQRCodes = () => {
    nextTick(() => {
      const downloadUrl = getDownloadUrl();
      if (androidQrRef.value && downloadUrl) {
        generateQRCode(androidQrRef.value, downloadUrl);
      }
    });
  };

  // 初始化页面数据
  const initPageData = async () => {
    await fetchAppInfo();
    initQRCodes();
  };

  onMounted(() => {
    initPageData();
  });
</script>

<style lang="less" scoped>
  .app-download-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .main-content {
    padding: 50px 30px 40px;
  }

  .app-info-card {
    display: flex;
    align-items: center;
    background: #f8fafc;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 40px;
    border: 1px solid #e2e8f0;

    .app-icon {
      position: relative;
      margin-right: 20px;

      .icon {
        width: 80px;
        height: 80px;
        border-radius: 16px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .version-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        font-size: 12px;
        font-weight: 700;
        padding: 4px 8px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        border: 2px solid white;
        min-width: 32px;
        text-align: center;
        line-height: 1;
      }
    }

    .app-details {
      flex: 1;

      .app-name {
        font-size: 24px;
        font-weight: 600;
        color: #1a202c;
        margin: 0 0 12px 0;
      }

      .app-description {
        font-size: 16px;
        color: #4a5568;
        line-height: 1.5;
        margin: 0;
      }

      .loading-text {
        font-size: 14px;
        color: #718096;
        margin-top: 8px;
        font-style: italic;
      }
    }
  }

  .download-section {
    margin-bottom: 40px;

    .download-buttons {
      display: flex;
      gap: 20px;
      margin-bottom: 50px;
      justify-content: center;

      .download-btn {
        display: flex;
        align-items: center;
        padding: 24px 40px;
        border-radius: 16px;
        text-decoration: none;
        transition: all 0.3s ease;
        min-width: 280px;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
        }

        .btn-icon {
          font-size: 48px;
          margin-right: 20px;
        }

        .btn-text {
          display: flex;
          flex-direction: column;

          .platform {
            font-size: 24px;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 4px;
          }

          .action {
            font-size: 18px;
            opacity: 0.9;
            font-weight: 500;
          }
        }

        &.android-btn {
          background: linear-gradient(135deg, #a4d65e 0%, #7cb342 100%);
          color: white;
        }

        &.ios-btn {
          background: linear-gradient(135deg, #007aff 0%, #0051d5 100%);
          color: white;
        }
      }
    }

    .qr-download-section {
      text-align: center;

      .qr-title {
        font-size: 26px;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 32px;
      }

      .qr-codes {
        display: flex;
        justify-content: center;
        gap: 50px;

        .qr-item {
          .qr-canvas {
            border-radius: 12px;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
            margin-bottom: 16px;
          }

          .qr-label {
            font-size: 18px;
            font-weight: 600;
            color: #4a5568;
            margin: 0;
          }
        }
      }
    }
  }

  .features-section, .requirements-section {
    margin-bottom: 40px;

    .features-title, .requirements-title {
      font-size: 20px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 24px;
      text-align: center;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .requirements-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      max-width: 500px;
      margin: 0 auto;
    }

    .feature-item, .requirement-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f7fafc;
      border-radius: 12px;
      border: 1px solid #e2e8f0;

      .feature-icon, .req-icon {
        font-size: 24px;
        color: #4299e1;
        margin-right: 16px;
      }

      .feature-content, .req-content {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #2d3748;
          margin: 0 0 4px 0;
        }

        p {
          font-size: 14px;
          color: #718096;
          margin: 0;
        }
      }
    }
  }

  .footer {
    background: #f7fafc;
    padding: 24px 30px;
    text-align: center;
    border-top: 1px solid #e2e8f0;

    .copyright, .contact {
      font-size: 14px;
      color: #718096;
      margin: 4px 0;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .app-download-page {
      padding: 10px;
    }

    .main-content {
      padding: 30px 15px 20px;
    }

    .app-info-card {
      flex-direction: column;
      text-align: center;

      .app-icon {
        margin-right: 0;
        margin-bottom: 16px;

        .version-badge {
          font-size: 11px;
          padding: 3px 6px;
          border-radius: 10px;
        }
      }
    }

    .download-buttons {
      flex-direction: column;
      align-items: center;

      .download-btn {
        min-width: 260px;
        padding: 20px 32px;

        .btn-icon {
          font-size: 40px;
        }

        .btn-text {
          .platform {
            font-size: 22px;
          }

          .action {
            font-size: 16px;
          }
        }
      }
    }

    .qr-download-section {
      .qr-title {
        font-size: 24px;
      }
    }

    .qr-codes {
      flex-direction: column;
      align-items: center;
      gap: 20px !important;

      .qr-item {
        .qr-label {
          font-size: 16px;
        }
      }
    }

    .features-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
