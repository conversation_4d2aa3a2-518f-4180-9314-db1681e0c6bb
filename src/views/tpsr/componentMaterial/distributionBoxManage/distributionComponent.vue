<template>
  <div class="fa-flex-column fa-flex-center">
    <div style="position: relative; width: 800px; padding: 2cm; min-height: 1100px;" ref="paperRef">
      <!-- 左上角标题 - 表格外部 -->
      <div style="position: absolute; top: 20px; left: 20px; font-size: 16px; font-weight: bold;">
        附录 12：配电箱安装验收记录
      </div>

      <table class="main-table" style="width: 100%; border-collapse: collapse; margin-top: 60px;">
        <!-- 公司名称、标题和编号 - 一行三列 -->
        <tr>
          <td style="width: 33%; padding: 20px; border: 1px solid #000; vertical-align: middle;">
            <div style="font-size: 14px; line-height: 1.5; font-weight: bold;">中国核工业第五建设有限公司漳州核电项目部</div>
          </td>
          <td style="width: 34%; padding: 20px; border: 1px solid #000; border-left: none; vertical-align: middle;">
            <div style="font-size: 30px; font-weight: bold; text-align: center;">配电箱安装 验收记录</div>
          </td>
          <td style="width: 33%; padding: 20px; border: 1px solid #000; border-left: none; vertical-align: middle; text-align: right;">
            <div style="font-weight: bold;">
              编号：{{distributionBoxCoverInfo.distributionBoxSchemeNumber || ''}}
            </div>
          </td>
        </tr>

        <!-- 项目信息 -->
        <tr>
          <td colspan="4" style="padding: 15px; text-align: center; font-weight: bold; border: 1px solid #000; border-top: none;">
            {{distributionBoxCoverInfo.projectName || ''}}
          </td>
        </tr>

        <!-- 基本信息 - 两行四列 -->
        <tr style="border-bottom: 1px solid #000;">
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>责任班组</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>{{distributionBoxCoverInfo.distributionBoxResponsibleTeam || ''}}</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>配电箱位置</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border: 1px solid #000; text-align: center;">
            <div>{{distributionBoxCoverInfo.distributionBoxArea || ''}}</div>
          </td>
        </tr>
        <tr style="border-bottom: 1px solid #000;">
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>配电箱类别</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>{{distributionBoxCoverInfo.distributionBoxTypeDesc || ''}}</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>检查日期</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border: 1px solid #000; text-align: center;">
            <div>{{formatToDate(distributionBoxCoverInfo.distributionBoxInspectionDate) || ''}}</div>
          </td>
        </tr>

        <!-- 检查内容表格 -->
        <tr>
          <td colspan="4" style="padding-top: 20px; border: 1px solid #000;">
            <table class="inspection-table" style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
              <tr>
                <th style="border: 1px solid #000; padding: 10px; text-align: center; width: 10%;">序号</th>
                <th style="border: 1px solid #000; padding: 10px; text-align: center; width: 50%;">检查内容</th>
                <th style="border: 1px solid #000; padding: 10px; text-align: center; width: 40%;">检查结果</th>
              </tr>
              <tr v-for="(item, index) in distributionBoxCoverInfo.distributionBoxInspectionContentList"
                  :key="index" style="border: 1px solid #000;">
                <td style="border: 1px solid #000; text-align: center; padding: 10px;">{{index + 1}}</td>
                <td style="border: 1px solid #000; padding: 10px;">{{item.content}}</td>
                <td style="border: 1px solid #000; padding: 10px;">{{item.result}}</td>
              </tr>
            </table>
          </td>
        </tr>

        <!-- 施工信息 - 两行四列 -->
        <tr style="border-bottom: 1px solid #000;">
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>安装时间</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>{{formatToDate(distributionBoxCoverInfo.distributionBoxInstallationTime)}}</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>施工人员</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border: 1px solid #000; text-align: center;">
            <div>{{distributionBoxCoverInfo.distributionBoxInstallationStaff}}</div>
          </td>
        </tr>
        <tr style="border-bottom: 1px solid #000;">
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>拆除时间</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>{{formatToDate(distributionBoxCoverInfo.distributionBoxRemovalTime)}}</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border-right: 1px solid #000; border: 1px solid #000; text-align: center;">
            <div>施工人员</div>
          </td>
          <td style="width: 25%; padding: 15px 10px; border: 1px solid #000; text-align: center;">
            <div>{{distributionBoxCoverInfo.distributionBoxRemovalStaff}}</div>
          </td>
        </tr>

        <!-- 结论 -->
        <tr>
          <td colspan="4" style="padding: 20px 0; border: 1px solid #000;">
            <div style="border: 1px solid #999; padding: 15px; min-height: 80px;">
              <div style="font-weight: bold; margin-bottom: 5px;">结论：</div>
              <div>{{distributionBoxCoverInfo.distributionBoxConclusion || ' '}}</div>
            </div>
          </td>
        </tr>

        <!-- 签名区域 -->
        <tr>
          <td colspan="4" style="padding-top: 30px; border: 1px solid #000;">
            <table class="signature-table" style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
              <tr>
                <td style="width: 25%; padding: 0 10px; text-align: center; border: 1px solid #000; font-weight: bold;">
                  <div>操作者：</div>
                  <div class="signature-area" style="height: 60px; margin: 10px 0; border-bottom: 1px solid #000;">
                    <img v-if="distributionBoxCoverInfo.distributionBoxOperator"
                         :src="distributionBoxCoverInfo.distributionBoxOperator"
                         style="height: 100%;">
                  </div>
                  <div>{{formatToDate(distributionBoxCoverInfo.distributionBoxOperatorDate)}}</div>
                </td>
                <td style="width: 25%; padding: 0 10px; text-align: center; border: 1px solid #000; font-weight: bold;">
                  <div>技术负责人：</div>
                  <div class="signature-area" style="height: 60px; margin: 10px 0; border-bottom: 1px solid #000;">
                    <img v-if="distributionBoxCoverInfo.distributionBoxTechnicalLeader"
                         :src="distributionBoxCoverInfo.distributionBoxTechnicalLeader"
                         style="height: 100%;">
                  </div>
                  <div>{{formatToDate(distributionBoxCoverInfo.distributionBoxTechnicalLeaderDate)}}</div>
                </td>
                <td style="width: 25%; padding: 0 10px; text-align: center; border: 1px solid #000; font-weight: bold;">
                  <div>安全检查员：</div>
                  <div class="signature-area" style="height: 60px; margin: 10px 0; border-bottom: 1px solid #000;">
                    <img v-if="distributionBoxCoverInfo.distributionBoxSafetyInspector"
                         :src="distributionBoxCoverInfo.distributionBoxSafetyInspector"
                         style="height: 100%;">
                  </div>
                  <div>{{formatToDate(distributionBoxCoverInfo.distributionBoxSafetyInspectorDate)}}</div>
                </td>
                <td style="width: 25%; padding: 0 10px; text-align: center; border: 1px solid #000; font-weight: bold;">
                  <div>监理/工程公司：</div>
                  <div class="signature-area" style="height: 60px; margin: 10px 0; border-bottom: 1px solid #000;">
                    <img v-if="distributionBoxCoverInfo.distributionBoxSupervisor"
                         :src="distributionBoxCoverInfo.distributionBoxSupervisor"
                         style="height: 100%;">
                  </div>
                  <div>{{formatToDate(distributionBoxCoverInfo.distributionBoxSupervisorDate)}}</div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { printHtml } from "/@/utils/printUtils";
import { formatToDate } from "/@/utils/dateUtil";

defineProps({
  distributionBoxCoverInfo: {
    type: Object,
    required: true
  }
});

const paperRef = ref();

function printThePaper() {
  printHtml(paperRef.value);
}
</script>

<style scoped>
.main-table {
  border-collapse: collapse;
  width: 100%;
}

.inspection-table, .signature-table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 15px;
}

/* 为签名区域添加下划线 */
.signature-area {
  position: relative;
}

/* 增强可读性的其他样式 */
th, td {
  vertical-align: middle;
  padding: 8px;
  font-size: 14px;
  border: 1px solid #000; /* 为所有单元格添加边框 */
}

/* 打印样式优化 */
@media print {
  .no-print {
    display: none;
  }

  /* 确保打印时不会分页 */
  .avoid-page-break {
    page-break-inside: avoid;
  }
}
</style>
