<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'lightingSchemeNumber'">
              <span>{{ record.lightingSchemeNumber || '-' }}</span>
            </template>
            <template v-if="column.key === 'lightingArea'">
              <span>{{ record.lightingArea || '-' }}</span>
            </template>
            <template v-if="column.key === 'lightingInspectionDate'">
              {{ formatDate(record.lightingInspectionDate) }}
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
            <template v-if="column.key === 'creatorTime'">
              {{ formatDate(record.creatorTime) }}
            </template>
            <template v-if="column.key === 'inspectionDate'">
              {{ formatDate(record.inspectionDate) }}
            </template>
            <template v-if="column.key === 'responsibleTeam'">
              <span>{{ record.responsibleTeam || '-' }}</span>
            </template>
          </template>
        </BasicTable>
      </div>

      <FaFlowCube ref="flowRef" :flow-en-code="flowCode" @reload="reload" />
      <LightingDetailDrawer @register="registerDetailDrawer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch, computed } from "vue";
import { ActionItem, BasicTable, TableAction, useTable } from "/@/components/Table";
import { lightingAcceptanceApi } from "/@/api";
import { useMessage } from "/@/hooks/web/useMessage";
import {
  genFlowDeleteBtn,
  genFlowDetailBtn,
  genFlowEditBtn,
  genQuerySearch,
} from '/@/utils/tableUtils';
import FaFlowCube from "/@/components/Fa/common/src/FaFlowCube.vue";
import { FLOW_OP_TYPE } from "/@/enums/zzEnums";
import { useDrawer } from "/@/components/Drawer";
import LightingDetailDrawer from "/@/views/tpsr/checkAcceptance/lighting/LightingDetailDrawer.vue";
import dayjs from "dayjs";

defineOptions({ name: 'tpsr-lighting-checkAcceptance' });

const flowCode = 'lightingAcceptance';
const flowRef = ref<any>();

const props = defineProps(['lightingAcceptance']);

// 表格列配置
const checkAccepTanceRecordCol = computed(() => [
  { title: '项目名称', dataIndex: 'projectName', key: 'projectName', width: 150 },
  { title: '审批状态', dataIndex: 'currentState', key: 'currentState', width: 100 },
  { title: '当前节点', dataIndex: 'currentNodeName', key: 'currentNodeName', width: 100 },
  // 临时照明相关列
  { title: '临时照明编号', dataIndex: 'lightingSchemeNumber', key: 'lightingSchemeNumber', width: 150 },
  { title: '临时照明区域', dataIndex: 'lightingArea', key: 'lightingArea', width: 120 },
  { title: '临时照明检查日期', dataIndex: 'lightingInspectionDate', key: 'lightingInspectionDate', width: 150 },
  { title: '制单人员', dataIndex: 'creatorUser', key: 'creatorUser', width: 120 },
  { title: '发起时间', dataIndex: 'creatorTime', key: 'creatorTime', width: 150 },
]);

// 日期格式化函数
const formatDate = (date: string | number | Date) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-';
};

async function init() {
  setLoading(true);
  reload();
}

const { createMessage, createConfirm } = useMessage();

const searchInfo = reactive({
  checkAcceptance: '',
  _sorter: 'f_creator_time DESC',
});
const selList = ref<any[]>([]);
function handleAdd() {
  flowRef.value.handleAdd({ lightingAcceptance: props.lightingAcceptance });
}

const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
const [registerTable, { reload, setLoading, getForm }] = useTable({
  api: lightingAcceptanceApi.page,
  columns: checkAccepTanceRecordCol.value,
  searchInfo,
  useSearchForm: true,
  clickToRowSelect: true,
  rowSelection: {
    onChange: (selectedRowKeys) => {
      // 处理行选择变化
    },
  },
  immediate: false, // 避免组件挂载前触发请求
  ellipsis: false,  // 禁用文本省略
  formConfig: {
    schemas: [
      genQuerySearch()
    ],
  },
  // 添加操作列配置
  actionColumn: {
    width: 180,
    title: '操作',
    dataIndex: 'action',
  },
});

// 添加操作列配置
function getTableActions(record: any): ActionItem[] {
  return [
    genFlowEditBtn(record, toDetail),
    genFlowDeleteBtn(record, handleDelete),
    genFlowDetailBtn(record, toDetail),
    {
      label: '材料详情',
      onClick: detailHandle.bind(null, record),
    },
  ];
}

// 处理材料详情点击
function detailHandle(record) {
  openDetailDrawer(true, { lightingAcceptanceId: record.id, lightingAcceptanceRecord: record, userId: undefined });
}

// 处理删除操作
function handleDelete(id: any) {
  lightingAcceptanceApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

// 处理查看/编辑详情
function toDetail(record: any, opType: FLOW_OP_TYPE) {
  flowRef.value.toDetail(record, opType);
}

onMounted(() => {
  init();
});
</script>
