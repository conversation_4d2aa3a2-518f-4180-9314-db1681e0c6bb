<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="1000px" class="full-drawer" title="培训详情" @close="handleClose">
    <div class="fa-full fa-pl12 fa-pr12 fa-pb12 fa-scroll-auto-y">
      <a-tabs class="jnpf-content-wrapper-tabs" destroyInactiveTabPane>
        <a-tab-pane key="1" tab="材料封面">
          <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printInfo(1)">打印封面</a-button>
          <div ref="coverRef">
            <DeliverCoverComponent :deliver-cover-info="deliverCoverInfo" />
          </div>
        </a-tab-pane>

        <a-tab-pane key="2" tab="签到表" v-if="deliverRecord.isNeedSign === 1">
          <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printInfo(2)">打印签到表</a-button>
          <div ref="signInfoRef">
            <SignRecordComponent :sign-info="signInfo" />
          </div>
        </a-tab-pane>

        <a-tab-pane key="3" tab="一页纸">
          <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printInfo(3)">打印一页纸</a-button>
          <div ref="onePageRef">
            <OnePageComponent :one-page-info="onePageInfo" />
          </div>
        </a-tab-pane>

        <a-tab-pane key="4" tab="全部材料">
          <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printInfo(4)">全部打印</a-button>
          <div ref="paperRef">
            <DeliverCoverComponent :deliver-cover-info="deliverCoverInfo" />
            <OnePageComponent :one-page-info="onePageInfo" style="margin-top: 20px;" />
            <SignRecordComponent :sign-info="signInfo" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

  </BasicDrawer>
</template>

<script lang="ts" setup>
import DeliverCoverComponent from "/@/views/extend/safeDeliver/deliverMaterial/deliverCoverManage/DeliverCoverComponent.vue";
import SignRecordComponent from "/@/views/extend/safeDeliver/deliverMaterial/signRecordManage/SignRecordComponent.vue";
import OnePageComponent from "/@/views/extend/safeDeliver/deliverMaterial/onePageManage/OnePageComponent.vue";

import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { ref } from "vue";
import {  checkAcceptanceApi } from "/@/api";
import { useMessage } from "/@/hooks/web/useMessage";
import { printHtml } from "/@/utils/printUtils";

defineOptions({ name: 'LightingDetailDrawer' });

const emit = defineEmits(['register', 'refresh']);
const [registerDrawer, { closeDrawer }] = useDrawerInner(init);

const { createMessage, createConfirm } = useMessage();

const id = ref('');
const approvalId = ref('');

const checkAcceptance = ref<any>({});
const userId = ref('');
const deliverId = ref('');

const paperRef = ref();
const signInfoRef = ref();
const coverRef = ref();
const onePageRef = ref();


async function init(data) {
  checkAcceptance.value = data.checkAcceptance;
  id.value = data.id;

  if (checkAcceptance.value.isNeedSign === 1) {

  }
}

async function getCheckAcceptanceContentList() {
  console.log('getCheckAcceptanceContentList');
  // checkAcceptanceApi.getDeliverPicList(deliverId.value)
  //   .then(res => {
  //     picList.value = res.data;
  //   });
}

async function printInfo(paneIndex) {
  if (paneIndex == 1) {
    printHtml(coverRef.value);
    return;
  }
  if (paneIndex == 2) {
    printHtml(signInfoRef.value);
    return;
  }
  if (paneIndex == 3) {
    printHtml(onePageRef.value);
    return;
  }
  if (paneIndex == 4) {
    printHtml(paperRef.value);
    return;
  }
}

function handleClose() {
  console.log('close the Drawer。。。');

  deliverId.value = '';
  // scoreInfo.value = {}
  closeDrawer();
}
</script>
<style lang="less">
</style>
