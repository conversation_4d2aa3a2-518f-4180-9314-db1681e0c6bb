<template>
  <div class="flow-form">
    <!-- 项目信息部分 -->
    <a-form
      :colon="false"
      :labelCol="{ style: { width: '200px' } }"
      :model="state.dataForm"
      :rules="state.dataRule"
      ref="projectInfoFormRef"
      :disabled="config.disabled"
    >
      <a-row>
        <a-col :span="12" v-if="judgeShow('projectName')">
          <a-form-item label="项目名称" name="projectName">
            <a-input v-model:value="state.dataForm.projectName" placeholder="项目名称" :disabled="judgeWrite('projectName')"/>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <!-- 临时照明安装验收记录 -->
    <div class="record-section" style="margin-top: 40px;" v-if="judgeShow('lighting')">
      <h2 class="record-title">安装验收记录</h2>
      <a-form
        :colon="false"
        :labelCol="{ style: { width: '200px' } }"
        :model="state.dataForm"
        :rules="state.dataRule"
        ref="lightingFormRef"
        :disabled="config.disabled"
      >
        <a-row>
          <a-col :span="12" v-if="judgeShow('lightingSchemeNumber')">
            <a-form-item label="编号" name="lightingSchemeNumber">
              <a-input v-model:value="state.dataForm.lightingSchemeNumber" placeholder="编号" :disabled="judgeWrite('lightingSchemeNumber')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingResponsibleTeam')">
            <a-form-item label="责任班组" name="lightingResponsibleTeams">
              <jnpf-organize-select
                v-model:value="state.dataForm.lightingResponsibleTeams"
                placeholder="请选择责任班组"
                :disabled="judgeWrite('lightingResponsibleTeam')"
                :allowClear="true"
                :multiple="true"
                selectType="all"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingArea')">
            <a-form-item label="区域" name="lightingArea">
              <a-input v-model:value="state.dataForm.lightingArea" placeholder="区域" :disabled="judgeWrite('lightingArea')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingInspectionDate')">
            <a-form-item label="检查日期" name="lightingInspectionDate">
              <jnpf-date-picker v-model:value="state.dataForm.lightingInspectionDate" placeholder="检查日期"
                                :disabled="judgeWrite('lightingInspectionDate')"/>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 临时照明检查内容表格 -->
        <a-row v-if="judgeShow('lightingInspectionContent')">
          <a-col :span="24">
            <a-card title="检查内容与结果" style="margin-top: 20px;">
              <a-table
                :data-source="state.dataForm.lightingInspectionContentList"
                :columns="lightingForm.getInspectionContentColumns"
                size="small"
                :pagination="false"
              >
                <template #headerCell="{ column }">
                  <span class="required-sign"
                        v-if="judgeRequired(`lighting-${column.key}`)">*</span>{{ column.title }}
                </template>
                <template #bodyCell="{ column, record }">
                  <a-input
                    v-model:value="record[column.dataIndex]"
                    :disabled="judgeWrite('lightingInspectionContent')"
                    :placeholder="column.dataIndex === 'result' ? '' : column.title"
                    :style="{ textAlign: 'center' }"
                  />
                </template>
              </a-table>
            </a-card>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12" v-if="judgeShow('lightingInstallationTime')">
            <a-form-item label="安装时间" name="lightingInstallationTime">
              <jnpf-date-picker
                v-model:value="state.dataForm.lightingInstallationTime"
                placeholder="安装时间"
                :disabled="judgeWrite('lightingInstallationTime')"
                style="width:90%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingInstallationStaff')">
            <a-form-item label="安装施工人员（双人）" name="lightingInstallationStaffs">
              <jnpf-user-select
                v-model:value="state.dataForm.lightingInstallationStaffs"
                placeholder="请选择2名安装施工人员"
                :disabled="judgeWrite('lightingInstallationStaffs')"
                :allowClear="true"
                :multiple="true"
                :max-tag-count="2"
                selectType="all"
                @change="validateTwoPersons('lighting-installationStaffs')"
              />
              <template #error>
                <span class="error-tip">需选择2名施工人员</span>
              </template>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12" v-if="judgeShow('lightingRemovalTime')">
            <a-form-item label="拆除时间" name="lightingRemovalTime">
              <jnpf-date-picker
                v-model:value="state.dataForm.lightingRemovalTime"
                placeholder="拆除时间"
                :disabled="judgeWrite('lightingRemovalTime')"
                style="width:90%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingRemovalStaff')">
            <a-form-item label="拆除施工人员（双人）" name="lightingRemovalStaffs">
              <jnpf-user-select
                v-model:value="state.dataForm.lightingRemovalStaffs"
                placeholder="请选择2名拆除施工人员"
                :disabled="judgeWrite('lightingRemovalStaffs')"
                :allowClear="true"
                :multiple="true"
                :max-tag-count="2"
                selectType="all"
                @change="validateTwoPersons('lighting-removalStaffs')"
              />
              <template #error>
                <span class="error-tip">需选择2名施工人员</span>
              </template>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24" v-if="judgeShow('lightingConclusion')">
            <a-form-item label="验收结论" name="lightingConclusion">
              <a-textarea
                v-model:value="state.dataForm.lightingConclusion"
                placeholder="请填写验收结论"
                :disabled="judgeWrite('lightingConclusion')"
                rows="4"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12" v-if="judgeShow('lightingOperator')">
            <a-form-item label="操作者" name="lightingOperators">
              <jnpf-user-select
                v-model:value="state.dataForm.lightingOperators"
                placeholder="请选择操作者"
                :disabled="judgeWrite('lightingOperators')"
                :allowClear="true"
                selectType="all"
                :multiple="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingOperatorDate')">
            <a-form-item label="操作者日期" name="lightingOperatorDate">
              <jnpf-date-picker v-model:value="state.dataForm.lightingOperatorDate" placeholder="操作者日期"
                                :disabled="judgeWrite('lightingOperatorDate')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingTechnicalLeader')">
            <a-form-item label="技术负责人" name="lightingTechnicalLeaders">
              <jnpf-user-select
                v-model:value="state.dataForm.lightingTechnicalLeaders"
                placeholder="请选择技术负责人"
                :disabled="judgeWrite('lightingTechnicalLeaders')"
                :allowClear="true"
                selectType="all"
                :multiple="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingTechnicalLeaderDate')">
            <a-form-item label="技术负责人日期" name="lightingTechnicalLeaderDate">
              <jnpf-date-picker v-model:value="state.dataForm.lightingTechnicalLeaderDate" placeholder="技术负责人日期"
                                :disabled="judgeWrite('lightingTechnicalLeaderDate')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingSafetyInspector')">
            <a-form-item label="安全检查员" name="lightingSafetyInspectors">
              <jnpf-user-select
                v-model:value="state.dataForm.lightingSafetyInspectors"
                placeholder="请选择安全检查员"
                :disabled="judgeWrite('lightingSafetyInspectors')"
                :allowClear="true"
                selectType="all"
                :multiple="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingSafetyInspectorDate')">
            <a-form-item label="安全检查员日期" name="lightingSafetyInspectorDate">
              <jnpf-date-picker v-model:value="state.dataForm.lightingSafetyInspectorDate" placeholder="安全检查员日期"
                                :disabled="judgeWrite('lightingSafetyInspectorDate')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingSupervisor')">
            <a-form-item label="监理/工程公司" name="lightingSupervisor">
              <a-input v-model:value="state.dataForm.lightingSupervisor" placeholder="监理/工程公司"
                       :disabled="judgeWrite('lightingSupervisor')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('lightingSupervisorDate')">
            <a-form-item label="监理/工程公司日期" name="lightingSupervisorDate">
              <jnpf-date-picker v-model:value="state.dataForm.lightingSupervisorDate" placeholder="监理/工程公司日期"
                                :disabled="judgeWrite('lightingSupervisorDate')"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, reactive, ref, onMounted} from 'vue';
import {useFlowForm} from "/@/views/workFlow/workFlowForm/hooks/useFlowForm";
import type {FormInstance} from "ant-design-vue";
import JnpfDatePicker from "/@/components/Jnpf/DatePicker/src/DatePicker.vue";
import JnpfOrganizeSelect from "/@/components/Jnpf/Organize/src/OrganizeSelect.vue";
import JnpfUserSelect from "/@/components/Jnpf/Organize/src/UserSelect.vue";
import {useMessage} from '/@/hooks/web/useMessage'
import dayjs from 'dayjs';

const props = defineProps(['config']);
const emit = defineEmits(['register', 'reload', 'created']);
const {createMessage} = useMessage();

// 定义表单数据类型
interface FormData {
  // 项目信息
  projectName: string;
  id: string;

  // 临时照明安装验收记录
  lightingSchemeNumber: string;
  lightingResponsibleTeam: string;
  lightingResponsibleTeams: any[];
  lightingArea: string;
  lightingInspectionDate: Date | null;
  lightingInstallationTime: Date | null;
  lightingRemovalTime: Date | null;
  lightingInstallationStaffs: any[];
  lightingRemovalStaffs: any[];
  lightingOperators: any[];
  lightingOperatorDate: Date | null;
  lightingTechnicalLeaders: any[];
  lightingTechnicalLeaderDate: Date | null;
  lightingSafetyInspectors: any[];
  lightingSafetyInspectorDate: Date | null;
  lightingSupervisor: string;
  lightingSupervisorDate: Date | null;
  lightingConclusion: string;
  lightingInspectionContentList: { id: number; content: string; result: string }[];
}

// 定义表单验证规则类型
interface FormRules {
  projectName: any[];
  lightingSchemeNumber: any[];
  lightingResponsibleTeams: any[];
  lightingArea: any[];
  lightingInspectionDate: any[];
  lightingInstallationStaffs: any[];
  lightingRemovalStaffs: any[];
  lightingOperators: any[];
  lightingTechnicalLeaders: any[];
  lightingSafetyInspectors: any[];
  lightingConclusion: any[];
  lightingInspectionContentList: any[];
}

// 定义状态接口
interface State {
  dataForm: FormData;
  dataRule: FormRules;
}

// 初始化状态
const state = reactive<State>({
  dataForm: {
    // 项目信息
    projectName: '',
    id: '',

    // 临时照明安装验收记录
    lightingSchemeNumber: '',
    lightingResponsibleTeam: '',
    lightingResponsibleTeams: [],
    lightingArea: '',
    lightingInspectionDate: dayjs().startOf('day').toDate(),
    lightingInstallationTime: dayjs().startOf('day').toDate(),
    lightingRemovalTime: dayjs().startOf('day').toDate(),
    lightingInstallationStaffs: [],
    lightingRemovalStaffs: [],
    lightingOperators: [],
    lightingOperatorDate: null,
    lightingTechnicalLeaders: [],
    lightingTechnicalLeaderDate: null,
    lightingSafetyInspectors: [],
    lightingSafetyInspectorDate: null,
    lightingSupervisor: 'N/A',
    lightingSupervisorDate: null,
    lightingConclusion: '',
    lightingInspectionContentList: [
      { id: 1, content: '照明安装高度', result: '' },
      { id: 2, content: '照明安装路径选择', result: '' },
      { id: 3, content: '电缆外观检查', result: '' },
      { id: 4, content: '灯具外观检查', result: '' },
      { id: 5, content: '照明照度检查', result: '' },
      { id: 6, content: '照明灯具选型', result: '' },
      { id: 7, content: '照明线路保护措施', result: '' },
      { id: 8, content: '照明端接检查', result: '' },
      { id: 9, content: '照明电缆绝缘检查', result: '' },
      { id: 10, content: '通电试验', result: '' },
    ],
  },
  dataRule: {
    projectName: [
      { required: true, message: '请输入项目名称', trigger: 'blur' }
    ],
    lightingSchemeNumber: [
      { required: true, message: '请输入临时照明编号', trigger: 'blur' }
    ],
    lightingResponsibleTeams: [
      { required: true, type: 'array', message: '请选择责任班组', trigger: 'change' }
    ],
    lightingArea: [
      { required: true, message: '请输入临时照明区域', trigger: 'blur' }
    ],
    lightingInspectionDate: [
      { required: true, message: '请选择检查日期', trigger: 'change' }
    ],
    lightingInstallationStaffs: [
      {
        validator: (_, value) => {
          if (value.length !== 2) {
            return Promise.reject('请选择2名安装施工人员');
          }
          return Promise.resolve();
        },
        trigger: 'change'
      }
    ],
    lightingRemovalStaffs: [
      {
        validator: (_, value) => {
          if (value.length !== 2) {
            return Promise.reject('请选择2名拆除施工人员');
          }
          return Promise.resolve();
        },
        trigger: 'change'
      }
    ],
    lightingOperators: [
      { required: true, message: '请选择临时照明操作者', trigger: 'change' }
    ],
    lightingTechnicalLeaders: [
      { required: true, message: '请选择临时照明技术负责人', trigger: 'change' }
    ],
    lightingSafetyInspectors: [
      { required: true, message: '请选择临时照明安全检查员', trigger: 'change' }
    ],
    lightingConclusion: [
      { required: true, message: '请填写临时照明验收结论', trigger: 'blur' }
    ],
    lightingInspectionContentList: [
      {
        validator: (_, value) => {
          if (!value || value.length === 0) {
            return Promise.reject('至少填写一项检查内容');
          }
          const invalidItems = value.filter(item => !item.result);
          if (invalidItems.length > 0) {
            return Promise.reject('请填写所有检查结果');
          }
          return Promise.resolve();
        },
        trigger: 'change'
      }
    ],
  }
});

// 声明表单引用
const projectInfoFormRef = ref<FormInstance>();
const lightingFormRef = ref<FormInstance>();

// 临时照明表单配置
const lightingForm = reactive({
  getInspectionContentColumns: computed(() => [
    { title: '序号', dataIndex: 'id', key: 'id', width: 50, align: 'center', customRender: ({record}) => record.id },
    { title: '检查内容', dataIndex: 'content', key: 'content', width: 200, align: 'center' },
    { title: '检查结果', dataIndex: 'result', key: 'result', width: 200, align: 'center' },
  ]),
  canEdit: computed(() => !props.config.disabled && !judgeWrite('lightingInspectionContent')),
});

const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
  config: props.config,
  selfState: state,
  emit,
  formRef: projectInfoFormRef,
  // @ts-ignore: 忽略类型错误
  afterFetch: (data) => {
    // 处理数据获取后的逻辑
  },
} as any);

defineExpose({dataFormSubmit});

// 验证人员选择
function validateTwoPersons(field: string) {
  const formData = state.dataForm;
  const fieldKey = field.split('-')[1]; // 解析出installationStaffs或removalStaffs
  const value = formData[fieldKey];

  if (Array.isArray(value) && value.length !== 2) {
    createMessage.warning(`${fieldKey === 'installationStaffs' ? '安装' : '拆除'}施工人员必须选择两人`);
  }
}

// 照明检查项操作
function handleDelLightingContent(id: number) {
  state.dataForm.lightingInspectionContentList = state.dataForm.lightingInspectionContentList.filter(item => item.id !== id);
}

function addLightingContent() {
  const maxId = state.dataForm.lightingInspectionContentList.reduce((max, item) => Math.max(max, item.id), 0);
  state.dataForm.lightingInspectionContentList.push({ id: maxId + 1, content: '', result: '' });
}

// 表单提交
async function handleSubmit() {
  try {
    // 验证所有表单
    await projectInfoFormRef.value?.validate();
    await lightingFormRef.value?.validate();

    // 提交表单数据
    await dataFormSubmit(state.dataForm);
    createMessage.success('提交成功');
  } catch (error) {
    console.error('表单验证失败', error);
    createMessage.error('表单验证失败，请检查必填项');
  }
}

// 重置表单
function handleReset() {
  // 重置表单验证状态
  projectInfoFormRef.value?.resetFields();
  lightingFormRef.value?.resetFields();

  // 重置dataForm数据到初始状态
  state.dataForm = {
    // 项目信息
    projectName: '',
    id: '',

    // 临时照明安装验收记录
    lightingSchemeNumber: '',
    lightingResponsibleTeam: '',
    lightingResponsibleTeams: [],
    lightingArea: '',
    lightingInspectionDate: dayjs().startOf('day').toDate(),
    lightingInstallationTime: dayjs().startOf('day').toDate(),
    lightingRemovalTime: dayjs().startOf('day').toDate(),
    lightingInstallationStaffs: [],
    lightingRemovalStaffs: [],
    lightingOperators: [],
    lightingOperatorDate: null,
    lightingTechnicalLeaders: [],
    lightingTechnicalLeaderDate: null,
    lightingSafetyInspectors: [],
    lightingSafetyInspectorDate: null,
    lightingSupervisor: 'N/A',
    lightingSupervisorDate: null,
    lightingConclusion: '',
    lightingInspectionContentList: [
      { id: 1, content: '照明安装高度', result: '' },
      { id: 2, content: '照明安装路径选择', result: '' },
      { id: 3, content: '电缆外观检查', result: '' },
      { id: 4, content: '灯具外观检查', result: '' },
      { id: 5, content: '照明照度检查', result: '' },
      { id: 6, content: '照明灯具选型', result: '' },
      { id: 7, content: '照明线路保护措施', result: '' },
      { id: 8, content: '照明端接检查', result: '' },
      { id: 9, content: '照明电缆绝缘检查', result: '' },
      { id: 10, content: '通电试验', result: '' },
    ],
  };
}

onMounted(() => {
  init();
});
</script>
