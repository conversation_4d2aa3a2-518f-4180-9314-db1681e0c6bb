<template>
  <div class="flow-form">
    <!-- 项目信息部分 -->
    <a-form
      :colon="false"
      :labelCol="{ style: { width: '200px' } }"
      :model="state.dataForm"
      :rules="state.dataRule"
      ref="projectInfoFormRef"
      :disabled="config.disabled"
    >
      <a-row>
        <a-col :span="12" v-if="judgeShow('projectName')">
          <a-form-item label="项目名称" name="projectName">
            <a-input v-model:value="state.dataForm.projectName" placeholder="项目名称" :disabled="judgeWrite('projectName')"/>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <!-- 配电箱安装验收记录 -->
    <div class="record-section" style="margin-top: 20px;" v-if="judgeShow('distributionBox')">
      <h2 class="record-title">安装验收记录</h2>
      <a-form
        :colon="false"
        :labelCol="{ style: { width: '200px' } }"
        :model="state.dataForm"
        :rules="state.dataRule"
        ref="distributionBoxFormRef"
        :disabled="config.disabled"
      >
        <a-row>
          <a-col :span="12" v-if="judgeShow('distributionBoxSchemeNumber')">
            <a-form-item label="编号" name="distributionBoxSchemeNumber">
              <a-input v-model:value="state.dataForm.distributionBoxSchemeNumber" placeholder="编号" :disabled="judgeWrite('distributionBoxSchemeNumber')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxResponsibleTeam')">
            <a-form-item label="责任班组" name="distributionBoxResponsibleTeams">
              <jnpf-organize-select
                v-model:value="state.dataForm.distributionBoxResponsibleTeams"
                placeholder="请选择责任班组"
                :disabled="judgeWrite('distributionBoxResponsibleTeam')"
                :allowClear="true"
                :multiple="true"
                selectType="all"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxArea')">
            <a-form-item label="区域" name="distributionBoxArea">
              <a-input v-model:value="state.dataForm.distributionBoxArea" placeholder="区域" :disabled="judgeWrite('distributionBoxArea')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxInspectionDate')">
            <a-form-item label="检查日期" name="distributionBoxInspectionDate">
              <jnpf-date-picker v-model:value="state.dataForm.distributionBoxInspectionDate" placeholder="检查日期"
                                :disabled="judgeWrite('distributionBoxInspectionDate')"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-col :span="12" v-if="judgeShow('distributionBoxType')">
          <a-form-item label="类型" name="distributionBoxType">
            <a-select
              v-model:value="state.dataForm.distributionBoxType"
              placeholder="请选择类型"
              :disabled="judgeWrite('distributionBoxType')"
              :allow-clear="true"
            >
              <a-select-option
                v-for="option in distributionBoxTypeOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 配电箱检查内容表格 -->
        <a-row v-if="judgeShow('distributionBoxInspectionContent')">
          <a-col :span="24">
            <a-card title="检查内容与结果" style="margin-top: 20px;">
              <a-table
                :data-source="state.dataForm.distributionBoxInspectionContentList"
                :columns="distributionBoxForm.getInspectionContentColumns"
                size="small"
                :pagination="false"
              >
                <template #headerCell="{ column }">
                  <span class="required-sign"
                        v-if="judgeRequired(`distributionBox-${column.key}`)">*</span>{{ column.title }}
                </template>
                <template #bodyCell="{ column, record }">
                  <a-input
                    v-model:value="record[column.dataIndex]"
                    :disabled="judgeWrite('distributionBoxInspectionContent')"
                    :placeholder="column.dataIndex === 'result' ? '' : column.title"
                    :style="{ textAlign: 'center' }"
                  />
                </template>
              </a-table>
              <div class="table-add-action" @click="addDistributionBoxContent" v-if="distributionBoxForm.canEdit">
                <a-button type="primary" icon="icon-ym icon-ym-btn-add">新增检查项</a-button>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12" v-if="judgeShow('distributionBoxInstallationTime')">
            <a-form-item label="安装时间" name="distributionBoxInstallationTime">
              <jnpf-date-picker
                v-model:value="state.dataForm.distributionBoxInstallationTime"
                placeholder="安装时间"
                :disabled="judgeWrite('distributionBoxInstallationTime')"
                style="width:90%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxInstallationStaff')">
            <a-form-item label="安装施工人员（双人）" name="distributionBoxInstallationStaffs">
              <jnpf-user-select
                v-model:value="state.dataForm.distributionBoxInstallationStaffs"
                placeholder="请选择2名安装施工人员"
                :disabled="judgeWrite('distributionBoxInstallationStaffs')"
                :allowClear="true"
                :multiple="true"
                :max-tag-count="2"
                selectType="all"
                @change="validateTwoPersons('distributionBox-installationStaffs')"
              />
              <template #error>
                <span class="error-tip">需选择2名施工人员</span>
              </template>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12" v-if="judgeShow('distributionBoxRemovalTime')">
            <a-form-item label="拆除时间" name="distributionBoxRemovalTime">
              <jnpf-date-picker
                v-model:value="state.dataForm.distributionBoxRemovalTime"
                placeholder="拆除时间"
                :disabled="judgeWrite('distributionBoxRemovalTime')"
                style="width:90%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxRemovalStaff')">
            <a-form-item label="拆除施工人员（双人）" name="distributionBoxRemovalStaffs">
              <jnpf-user-select
                v-model:value="state.dataForm.distributionBoxRemovalStaffs"
                placeholder="请选择2名拆除施工人员"
                :disabled="judgeWrite('distributionBoxRemovalStaffs')"
                :allowClear="true"
                :multiple="true"
                :max-tag-count="2"
                selectType="all"
                @change="validateTwoPersons('distributionBox-removalStaffs')"
              />
              <template #error>
                <span class="error-tip">需选择2名施工人员</span>
              </template>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24" v-if="judgeShow('distributionBoxConclusion')">
            <a-form-item label="验收结论" name="distributionBoxConclusion">
              <a-textarea
                v-model:value="state.dataForm.distributionBoxConclusion"
                placeholder="请填写验收结论"
                :disabled="judgeWrite('distributionBoxConclusion')"
                rows="4"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12" v-if="judgeShow('distributionBoxOperator')">
            <a-form-item label="操作者" name="distributionBoxOperators">
              <jnpf-user-select
                v-model:value="state.dataForm.distributionBoxOperators"
                placeholder="请选择操作者"
                :disabled="judgeWrite('distributionBoxOperators')"
                :allowClear="true"
                selectType="all"
                :multiple="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxOperatorDate')">
            <a-form-item label="操作者日期" name="distributionBoxOperatorDate">
              <jnpf-date-picker v-model:value="state.dataForm.distributionBoxOperatorDate" placeholder="操作者日期"
                                :disabled="judgeWrite('distributionBoxOperatorDate')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxTechnicalLeader')">
            <a-form-item label="技术负责人" name="distributionBoxTechnicalLeaders">
              <jnpf-user-select
                v-model:value="state.dataForm.distributionBoxTechnicalLeaders"
                placeholder="请选择技术负责人"
                :disabled="judgeWrite('distributionBoxTechnicalLeaders')"
                :allowClear="true"
                selectType="all"
                :multiple="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxTechnicalLeaderDate')">
            <a-form-item label="技术负责人日期" name="distributionBoxTechnicalLeaderDate">
              <jnpf-date-picker v-model:value="state.dataForm.distributionBoxTechnicalLeaderDate" placeholder="技术负责人日期"
                                :disabled="judgeWrite('distributionBoxTechnicalLeaderDate')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxSafetyInspector')">
            <a-form-item label="安全检查员" name="distributionBoxSafetyInspectors">
              <jnpf-user-select
                v-model:value="state.dataForm.distributionBoxSafetyInspectors"
                placeholder="请选择安全检查员"
                :disabled="judgeWrite('distributionBoxSafetyInspectors')"
                :allowClear="true"
                selectType="all"
                :multiple="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxSafetyInspectorDate')">
            <a-form-item label="安全检查员日期" name="distributionBoxSafetyInspectorDate">
              <jnpf-date-picker v-model:value="state.dataForm.distributionBoxSafetyInspectorDate" placeholder="安全检查员日期"
                                :disabled="judgeWrite('distributionBoxSafetyInspectorDate')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxSupervisor')">
            <a-form-item label="监理/工程公司" name="distributionBoxSupervisor">
              <a-input v-model:value="state.dataForm.distributionBoxSupervisor" placeholder="监理/工程公司"
                       :disabled="judgeWrite('distributionBoxSupervisor')"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="judgeShow('distributionBoxSupervisorDate')">
            <a-form-item label="监理/工程公司日期" name="distributionBoxSupervisorDate">
              <jnpf-date-picker v-model:value="state.dataForm.distributionBoxSupervisorDate" placeholder="监理/工程公司日期"
                                :disabled="judgeWrite('distributionBoxSupervisorDate')"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, reactive, ref, onMounted} from 'vue';
import {useFlowForm} from "/@/views/workFlow/workFlowForm/hooks/useFlowForm";
import type {FormInstance} from "ant-design-vue";
import JnpfDatePicker from "/@/components/Jnpf/DatePicker/src/DatePicker.vue";
import JnpfOrganizeSelect from "/@/components/Jnpf/Organize/src/OrganizeSelect.vue";
import JnpfUserSelect from "/@/components/Jnpf/Organize/src/UserSelect.vue";
import {useMessage} from '/@/hooks/web/useMessage'
import dayjs from 'dayjs';

const props = defineProps(['config']);
const emit = defineEmits(['register', 'reload', 'created']);
const {createMessage} = useMessage();

const DistributionBoxTypeMap = {
  PRIMARY: '一级配电箱',
  SECONDARY: '二级配电箱',
  TERTIARY: '三级配电箱',
  TOWER_CRANE: '塔吊专用箱',
  LIGHTING_CONTROL: '照明控制箱',
};

const distributionBoxTypeOptions = Object.entries(DistributionBoxTypeMap).map(([value, label]) => ({
  value,
  label,
}));

interface State {
  dataForm: {
    // 项目信息
    projectName: string;
    id: string;

    // 配电箱安装验收记录
    distributionBoxSchemeNumber: string;
    distributionBoxResponsibleTeam: string;
    distributionBoxResponsibleTeams: any[];
    distributionBoxArea: string;
    distributionBoxInspectionDate: Date;
    distributionBoxInstallationTime: Date;
    distributionBoxRemovalTime: Date;
    distributionBoxInstallationStaffs: any[];
    distributionBoxRemovalStaffs: any[];
    distributionBoxOperators: any[];
    distributionBoxOperatorDate: Date | null;
    distributionBoxTechnicalLeaders: any[];
    distributionBoxTechnicalLeaderDate: Date | null;
    distributionBoxSafetyInspectors: any[];
    distributionBoxSafetyInspectorDate: Date | null;
    distributionBoxSupervisor: string;
    distributionBoxSupervisorDate: Date | null;
    distributionBoxConclusion: string;
    distributionBoxInspectionContentList: { id: number; content: string; result: string }[];
    distributionBoxType: string;
  };
  dataRule: Record<string, any[]>;
}

const state = reactive<State>({
  dataForm: {
    // 项目信息
    projectName: '',
    id: '',

    // 配电箱安装验收记录
    distributionBoxSchemeNumber: '',
    distributionBoxResponsibleTeam: '',
    distributionBoxResponsibleTeams: [],
    distributionBoxArea: '',
    distributionBoxInspectionDate: dayjs().startOf('day').toDate(),
    distributionBoxInstallationTime: dayjs().startOf('day').toDate(),
    distributionBoxRemovalTime: dayjs().startOf('day').toDate(),
    distributionBoxInstallationStaffs: [],
    distributionBoxRemovalStaffs: [],
    distributionBoxOperators: [],
    distributionBoxOperatorDate: null,
    distributionBoxTechnicalLeaders: [],
    distributionBoxTechnicalLeaderDate: null,
    distributionBoxSafetyInspectors: [],
    distributionBoxSafetyInspectorDate: null,
    distributionBoxSupervisor: 'N/A',
    distributionBoxSupervisorDate: null,
    distributionBoxConclusion: '',
    distributionBoxInspectionContentList: [
      { id: 1, content: '漏电保护器动作情况', result: '' },
      { id: 2, content: '配电箱插座检查', result: '' },
      { id: 3, content: '电缆外观检查', result: '' },
      { id: 4, content: '保护性接地、接零', result: '' },
      { id: 5, content: '配电箱安装位置', result: '' },
      { id: 6, content: '配电箱外观检查', result: '' },
      { id: 7, content: '一机、一闸、一保护', result: '' },
      { id: 8, content: '螺丝紧固状态', result: '' },
      { id: 9, content: '配电箱编号及标识', result: '' },
      { id: 10, content: '通电试验', result: '' },
    ],
    distributionBoxType: 'PRIMARY',
  },
  dataRule: {
    projectName: [
      { required: true, message: '请输入项目名称', trigger: 'blur' }
    ],
    distributionBoxSchemeNumber: [
      { required: true, message: '请输入配电箱编号', trigger: 'blur' }
    ],
    distributionBoxResponsibleTeams: [
      { required: true, type: 'array', message: '请选择责任班组', trigger: 'change' }
    ],
    distributionBoxArea: [
      { required: true, message: '请输入配电箱区域', trigger: 'blur' }
    ],
    distributionBoxInspectionDate: [
      { required: true, message: '请选择检查日期', trigger: 'change' }
    ],
    distributionBoxType: [
      { required: true, message: '请选择配电箱类型', trigger: 'change' }
    ],
    distributionBoxInstallationStaffs: [
      {
        validator: (_, value) => {
          if (value.length !== 2) {
            return Promise.reject('请选择2名安装施工人员');
          }
          return Promise.resolve();
        },
        trigger: 'change'
      }
    ],
    distributionBoxRemovalStaffs: [
      {
        validator: (_, value) => {
          if (value.length !== 2) {
            return Promise.reject('请选择2名拆除施工人员');
          }
          return Promise.resolve();
        },
        trigger: 'change'
      }
    ],
    distributionBoxOperators: [
      { required: true, message: '请选择配电箱操作者', trigger: 'change' }
    ],
    distributionBoxTechnicalLeaders: [
      { required: true, message: '请选择配电箱技术负责人', trigger: 'change' }
    ],
    distributionBoxSafetyInspectors: [
      { required: true, message: '请选择配电箱安全检查员', trigger: 'change' }
    ],
    distributionBoxConclusion: [
      { required: true, message: '请填写配电箱验收结论', trigger: 'blur' }
    ],
    distributionBoxInspectionContentList: [
      {
        validator: (_, value) => {
          if (!value || value.length === 0) {
            return Promise.reject('至少填写一项检查内容');
          }
          const invalidItems = value.filter(item => !item.result);
          if (invalidItems.length > 0) {
            return Promise.reject('请填写所有检查结果');
          }
          return Promise.resolve();
        },
        trigger: 'change'
      }
    ],
  }
});

// 声明表单引用
const projectInfoFormRef = ref<FormInstance>();
const distributionBoxFormRef = ref<FormInstance>();

// 配电箱表单配置
const distributionBoxForm = reactive({
  getInspectionContentColumns: computed(() => [
    { title: '序号', dataIndex: 'id', key: 'id', width: 50, align: 'center', customRender: ({record}) => record.id },
    { title: '检查内容', dataIndex: 'content', key: 'content', width: 200, align: 'center' },
    { title: '检查结果', dataIndex: 'result', key: 'result', width: 200, align: 'center' },
  ]),
  canEdit: computed(() => !props.config.disabled && !judgeWrite('distributionBox-inspectionContentList')),
});

const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
  config: props.config,
  selfState: state,
  emit,
  formRef: projectInfoFormRef,
  // @ts-ignore: 忽略类型错误
  afterFetch: (data) => {
    console.log('afterFetch called with data:', data);

    // 确保数据存在
    if (!data) return;

    // 合并项目信息
    if (data.projectName) state.dataForm.projectName = data.projectName;

    // 合并配电箱信息
    if (data.distributionBoxSchemeNumber) state.dataForm.distributionBoxSchemeNumber = data.distributionBoxSchemeNumber;
    if (data.distributionBoxResponsibleTeams) state.dataForm.distributionBoxResponsibleTeams = data.distributionBoxResponsibleTeams;
    if (data.distributionBoxArea) state.dataForm.distributionBoxArea = data.distributionBoxArea;
    if (data.distributionBoxInspectionDate) state.dataForm.distributionBoxInspectionDate = data.distributionBoxInspectionDate;
    if (data.distributionBoxType) state.dataForm.distributionBoxType = data.distributionBoxType;
    if (data.distributionBoxInstallationTime) state.dataForm.distributionBoxInstallationTime = data.distributionBoxInstallationTime;
    if (data.distributionBoxRemovalTime) state.dataForm.distributionBoxRemovalTime = data.distributionBoxRemovalTime;
    if (data.distributionBoxInstallationStaffs) state.dataForm.distributionBoxInstallationStaffs = data.distributionBoxInstallationStaffs;
    if (data.distributionBoxRemovalStaffs) state.dataForm.distributionBoxRemovalStaffs = data.distributionBoxRemovalStaffs;
    if (data.distributionBoxOperators) state.dataForm.distributionBoxOperators = data.distributionBoxOperators;
    if (data.distributionBoxOperatorDate) state.dataForm.distributionBoxOperatorDate = data.distributionBoxOperatorDate;
    if (data.distributionBoxTechnicalLeaders) state.dataForm.distributionBoxTechnicalLeaders = data.distributionBoxTechnicalLeaders;
    if (data.distributionBoxTechnicalLeaderDate) state.dataForm.distributionBoxTechnicalLeaderDate = data.distributionBoxTechnicalLeaderDate;
    if (data.distributionBoxSafetyInspectors) state.dataForm.distributionBoxSafetyInspectors = data.distributionBoxSafetyInspectors;
    if (data.distributionBoxSafetyInspectorDate) state.dataForm.distributionBoxSafetyInspectorDate = data.distributionBoxSafetyInspectorDate;
    if (data.distributionBoxSupervisor) state.dataForm.distributionBoxSupervisor = data.distributionBoxSupervisor;
    if (data.distributionBoxSupervisorDate) state.dataForm.distributionBoxSupervisorDate = data.distributionBoxSupervisorDate;
    if (data.distributionBoxConclusion) state.dataForm.distributionBoxConclusion = data.distributionBoxConclusion;

    // 处理检查内容列表
    if (data.distributionBoxInspectionContentList && data.distributionBoxInspectionContentList.length > 0) {
      // 合并检查内容列表，保留原有结构，更新内容和结果
      const newContentList = data.distributionBoxInspectionContentList;
      const mergedContentList = [...state.dataForm.distributionBoxInspectionContentList];

      // 用新数据更新现有项
      newContentList.forEach(newItem => {
        const existingIndex = mergedContentList.findIndex(item => item.id === newItem.id);
        if (existingIndex !== -1) {
          mergedContentList[existingIndex] = {...mergedContentList[existingIndex], ...newItem};
        } else {
          // 如果新项不存在，则添加它
          mergedContentList.push({...newItem, id: mergedContentList.length + 1});
        }
      });

      state.dataForm.distributionBoxInspectionContentList = mergedContentList;
    }

    console.log('Updated dataForm:', state.dataForm);
  },
} as any);

defineExpose({dataFormSubmit});

// 验证人员选择
function validateTwoPersons(field: string) {
  const formData = state.dataForm;
  const fieldKey = field.split('-')[1]; // 解析出installationStaffs或removalStaffs
  const value = formData[fieldKey];

  if (Array.isArray(value) && value.length !== 2) {
    createMessage.warning(`${fieldKey === 'installationStaffs' ? '安装' : '拆除'}施工人员必须选择两人`);
    // 不自动截断，让用户自己选择
    // formData[fieldKey] = value.slice(0, 2);
  }
}

// 配电箱检查项操作
function handleDelDistributionBoxContent(id: number) {
  state.dataForm.distributionBoxInspectionContentList = state.dataForm.distributionBoxInspectionContentList.filter(item => item.id !== id);
}

function addDistributionBoxContent() {
  const maxId = state.dataForm.distributionBoxInspectionContentList.reduce((max, item) => Math.max(max, item.id), 0);
  state.dataForm.distributionBoxInspectionContentList.push({ id: maxId + 1, content: '', result: '' });
}

// 表单提交
async function handleSubmit() {
  try {
    // 验证所有表单
    await projectInfoFormRef.value?.validate();
    await distributionBoxFormRef.value?.validate();

    // 提交表单数据
    await dataFormSubmit(state.dataForm);
    createMessage.success('提交成功');
  } catch (error) {
    console.error('表单验证失败', error);
    createMessage.error('表单验证失败，请检查必填项');
  }
}

// 重置表单
function handleReset() {
  // 重置表单验证状态
  projectInfoFormRef.value?.resetFields();
  distributionBoxFormRef.value?.resetFields();

  // 重置dataForm数据到初始状态
  state.dataForm = {
    // 项目信息
    projectName: '',
    id: '',

    // 配电箱安装验收记录
    distributionBoxSchemeNumber: '',
    distributionBoxResponsibleTeam: '',
    distributionBoxResponsibleTeams: [],
    distributionBoxArea: '',
    distributionBoxInspectionDate: dayjs().startOf('day').toDate(),
    distributionBoxInstallationTime: dayjs().startOf('day').toDate(),
    distributionBoxRemovalTime: dayjs().startOf('day').toDate(),
    distributionBoxInstallationStaffs: [],
    distributionBoxRemovalStaffs: [],
    distributionBoxOperators: [],
    distributionBoxOperatorDate: null,
    distributionBoxTechnicalLeaders: [],
    distributionBoxTechnicalLeaderDate: null,
    distributionBoxSafetyInspectors: [],
    distributionBoxSafetyInspectorDate: null,
    distributionBoxSupervisor: 'N/A',
    distributionBoxSupervisorDate: null,
    distributionBoxConclusion: '',
    distributionBoxInspectionContentList: [
      { id: 1, content: '漏电保护器动作情况', result: '' },
      { id: 2, content: '配电箱插座检查', result: '' },
      { id: 3, content: '电缆外观检查', result: '' },
      { id: 4, content: '保护性接地、接零', result: '' },
      { id: 5, content: '配电箱安装位置', result: '' },
      { id: 6, content: '配电箱外观检查', result: '' },
      { id: 7, content: '一机、一闸、一保护', result: '' },
      { id: 8, content: '螺丝紧固状态', result: '' },
      { id: 9, content: '配电箱编号及标识', result: '' },
      { id: 10, content: '通电试验', result: '' },
    ],
    distributionBoxType: 'PRIMARY',
  };
}

onMounted(() => {
  init();
});
</script>
