<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="1000px" class="full-drawer" title="配电箱验收详情" @close="handleClose">
    <div class="fa-full fa-pl12 fa-pr12 fa-pb12 fa-scroll-auto-y">
      <a-tabs class="jnpf-content-wrapper-tabs" destroyInactiveTabPane>
        <a-tab-pane key="1" tab="材料封面">
          <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printInfo(1)">打印封面</a-button>
          <div ref="coverRef">
            <DistributionBoxCoverComponent :distribution-box-cover-info="distributionBoxInfo"/>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import DistributionBoxCoverComponent from "/@/views/tpsr/componentMaterial/distributionBoxManage/distributionComponent.vue";
import {BasicDrawer, useDrawerInner} from '/@/components/Drawer';
import {ref} from "vue";
import {distributionBoxCoverInfoApi} from "/@/api";
import {printHtml} from "/@/utils/printUtils";

defineOptions({name: 'DistributionBoxDetailDrawer'});

const [registerDrawer, { changeLoading, closeDrawer }] = useDrawerInner(init);


const distributionBoxId = ref('')
const distributionBoxInfo = ref<any>({})

const coverRef = ref();
const onePageRef = ref();

async function init(data) {
  distributionBoxId.value = data.distributionBoxId
  await setDistributionBoxInfo();
}

async function setDistributionBoxInfo() {
  if (distributionBoxId.value) {
    changeLoading(true);
    try {
      const res = await distributionBoxCoverInfoApi.getDeliverCoverInfo(distributionBoxId.value);
      if (res.data) {
        distributionBoxInfo.value = res.data;
      }
    } finally {
      changeLoading(false);
    }
  }
}

function printInfo(paneIndex: number) {
  if (paneIndex === 1) {
    printHtml(coverRef.value);
  } else if (paneIndex === 2) {
    printHtml(onePageRef.value);
  }
}

function handleClose() {
  distributionBoxId.value = '';
  distributionBoxInfo.value = {};
  closeDrawer();
}
</script>

<style lang="less" scoped>
.full-drawer {
  height: 100%;

  .fa-scroll-auto-y {
    height: calc(100% - 50px);
    overflow-y: auto;
  }

  .jnpf-content-wrapper-tabs {
    :deep(.ant-tabs-content) {
      height: calc(100% - 55px);
      overflow-y: auto;
    }
  }
}
</style>
