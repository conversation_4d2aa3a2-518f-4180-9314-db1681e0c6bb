<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'distributionBoxSchemeNumber'">
              <span>{{ record.distributionBoxSchemeNumber || '-' }}</span>
            </template>
            <template v-if="column.key === 'distributionBoxType'">
              <!-- 类型代码转为中文 -->
              <span>{{ getDistributionBoxTypeText(record.distributionBoxType) }}</span>
            </template>
            <template v-if="column.key === 'distributionBoxArea'">
              <span>{{ record.distributionBoxArea || '-' }}</span>
            </template>
            <template v-if="column.key === 'distributionBoxInspectionDate'">
              {{ formatDate(record.distributionBoxInspectionDate) }}
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
            <template v-if="column.key === 'creatorTime'">
              {{ formatDate(record.creatorTime) }}
            </template>
            <template v-if="column.key === 'inspectionDate'">
              {{ formatDate(record.inspectionDate) }}
            </template>
            <template v-if="column.key === 'responsibleTeam'">
              <span>{{ record.responsibleTeam || '-' }}</span>
            </template>
          </template>
        </BasicTable>
      </div>

      <FaFlowCube ref="flowRef" :flow-en-code="flowCode" @reload="reload" />
      <DistributionBoxDetailDrawer @register="registerDetailDrawer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from "vue";
import { ActionItem, BasicTable, TableAction, useTable } from "/@/components/Table";
import { distributionBoxAcceptanceApi } from "/@/api";
import { useMessage } from "/@/hooks/web/useMessage";
import {
  genFlowDeleteBtn,
  genFlowDetailBtn,
  genFlowEditBtn,
  genQuerySearch,
} from '/@/utils/tableUtils';
import FaFlowCube from "/@/components/Fa/common/src/FaFlowCube.vue";
import { FLOW_OP_TYPE } from "/@/enums/zzEnums";
import { useDrawer } from "/@/components/Drawer";
import DistributionBoxDetailDrawer from "/@/views/tpsr/checkAcceptance/distributionBox/DistributionBoxDetailDrawer.vue";
import dayjs from "dayjs";

// 定义配电箱类型映射
const DISTRIBUTION_BOX_TYPES = {
  PRIMARY: "一级配电箱",
  SECONDARY: "二级配电箱",
  TERTIARY: "三级配电箱",
  TOWER_CRANE: "塔吊专用箱",
  LIGHTING_CONTROL: "照明控制箱",
};

defineOptions({ name: 'tpsr-distributionBox-checkAcceptance' });

const flowCode = 'distributionBoxAcceptance';
const flowRef = ref<any>();

const props = defineProps(['distributionBoxAcceptance']);

// 表格列配置
const distributionBoxAcceptanceRecordCol = computed(() => [
  { title: '项目名称', dataIndex: 'projectName', key: 'projectName', width: 150 },
  { title: '审批状态', dataIndex: 'currentState', key: 'currentState', width: 100 },
  { title: '当前节点', dataIndex: 'currentNodeName', key: 'currentNodeName', width: 100 },
  // 配电箱相关列
  { title: '配电箱编号', dataIndex: 'distributionBoxSchemeNumber', key: 'distributionBoxSchemeNumber', width: 150 },
  { title: '配电箱类型', dataIndex: 'distributionBoxType', key: 'distributionBoxType', width: 120 },
  { title: '配电箱区域', dataIndex: 'distributionBoxArea', key: 'distributionBoxArea', width: 120 },
  { title: '配电箱检查日期', dataIndex: 'distributionBoxInspectionDate', key: 'distributionBoxInspectionDate', width: 150 },
  { title: '制单人员', dataIndex: 'creatorUser', key: 'creatorUser', width: 120 },
  { title: '发起时间', dataIndex: 'creatorTime', key: 'creatorTime', width: 150 },
]);

// 日期格式化函数
const formatDate = (date: string | number | Date) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-';
};

// 配电箱类型代码转中文
const getDistributionBoxTypeText = (typeCode: string) => {
  return DISTRIBUTION_BOX_TYPES[typeCode] || typeCode || '-';
};

async function init() {
  setLoading(true);
  reload();
}

function handleAdd() {
  flowRef.value.handleAdd({ distributionBoxAcceptance: props.distributionBoxAcceptance });
}

const { createMessage, createConfirm } = useMessage();

const searchInfo = reactive({
  distributionBoxAcceptance: '',
  _sorter: 'f_creator_time DESC',
});
const selList = ref<any[]>([]);

const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
const [registerTable, { reload, setLoading, getForm }] = useTable({
  api: distributionBoxAcceptanceApi.page,
  columns: distributionBoxAcceptanceRecordCol.value,
  searchInfo,
  useSearchForm: true,
  clickToRowSelect: true,
  rowSelection: {
    onChange: (selectedRowKeys) => {
      // 处理行选择变化
      selList.value = selectedRowKeys;
    },
  },
  immediate: false, // 避免组件挂载前触发请求
  ellipsis: false,  // 禁用文本省略
  formConfig: {
    schemas: [
      genQuerySearch()
    ],
  },
  actionColumn: {
    width: 180,
    title: '操作',
    dataIndex: 'action',
  },
});

// 添加操作列配置
function getTableActions(record: any): ActionItem[] {
  return [
    genFlowEditBtn(record, toDetail),
    genFlowDeleteBtn(record, handleDelete),
    genFlowDetailBtn(record, toDetail),
    {
      label: '材料详情',
      onClick: detailHandle.bind(null, record),
    },
  ];
}

function detailHandle(record) {
  openDetailDrawer(true, { distributionBoxAcceptanceId: record.id, distributionBoxAcceptanceRecord: record, userId: undefined });
}

function handleDelete(id: any) {
  distributionBoxAcceptanceApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function toDetail(record: any, opType: FLOW_OP_TYPE) {
  flowRef.value.toDetail(record, opType);
}

onMounted(() => {
  init();
});
</script>
