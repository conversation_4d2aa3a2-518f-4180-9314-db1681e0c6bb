<template>
  <BasicPopup
    v-bind="$attrs"
    @register="registerPopup"
    :title="getTitle"
    showOkBtn
    @ok="handleSubmit"
    :destroyOnClose="true"
  >
    <div class="jnpf-content-wrapper jnpf-content-wrapper-form">
      <div class="jnpf-content-wrapper-form-body px-10px">
        <ScrollContainer>
          <div class="my-10px">
            <a-alert v-if="!id" message="七牛云多文件上传（仅支持PDF）" type="info" :showIcon="false" />
          </div>
          <a-form :colon="false" :labelCol="{ style: { width: '400px' } }">
            <template v-if="!id">
              <a-form-item label="七牛云上传附件+多文件（仅支持PDF）">
                <JnpfFaUploadFileQiniu
                  v-model:value="ids"
                  prefix="test"
                  :multiple="true"
                  accept=".pdf"
                  @beforeUpload="handleBeforeUpload"
                  @change="handleUploadChange"
                />
                <div class="my-5px">
                  <span class="ant-form-text" style="color: red;">仅支持PDF格式上传</span>
                </div>
                <!-- 新增：显示已上传的文件列表 -->
                <div v-if="uploadedFiles.length > 0" class="mt-3">
                  <h4>已上传文件:</h4>
                  <ul class="list-disc pl-5">
                    <li v-for="file in uploadedFiles" :key="file.id">
                      {{ file.name }}
                      <a-icon
                        type="delete"
                        class="ml-2 text-red-500 cursor-pointer"
                        @click="removeFile(file.id)"
                      />
                    </li>
                  </ul>
                </div>
              </a-form-item>
            </template>
            <template v-else>
              <!-- 使用v-model正确绑定fileName -->
              <div class="mb-4 flex justify-center max-w-4xl mx-auto">
                <div class="flex items-center">
                  <label class="block text-sm font-medium text-gray-700 w-18 pr-2">文件名称</label>
                  <div class="mt-1 flex-1">
                    <input
                      type="text"
                      v-model="fileName"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary w-200"
                    />
                  </div>
                </div>
              </div>
            </template>
          </a-form>
        </ScrollContainer>
      </div>
    </div>
  </BasicPopup>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { BasicPopup, usePopupInner } from "/@/components/Popup";
import { ScrollContainer } from '/@/components/Container';
import { useMessage } from '/@/hooks/web/useMessage';
import { odesignPlanApi as api } from '/@/api';
import JnpfFaUploadFileQiniu from "/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue";

const id = ref('');
const ids = ref<string[]>([]);
const fileName = ref(''); // 统一使用fileName管理文件名
const uploadedFiles = ref<{ id: string; name: string }[]>([]); // 新增：上传文件列表
const { createMessage } = useMessage();

// 新增：添加对fileName的watch监听
watch(fileName, (newVal, oldVal) => {
  console.log('fileName变化:', oldVal, '->', newVal);
});

const getTitle = computed(() => (!id.value ? '新建试验、检验凭单和调试记录' : '编辑试验、检验凭单和调试记录'));
const emit = defineEmits(['register', 'reload']);

const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

function init(data: any) {
  id.value = data.id;
  ids.value = [];
  fileName.value = '';
  uploadedFiles.value = []; // 重置上传文件列表

  if (id.value) {
    changeLoading(true);
    api.getDetailById(id.value)
      .then((res) => {
        const fileInfo = res.data;
        console.log('从API获取的文件信息:', fileInfo);

        // 根据实际数据结构调整
        const fileId = fileInfo.id || '';
        const originalName = fileInfo.originalFilename || fileInfo.filename || fileInfo.fileName || '';

        if (fileId && originalName) {
          ids.value.push(fileId);

          // 提取文件名（不含扩展名）
          const fileNameWithoutExtension = originalName.split('.').slice(0, -1).join('.');
          fileName.value = fileNameWithoutExtension; // 直接设置fileName

          console.log('初始化文件名:', fileNameWithoutExtension);
        }

        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }
}

// 文件上传前的处理
function handleBeforeUpload(file: any) {
  // 检查文件类型
  const isPDF = file.type === 'application/pdf';
  if (!isPDF) {
    createMessage.error('只能上传PDF文件!');
  }
  return isPDF;
}

// 处理文件上传变化
function handleUploadChange(info: any) {
  console.log('上传变化:', info);

  // 如果有新文件上传成功
  if (info.file.status === 'done') {
    // 提取文件名（不含扩展名）
    const file = info.file;
    const nameWithoutExt = file.name.split('.').slice(0, -1).join('.');

    // 添加到已上传文件列表
    uploadedFiles.value.push({
      id: file.response?.id || file.uid, // 使用返回的id或临时uid
      name: nameWithoutExt
    });

    // 如果fileName为空，使用第一个上传的文件名
    if (!fileName.value && uploadedFiles.value.length === 1) {
      fileName.value = nameWithoutExt;
    }

    createMessage.success(`${file.name} 上传成功`);
  }
  // 如果有文件上传失败
  else if (info.file.status === 'error') {
    createMessage.error(`${info.file.name} 上传失败`);
  }
}

// 移除文件
function removeFile(fileId: string) {
  uploadedFiles.value = uploadedFiles.value.filter(file => file.id !== fileId);
  ids.value = ids.value.filter(id => id !== fileId);

  // 如果没有文件了，清空fileName
  if (uploadedFiles.value.length === 0) {
    fileName.value = '';
  }
}

// 提交表单
function handleSubmit() {
  if ((!id.value && ids.value.length === 0) || (id.value && !fileName.value.trim())) {
    createMessage.error(id.value ? '请填写文件名称' : '请至少上传一个PDF文件');
    return;
  }

  changeOkLoading(true);

  if (id.value) {
    // 编辑时直接使用fileName
    api.update({
      id: id.value,
      fileName: fileName.value,
      uploadType: '3'
    })
      .then((res) => {
        createMessage.success(res.msg);
        changeOkLoading(false);
        closePopup();
        emit('reload');
      })
      .catch(() => {
        createMessage.error('更新失败');
        changeOkLoading(false);
      });
  } else {
    // 新建时使用fileName为所有文件命名
    const entities = ids.value.map((id) => {
      return {
        id: id,
        fileName: fileName.value,
        uploadType: '3'
      };
    });

    api.saveBatch(entities)
      .then((res) => {
        createMessage.success(res.msg);
        changeOkLoading(false);
        closePopup();
        emit('reload');
      })
      .catch(() => {
        createMessage.error('保存失败');
        changeOkLoading(false);
      });
  }
}
</script>
