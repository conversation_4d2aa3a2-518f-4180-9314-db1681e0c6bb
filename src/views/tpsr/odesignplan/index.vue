<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新建</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key ==='status'">
              <a-tag v-if="record.status === 0">未开始</a-tag>
              <a-tag v-if="record.status === 1" color="processing">进行中</a-tag>
              <a-tag v-if="record.status === 2" color="success">已开始</a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { odesignPlanApi as api } from '/@/api';
import { usePopup } from "/@/components/Popup";
import { useMessage } from "/@/hooks/web/useMessage";
import { genDeleteBtn, genEditBtn, genQueryInput, genQuerySearch } from "/@/utils/tableUtils";
import Form from "./Form.vue";

defineOptions({ name: 'tpsr-odesignPlan-table' });

const { createMessage } = useMessage();
const [registerForm, { openPopup: openFormPopup }] = usePopup();

const columns: BasicColumn[] = [
  { title: '文件编号', dataIndex: 'id', width: 80 },
  { title: '文件名称', dataIndex: 'fileName', width: 300 },
  { title: '创建时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
];

const [registerTable, { reload }] = useTable({
  api: (params) => api.minePage({ ...params, uploadType: '0' }), // 添加 uploadType 参数
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('文件名称', 'fileName'),
    ],
  },
  actionColumn: {
    width: 140,
    title: '操作',
    dataIndex: 'action',
  },
  searchInfo: {
    '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
  ];
}

function addOrUpdateHandle(id = '') {
  openFormPopup(true, { id });
}

function handleDelete(id: any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
