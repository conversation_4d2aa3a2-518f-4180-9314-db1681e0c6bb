<template>
  <BasicDrawer
      v-bind="$attrs"
      @register="registerDrawer"
      title="设备详情"
      width="800px"
      class="full-drawer detail-drawer"
      :destroyOnClose="true"
      @close="handleClose"
  >
    <div class="fa-pl12 fa-pr12">
      <a-tabs>
        <a-tab-pane key="1" tab="设备信息">
          <div v-if="id">
            <p><span>设备ID:</span> {{ detail.deviceId }}</p>
            <p><span>设备名称:</span> {{ detail.deviceName }}</p>
            <p><span>设备位置:</span> {{ detail.deviceLocation }}</p>
            <p><span>直播URL:</span> {{ detail.liveUrl }}</p>
            <p><span>负责人:</span> {{ detail.responsiblePerson }}</p>
            <!-- 添加 VideoYs7 组件 -->
            <div v-if="detail.deviceId && detail.accessToken">
              <VideoYs7 :device-serial="detail.deviceId" :access-token="detail.accessToken" :url="detail.liveUrl" />
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { ref } from 'vue';
import { videoApi } from '/@/api';
import { VideoYs7 } from "/@/components/Video";

const detail = ref<any>({});

const [registerDrawer, { closeDrawer }] = useDrawerInner(init);

const id = ref<any>();

function init(data: any) {
  console.log('init', data);
  id.value = data.id;
  fetchDetail();
}

function fetchDetail() {
  if (id.value) {
    videoApi.getById(id.value).then((res) => {
      detail.value = res.data;
      // 打印值
      console.log('detail.liveUrl:', detail.value.liveUrl);
      console.log('detail.deviceId:', detail.value.deviceId);
      console.log('detail.accessToken:', detail.value.accessToken);
    });
  }
}

function handleClose() {
  // 清理资源
  detail.value = {};
  id.value = null;
}
</script>

<style lang="less">
.detail-drawer {
  .ant-drawer-content-wrapper {
    border-radius: 4px !important;
  }
}
</style>
