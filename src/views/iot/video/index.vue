<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新建</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag v-if="record.status === 0">未开始</a-tag>
              <a-tag v-if="record.status === 1" color="processing">进行中</a-tag>
              <a-tag v-if="record.status === 2" color="success">已结束</a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
    <DetailDrawer @register="registerDetailDrawer" />
  </div>
</template>

<script setup lang="ts">
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from "/@/components/Table";
import { videoApi as api } from '/@/api';
import { usePopup } from "/@/components/Popup";
import { useMessage } from "/@/hooks/web/useMessage";
import { genDeleteBtn, genEditBtn, genQueryInput, genQuerySearch } from "/@/utils/tableUtils";
import Form from "./Form.vue";
import { useDrawer } from '/@/components/Drawer';
import DetailDrawer from './DetailDrawer.vue';

const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();


defineOptions({ name: 'iot-water-device-table' });

const {createMessage} = useMessage();
const [registerForm, { openPopup: openFormPopup }] = usePopup();

const columns: BasicColumn[] = [
  { title: '设备ID', dataIndex: 'deviceId', width: 80 },
  { title: '设备名称', dataIndex: 'deviceName', width: 80 },
  { title: '设备位置', dataIndex: 'deviceLocation', width: 80 },
  { title: '直播URL', dataIndex: 'liveUrl', width: 150 },
  { title: '负责人', dataIndex: 'responsiblePerson', width: 80 },
];

const [registerTable, { reload }] = useTable({
  api: api.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('标题', 'deviceName'),
    ],
  },
  actionColumn: {
    width: 140,
    title: '操作',
    dataIndex: 'action',
  },
  searchInfo: {
    '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
  },
});

function getTableActions(record:any): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: () => openDetailDrawer(true, {id: record.id}),
    },
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
  ];
}

function addOrUpdateHandle(id='') {
  openFormPopup(true, {id});
}

function handleDelete(id:any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
