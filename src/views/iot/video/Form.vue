<template>
  <BasicPopup
    v-bind="$attrs"
    @register="registerPopup"
    :title="getTitle"
    showOkBtn
    @ok="handleSubmit"
    :destroyOnClose="true"
  >
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>

<script setup lang="ts">
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from "/@/components/Popup";
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { videoApi as api } from '/@/api';
import { genInput, genUser } from "/@/utils/formUtils";

const id = ref('');

const getTitle = computed(() => (!unref(id) ? '新建摄像头' : '编辑摄像头'));
const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  schemas: [
    genInput('设备ID', 'deviceId'),
    genInput('设备名称', 'deviceName'),
    genInput('设备位置', 'deviceLocation', false),
    genInput('直播URL', 'liveUrl'),
    genUser('负责人', 'responsiblePerson'),
  ],
});
const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

function init(data:any) {
  resetFields();
  id.value = data.id;
  setFieldsValue({ treeId: data.treeId })
  if (id.value) {
    changeLoading(true);
    api.getDetailById(id.value).then(res => {
      setFieldsValue(res.data);
      changeLoading(false);
    });
  }
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
  };
  const formMethod = id.value ? api.update : api.save;
  formMethod(query).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closePopup();
    emit('reload');
  }).catch(() => changeOkLoading(false));
}
</script>
