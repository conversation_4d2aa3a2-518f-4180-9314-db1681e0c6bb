<template>
  <BasicPopup
    v-bind="$attrs"
    @register="registerPopup"
    :title="getTitle"
    showOkBtn
    @ok="handleSubmit"
    :destroyOnClose="true"
  >
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>

<script setup lang="ts">
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from "/@/components/Popup";
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { waterlsApi as api, videoApi } from '/@/api';
import { genInput, genUser } from "/@/utils/formUtils";

const id = ref('');
const statusMap = { // 状态映射
  normal: '正常',
  alarming: '报警',
  preAlarming: '预警',
  offline: '离线'
};

const getTitle = computed(() => (!unref(id) ? '新建设备' : '编辑设备'));
const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();

// 摄像头选项
const cameraOptions = ref<any[]>([]);

// 获取摄像头列表
async function getCameraOptions() {
  try {
    const res = await videoApi.all();
    cameraOptions.value = res.data?.map(item => ({
      fullName: item.deviceName || item.deviceId,
      id: item.id,
    })) || [];
  } catch (error) {
    console.error('获取摄像头列表失败:', error);
    cameraOptions.value = [];
  }
}
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  schemas: [
    genInput('设备ID', 'deviceId'),
    genInput('设备名称', 'deviceName'),
    genInput('设备位置', 'deviceLocation', false),
    {
      field: 'cameraId',
      label: '绑定摄像头',
      component: 'Select',
      componentProps: {
        placeholder: '请选择摄像头',
        options: cameraOptions,
        fieldNames: { label: 'fullName', value: 'id' },
      },
      rules: [{ required: false, trigger: 'change', message: '请选择摄像头' }],
    },
    {
      field: 'currentValue',
      label: '当前值',
      component: 'Input',
      componentProps: { placeholder: '', disabled: true },
      rules: [{ required: false, trigger: 'blur', message: '当前值不能为空' }],
    },
    {
      field: 'deviceStatus',
      label: '设备运行状态',
      component: 'Select',
      componentProps: {
        placeholder: '',
        options: [
          { label: '正常', value: 'normal' },
          { label: '报警', value: 'alarming' },
          { label: '预警', value: 'preAlarming' },
          { label: '离线', value: 'offline' },
        ],
        disabled: true,
      },
      rules: [{ required: false, trigger: 'change', message: '设备运行状态不能为空' }],
    },
    genUser('告警人', 'alarmPerson'),
  ],
});
const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

function init(data: any) {
  resetFields();
  id.value = data.id;

  // 加载摄像头列表
  getCameraOptions();

  if (id.value) {
    changeLoading(true);
    api.getDetailById(id.value).then(res => {
      const deviceData = res.data;
      // 将设备状态的英文值转换为中文显示
      deviceData.deviceStatus = statusMap[deviceData.deviceStatus] || deviceData.deviceStatus;
      setFieldsValue(deviceData);
      changeLoading(false);
    });
  }
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
    // 提交时将中文状态转换回英文值
    deviceStatus: Object.keys(statusMap).find(key => statusMap[key] === values.deviceStatus) || values.deviceStatus,
  };
  const formMethod = id.value ? api.update : api.save;
  formMethod(query).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closePopup();
    emit('reload');
  }).catch(() => changeOkLoading(false));
}
</script>
