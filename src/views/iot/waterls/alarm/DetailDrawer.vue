<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="设备详情"
    width="800px"
    class="full-drawer detail-drawer"
  >
    <div class="fa-pl12 fa-pr12">
      <a-tabs>
        <a-tab-pane key="1" tab="设备信息">
          <div v-if="id">
            <p><span>设备ID:</span> {{ detail.deviceId }}</p>
            <p><span>告警时间:</span> {{ detail.alarmTime | date('YYYY-MM-DD HH:mm:ss') }}</p>
            <p><span>告警值:</span> {{ detail.alarmValue }}</p>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { ref } from 'vue';
import { waterlsApi } from '/@/api';

const detail = ref<any>({});

const [registerDrawer] = useDrawerInner(init);

const id = ref<any>();

function init(data: any) {
  console.log('init', data);
  id.value = data.id;
  fetchDetail();
}

function fetchDetail() {
  if (id.value) {
    waterlsApi.getById(id.value).then((res) => {
      detail.value = res;
    });
  }
}
</script>

<style lang="less">
.detail-drawer {
  .ant-drawer-content-wrapper {
    border-radius: 4px !important;
  }
}
</style>
