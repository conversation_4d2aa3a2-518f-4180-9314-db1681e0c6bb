<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <!-- 移除新建按钮 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 移除 Form 组件，因为不需要新建和编辑功能 -->
    <DetailDrawer @register="registerDetailDrawer" />
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { waterlsAlarmApi as api} from '/@/api';
import { useMessage } from "/@/hooks/web/useMessage";
import { genDeleteBtn, genQueryInput, genQuerySearch } from "/@/utils/tableUtils";
import { useDrawer } from '/@/components/Drawer';
import DetailDrawer from './DetailDrawer.vue';

const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
const { createMessage } = useMessage();

// 只保留需要展示的列
const columns: BasicColumn[] = [
  { title: '设备ID', dataIndex: 'deviceId', width: 80 },
  { title: '设备名称', dataIndex: 'deviceName', width: 100 },
  { title: '告警时间', dataIndex: 'alarmTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '告警值', dataIndex: 'alarmValue', width: 80 }
];

const [registerTable, { reload }] = useTable({
  api: api.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('设备ID', 'deviceId'), // 可根据需求添加搜索条件
    ],
  },
  searchInfo: {
    '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: () => openDetailDrawer(true, { id: record.id }),
    },
    genDeleteBtn(record, handleDelete),
  ];
}

function handleDelete(id: any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
