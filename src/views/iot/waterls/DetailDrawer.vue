<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="设备详情"
    width="1200px"
    class="full-drawer detail-drawer bg-white shadow-md rounded-md"
    :destroyOnClose="true"
    @close="handleClose"
  >
    <div class="detail-container">
      <div class="detail-info-section">
        <a-tabs class="rounded-md overflow-hidden">
          <a-tab-pane key="1" tab="设备信息">
            <div v-if="id" class="space-y-4">
              <p class="flex items-center space-x-2">
                <span class="font-medium">设备ID:</span>
                <span>{{ detail.deviceId }}</span>
              </p>
              <p class="flex items-center space-x-2">
                <span class="font-medium">设备名称:</span>
                <span>{{ detail.deviceName }}</span>
              </p>
              <p class="flex items-center space-x-2">
                <span class="font-medium">设备位置:</span>
                <span>{{ detail.deviceLocation }}</span>
              </p>
              <p class="flex items-center space-x-2">
                <span class="font-medium">当前值:</span>
                <span>{{ detail.currentValue }}</span>
              </p>
              <p class="flex items-center space-x-2">
                <span class="font-medium">设备运行状态:</span>
                <span>{{ getStatusText(detail.deviceStatus) }}</span>
              </p>
              <p class="flex items-center space-x-2">
                <span class="font-medium">告警人:</span>
                <span>{{ detail.alarmPerson }}</span>
              </p>
              <p v-if="detail.cameraId" class="flex items-center space-x-2">
                <span class="font-medium">绑定摄像头:</span>
                <span>{{ cameraDetail.deviceName || cameraDetail.deviceId || '未知' }}</span>
              </p>
            </div>
          </a-tab-pane>
          <!-- 可以添加其他标签页 -->
        </a-tabs>
      </div>

      <!-- 摄像头展示区域 -->
      <div v-if="cameraDetail.deviceId && cameraDetail.accessToken && cameraDetail.liveUrl" class="camera-section">
        <div class="camera-title">摄像头监控</div>
        <div class="camera-wrapper">
          <VideoYs7
            :device-serial="cameraDetail.deviceId"
            :access-token="cameraDetail.accessToken"
            :url="cameraDetail.liveUrl"
            @error="handleVideoError"
          />
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { ref } from 'vue';
import { waterlsApi, videoApi } from '/@/api';
import { VideoYs7 } from "/@/components/Video";
import { useMessage } from '/@/hooks/web/useMessage';

const detail = ref<any>({});
const cameraDetail = ref<any>({});
const { createMessage } = useMessage();

const [registerDrawer] = useDrawerInner(init);

const id = ref<any>();

function init(data: any) {
  console.log('init', data);
  id.value = data.id;
  fetchDetail();
}

function fetchDetail() {
  if (id.value) {
    waterlsApi.getById(id.value).then((res) => {
      detail.value = res.data;

      // 如果有摄像头ID，获取摄像头详情
      if (detail.value.cameraId) {
        getCameraDetail(detail.value.cameraId);
      }
    });
  }
}

// 获取摄像头详情
async function getCameraDetail(cameraId: string) {
  if (!cameraId) {
    cameraDetail.value = {};
    return;
  }

  try {
    const res = await videoApi.getById(cameraId);
    cameraDetail.value = res.data || {};
    console.log('摄像头详情:', cameraDetail.value);
  } catch (error) {
    console.error('获取摄像头详情失败:', error);
    cameraDetail.value = {};
  }
}

// 处理视频错误
function handleVideoError(error: any) {
  console.error('视频播放错误:', error);
  createMessage.error('视频播放失败');
}

// 处理关闭事件
function handleClose() {
  // 清理资源
  detail.value = {};
  cameraDetail.value = {};
  id.value = null;
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'normal':
      return '正常';
    case 'alarming':
      return '报警';
    case 'preAlarming':
      return '预警';
    case 'offline':
      return '离线';
    default:
      return '未知';
  }
};
</script>

<style lang="less">
.detail-drawer {
  .ant-drawer-content-wrapper {
    border-radius: 4px !important;
  }

  // 确保Drawer的z-index不会影响视频全屏
  &.ant-drawer {
    z-index: 1000;
  }
}

.detail-container {
  display: flex;
  gap: 20px;
  padding: 16px;

  .detail-info-section {
    flex: 1;
    min-width: 400px;
  }

  .camera-section {
    flex: 1;
    min-width: 500px;

    .camera-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }

    .camera-wrapper {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      overflow: hidden;
      background: #f5f5f5;
      min-height: 400px;
      position: relative;

      // 确保视频在容器中正确显示
      :deep(.video-player-element) {
        min-height: 350px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .detail-container {
    flex-direction: column;

    .camera-section {
      min-width: auto;
    }
  }
}
</style>

<!-- 全局样式，确保全屏功能正常 -->
<style lang="less">
// 全屏时隐藏滚动条
body:-webkit-full-screen,
body:-moz-full-screen,
body:-ms-fullscreen,
body:fullscreen {
  overflow: hidden;
}
</style>
