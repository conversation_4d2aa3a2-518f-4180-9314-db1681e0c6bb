<template>
  <a-tabs v-model:activeKey="activeKey" class="userInfo-tabs">
    <a-tab-pane key="1" tab="账户信息">
      <a-row>
        <a-col :span="12">
          <a-form :colon="false" labelAlign="right" :labelCol="{ style: { width: '100px' } }">
            <a-form-item label="账户">
              <a-input v-model:value="form.account" readonly />
            </a-form-item>
            <a-form-item label="所属组织">
              <a-input v-model:value="form.organize" readonly />
            </a-form-item>
            <a-form-item label="直属主管">
              <a-input v-model:value="form.manager" readonly />
            </a-form-item>
            <a-form-item label="岗位">
              <a-input v-model:value="form.position" readonly />
            </a-form-item>
            <a-form-item label="角色">
              <a-input v-model:value="form.roleId" readonly />
            </a-form-item>
            <a-form-item label="注册时间">
              <a-input v-model:value="getCreatorTime" readonly />
            </a-form-item>
            <a-form-item label="上次登录">
              <a-input v-model:value="getPrevLogTime" readonly />
            </a-form-item>
            <a-form-item label="入职日期">
              <a-input v-model:value="getEntryDate" readonly />
            </a-form-item>
            <a-form-item label="学习积分">
              <a-input v-model:value="form.studyScore" readonly />
            </a-form-item>
          </a-form>
        </a-col>
      </a-row>
    </a-tab-pane>
    <a-tab-pane key="2" tab="个人资料">
      <a-form :colon="false" labelAlign="right" :model="form2" :rules="state.form2Rule" ref="form2ElRef" :labelCol="{ style: { width: '100px' } }">
        <a-row>
          <a-col :span="12">
            <a-form-item label="姓名" name="realName">
              <a-input v-model:value="form2.realName" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别">
              <jnpf-select v-model:value="form2.gender" :options="genderOptions" placeholder="选择性别" :fieldNames="{ value: 'enCode' }" showSearch />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="民族">
              <jnpf-select v-model:value="form2.nation" :options="nationOptions" placeholder="选择民族" showSearch />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="籍贯">
              <a-input v-model:value="form2.nativePlace" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件类型">
              <jnpf-select v-model:value="form2.certificatesType" :options="certificatesTypeOptions" placeholder="选择证件类型" showSearch />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件号码">
              <a-input v-model:value="form2.certificatesNumber" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="文化程度">
              <jnpf-select v-model:value="form2.education" :options="educationOptions" placeholder="选择学历" showSearch />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="出生年月">
              <jnpf-date-picker v-model:value="form2.birthday" placeholder="选择日期" format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="办公电话">
              <a-input v-model:value="form2.telePhone" :maxlength="20" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="办公座机">
              <a-input v-model:value="form2.landline" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号码">
              <a-input v-model:value="form2.mobilePhone" :maxlength="20" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="电子邮箱">
              <a-input v-model:value="form2.email" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="紧急联系">
              <a-input v-model:value="form2.urgentContacts" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="紧急电话">
              <a-input v-model:value="form2.urgentTelePhone" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="通讯地址">
              <a-input v-model:value="form2.postalAddress" :maxlength="300" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="自我介绍">
              <jnpf-textarea v-model:value="form2.signature" :maxlength="300" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label=" ">
              <a-button type="primary" @click="handleSubmit">保存</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-tab-pane>
    <a-tab-pane key="3" tab="个人签名">
      <a-space>
        <a-button @click="getSign">刷新</a-button>
        <a-button @click="() => openSignQrCodeModal(true, {})">
          <template #icon>
            <qrcode-outlined />
          </template>
          App扫码签名
        </a-button>
      </a-space>
      <a-row class="sign-list" :gutter="40">
        <a-col :span="6" class="sign-item add-sign" @click="openSignModal">
          <div class="sign-item-main">
            <i class="add-icon icon-ym icon-ym-btn-add"></i>
          </div>
        </a-col>
        <a-col :span="6" class="sign-item" :key="i" v-for="(item, i) in state.signList">
          <div :class="item.isDefault ? 'sign-item-main active' : 'sign-item-main'">
            <img :src="item.signImg" alt="" class="sign-img" />
            <div class="icon-checked" v-if="item.isDefault">
              <check-outlined />
            </div>
            <div v-if="!item.isDefault" class="add-button">
              <a-button size="small" @click="delSign(item.id)" class="mr-10px">删除</a-button>
              <a-button size="small" type="primary" @click="updateDefault(item.id, item.signImg)">设为默认</a-button>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-tab-pane>
  </a-tabs>
  <SignModal ref="signModalRef" submitOnConfirm @confirm="getSign" />
  <SignQrCordModal @register="registerForm" @reload="getSign" />
</template>

<script setup lang="ts">
  import { updateUserInfo, getSignList, deleteSign, updateDefaultSign } from '/@/api/permission/userSetting';
  import { reactive, toRefs, ref, computed, onMounted, unref } from 'vue';
  import { useBaseStore } from '/@/store/modules/base';
  import { useUserStore } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';
  import type { FormInstance } from 'ant-design-vue';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import SignModal from '/@/components/Jnpf/Sign/src/SignModal.vue';
  import { CheckOutlined, QrcodeOutlined } from '@ant-design/icons-vue';
  import SignQrCordModal from './modal/SignQrCordModal.vue'
  import { useModal } from '/@/components/Modal';

  interface State {
    activeKey: string;
    educationOptions: any[];
    certificatesTypeOptions: any[];
    genderOptions: any[];
    nationOptions: any[];
    signList: any[];
    form: any;
    form2: any;
    form2Rule: any;
  }

  const props = defineProps({
    user: { type: Object, default: () => ({}) },
  });
  const emit = defineEmits(['updateInfo']);
  const baseStore = useBaseStore();
  const userStore = useUserStore();
  const { createMessage } = useMessage();
  const form2ElRef = ref<FormInstance>();
  const signModalRef = ref(null);
  const state = reactive<State>({
    activeKey: '1',
    educationOptions: [],
    certificatesTypeOptions: [],
    genderOptions: [],
    nationOptions: [],
    signList: [],
    form: {},
    form2: {
      realName: '',
      signature: '',
      gender: 1,
      nation: '',
      nativePlace: '',
      certificatesType: '',
      certificatesNumber: '',
      education: '',
      birthday: null,
      telePhone: '',
      landline: '',
      mobilePhone: '',
      email: '',
      urgentContacts: '',
      urgentTelePhone: '',
      postalAddress: '',
    },
    form2Rule: {
      realName: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
    },
  });
  const { activeKey, form, form2, educationOptions, certificatesTypeOptions, genderOptions, nationOptions } = toRefs(state);

  const getCreatorTime = computed(() => (state.form.creatorTime ? formatToDateTime(state.form.creatorTime, 'YYYY-MM-DD HH:mm') : ''));
  const getEntryDate = computed(() => (state.form.entryDate ? formatToDateTime(state.form.entryDate, 'YYYY-MM-DD HH:mm') : ''));
  const getPrevLogTime = computed(() => (state.form.prevLogTime ? formatToDateTime(state.form.prevLogTime, 'YYYY-MM-DD HH:mm') : ''));

  async function getOptions() {
    const educationRes = (await baseStore.getDictionaryData('Education')) as any;
    state.educationOptions = educationRes;
    const certificateTypeRes = (await baseStore.getDictionaryData('certificateType')) as any;
    state.certificatesTypeOptions = certificateTypeRes;
    const sexRes = (await baseStore.getDictionaryData('sex')) as any;
    state.genderOptions = sexRes;
    const nationRes = (await baseStore.getDictionaryData('Nation')) as any;
    state.nationOptions = nationRes;
  }
  function getInfo() {
    state.form = props.user;
    for (let key of Object.keys(state.form2)) {
      state.form2[key] = state.form[key];
    }
  }
  function getSign() {
    getSignList().then(res => {
      state.signList = res.data || [];
    });
  }
  async function handleSubmit() {
    try {
      const values = await form2ElRef.value?.validate();
      if (!values) return;
      updateUserInfo(state.form2).then(res => {
        createMessage.success(res.msg);
        emit('updateInfo');
        userStore.setUserInfo({ userName: state.form2.realName });
      });
    } catch (_) {}
  }
  function openSignModal() {
    const signRef = unref(signModalRef) as any;
    signRef?.openModal();
  }
  function updateDefault(id, signImg) {
    updateDefaultSign(id)
      .then(res => {
        createMessage.success(res.msg);
        userStore.setUserInfo({ signImg: signImg });
        getSign();
      })
      .catch(_ => {
        getSign();
      });
  }
  function delSign(id) {
    deleteSign(id).then(res => {
      createMessage.success(res.msg);
      getSign();
    });
  }

  const [registerForm, { openModal: openSignQrCodeModal }] = useModal(); // 使用registerForm注册弹框，openFormModal打开弹框

  onMounted(() => {
    getOptions();
    getInfo();
    getSign();
  });
</script>

<style lang="less" scoped>
  html[data-theme='dark'] {
    .sign-list .sign-item .sign-item-main {
      background-color: #fff;
    }
  }
  .userInfo-tabs {
    height: 100%;
    .ant-tabs-tabpane {
      padding: 10px;
      overflow-x: hidden;
    }
  }
  :deep(.ant-tabs-content-holder) {
    height: calc(100% - 64px);
    overflow: auto;
  }
  .sign-list {
    padding: 20px 50px 0;
    .sign-item {
      margin-bottom: 20px;
      .sign-item-main {
        position: relative;
        height: 160px;
        background-color: @app-content-background;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .icon-checked {
          display: block;
          width: 16px;
          height: 16px;
          border: 16px solid @primary-color;
          border-left: 16px solid transparent;
          border-top: 16px solid transparent;
          border-bottom-right-radius: 10px;
          position: absolute;
          right: -1px;
          bottom: -1px;
          .anticon-check {
            position: absolute;
            top: -1px;
            left: -1px;
            font-size: 14px;
            color: #fff;
          }
        }
        &.active {
          border: 1px solid @primary-color;
          box-shadow: 0 0 6px rgba(6, 58, 108, 0.26);
          color: @primary-color;
        }
        &:hover {
          .add-button {
            display: flex;
            width: 100%;
            height: 100%;
            border-radius: 10px;
            background-color: rgba(157, 158, 159, 0.8);
            justify-content: center;
            align-items: center;
          }
        }
        .add-button {
          position: absolute;
          display: none;
        }
        .add-icon {
          font-size: 50px;
          color: @text-color-secondary;
        }
        .sign-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
</style>
