<template>
  <a-tabs v-model:activeKey="activeKey" class="userInfo-tabs">
    <a-tab-pane key="1" tab="个人资料">
      <a-form :colon="false" labelAlign="right" :model="form2" :rules="state.form2Rule" ref="form2ElRef" :labelCol="{ style: { width: '100px' } }">
        <a-row>
          <a-col :span="12">
            <a-form-item label="姓名" name="realName">
              <a-input v-model:value="form2.realName" :maxlength="50"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" name="gender">
              <jnpf-select v-model:value="form2.gender" :options="genderOptions" placeholder="选择性别" :fieldNames="{ value: 'enCode' }" showSearch/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="民族" name="nation">
              <jnpf-select v-model:value="form2.nation" :options="nationOptions" placeholder="选择民族" showSearch/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="籍贯" name="nativePlace">
              <a-input v-model:value="form2.nativePlace" :maxlength="50"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件类型" name="certificatesType">
              <jnpf-select v-model:value="form2.certificatesType" :options="certificatesTypeOptions" placeholder="选择证件类型" showSearch/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件号码" name="certificatesNumber">
              <a-input v-model:value="form2.certificatesNumber" :maxlength="50"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="文化程度" name="education">
              <jnpf-select v-model:value="form2.education" :options="educationOptions" placeholder="选择学历" showSearch/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="出生年月" name="birthday">
              <jnpf-date-picker v-model:value="form2.birthday" placeholder="选择日期" format="YYYY-MM-DD"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="办公电话">
              <a-input v-model:value="form2.telePhone" :maxlength="20"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="办公座机">
              <a-input v-model:value="form2.landline" :maxlength="50"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号码" name="mobilePhone">
              <a-input v-model:value="form2.mobilePhone" :maxlength="20"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="电子邮箱">
              <a-input v-model:value="form2.email" :maxlength="50"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="紧急联系">
              <a-input v-model:value="form2.urgentContacts" :maxlength="50"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="紧急电话">
              <a-input v-model:value="form2.urgentTelePhone" :maxlength="50"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="通讯地址">
              <a-input v-model:value="form2.postalAddress" :maxlength="300"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="自我介绍">
              <jnpf-textarea v-model:value="form2.signature" :maxlength="300"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label=" ">
              <a-button type="primary" @click="handleSubmit" :loading="loading">保存</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
import { updateUserInfo } from '/@/api/permission/userSetting';
import { onMounted, reactive, ref, toRefs } from 'vue';
import { useBaseStore } from '/@/store/modules/base';
import { useUserStore } from '/@/store/modules/user';
import { useMessage } from '/@/hooks/web/useMessage';
import type { FormInstance } from 'ant-design-vue';

interface State {
  activeKey: string;
  educationOptions: any[];
  certificatesTypeOptions: any[];
  genderOptions: any[];
  nationOptions: any[];
  signList: any[];
  form: any;
  form2: any;
  form2Rule: any;
  loading: boolean;
}

const props = defineProps({
  user: {type: Object, default: () => ({})},
});
const emit = defineEmits(['updateInfo']);
const baseStore = useBaseStore();
const userStore = useUserStore();
const {createMessage} = useMessage();
const form2ElRef = ref<FormInstance>();
const state = reactive<State>({
  activeKey: '1',
  educationOptions: [],
  certificatesTypeOptions: [],
  genderOptions: [],
  nationOptions: [],
  signList: [],
  form: {},
  form2: {
    realName: '',
    signature: '',
    gender: 1,
    nation: '',
    nativePlace: '',
    certificatesType: '',
    certificatesNumber: '',
    education: '',
    birthday: null,
    telePhone: '',
    landline: '',
    mobilePhone: '',
    email: '',
    urgentContacts: '',
    urgentTelePhone: '',
    postalAddress: '',
  },
  form2Rule: {
    realName: [{required: true, message: '姓名不能为空', trigger: 'blur'}],
    gender: [{required: true, message: '性别不能为空', trigger: 'blur'}],
    nation: [{required: true, message: '民族不能为空', trigger: 'blur'}],
    nativePlace: [{required: true, message: '籍贯不能为空', trigger: 'blur'}],
    certificatesType: [{required: true, message: '证件类型不能为空', trigger: 'blur'}],
    certificatesNumber: [{required: true, message: '证件号码不能为空', trigger: 'blur'}],
    education: [{required: true, message: '文化程度不能为空', trigger: 'blur'}],
    birthday: [{required: true, message: '出生年月不能为空', trigger: 'blur'}],
    mobilePhone: [{required: true, message: '手机号码不能为空', trigger: 'blur'}],
  },
  loading: false,
});
const {activeKey, form2, educationOptions, certificatesTypeOptions, genderOptions, nationOptions, loading} = toRefs(state);

async function getOptions() {
  const educationRes = (await baseStore.getDictionaryData('Education')) as any;
  state.educationOptions = educationRes;
  const certificateTypeRes = (await baseStore.getDictionaryData('certificateType')) as any;
  state.certificatesTypeOptions = certificateTypeRes;
  const sexRes = (await baseStore.getDictionaryData('sex')) as any;
  state.genderOptions = sexRes;
  const nationRes = (await baseStore.getDictionaryData('Nation')) as any;
  state.nationOptions = nationRes;
}

function getInfo() {
  console.log('props.user', props.user)
  state.form = props.user;
  for (let key of Object.keys(state.form2)) {
    state.form2[key] = state.form[key];
  }
}

async function handleSubmit() {
  try {
    const values = await form2ElRef.value?.validate();
    if (!values) return;
    state.loading = true
    updateUserInfo(state.form2).then(async res => {
      state.loading = false
      createMessage.success(res.msg);
      emit('updateInfo');
      userStore.setUserInfo({userName: state.form2.realName});
      await userStore.afterLoginAction(true)
    });
  } catch (_) {
    state.loading = false
  }
}

onMounted(() => {
  getOptions();
  getInfo();
});
</script>

<style lang="less" scoped>
html[data-theme='dark'] {
  .sign-list .sign-item .sign-item-main {
    background-color: #fff;
  }
}

.userInfo-tabs {
  height: 100%;

  .ant-tabs-tabpane {
    padding: 10px;
    overflow-x: hidden;
  }
}

:deep(.ant-tabs-content-holder) {
  height: calc(100% - 64px);
  overflow: auto;
}

.sign-list {
  padding: 20px 50px 0;

  .sign-item {
    margin-bottom: 20px;

    .sign-item-main {
      position: relative;
      height: 160px;
      background-color: @app-content-background;
      border-radius: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .icon-checked {
        display: block;
        width: 16px;
        height: 16px;
        border: 16px solid @primary-color;
        border-left: 16px solid transparent;
        border-top: 16px solid transparent;
        border-bottom-right-radius: 10px;
        position: absolute;
        right: -1px;
        bottom: -1px;

        .anticon-check {
          position: absolute;
          top: -1px;
          left: -1px;
          font-size: 14px;
          color: #fff;
        }
      }

      &.active {
        border: 1px solid @primary-color;
        box-shadow: 0 0 6px rgba(6, 58, 108, 0.26);
        color: @primary-color;
      }

      &:hover {
        .add-button {
          display: flex;
          width: 100%;
          height: 100%;
          border-radius: 10px;
          background-color: rgba(157, 158, 159, 0.8);
          justify-content: center;
          align-items: center;
        }
      }

      .add-button {
        position: absolute;
        display: none;
      }

      .add-icon {
        font-size: 50px;
        color: @text-color-secondary;
      }

      .sign-img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
