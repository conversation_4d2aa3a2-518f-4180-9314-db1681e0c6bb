<template>
  <div :class="prefixCls">
    <div class="flex items-center absolute right-4 top-4">
      <AppDarkModeToggle class="enter-x mr-2" />
    </div>
    <div class="login-header">
    </div>
    <div class="login-content" style="height: auto;">
      <div style="width: 700px; padding: 24px;" v-if="user">
        <UserInfoUpdateForm :user="user" />
      </div>
    </div>
    <div class="copyright">{{ getSysConfig.copyright }}</div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, reactive, toRefs } from 'vue';
import { AppDarkModeToggle } from '/@/components/Application';
import { useDesign } from '/@/hooks/web/useDesign';
import { useAppStore } from '/@/store/modules/app';
import UserInfoUpdateForm from "/@/views/basic/firstUpdateProfile/UserInfoUpdateForm.vue";
import { getUserSettingInfo } from "/@/api/permission/userSetting";

interface State {
  user: any;
  userLoading: boolean;
}

const { prefixCls } = useDesign('login-container');
const appStore = useAppStore();

const state = reactive<State>({
  user: undefined,
  userLoading: false,
});
const { user } = toRefs(state);

function getInfo() {
  state.userLoading = true;
  getUserSettingInfo().then(res => {
    state.user = res.data;
    state.userLoading = false;
  });
}

const getSysConfig = computed(() => appStore.getSysConfigInfo);

onMounted(() => {
  getInfo();
});
</script>
<style lang="less">
@import url('../login/index.less');
</style>
