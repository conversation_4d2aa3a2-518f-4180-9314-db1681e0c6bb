<template>
  <div class="fa-flex-column fa-p24">
    <div class="fa-flex-row" style="height: 100px;">
      <div class="fa-flex-1">
        <Decoration3 style="width:250px;height:30px;position: absolute; top: 34px;left: 30px;"/>
      </div>
      <div class="fa-flex-1">
        <div class="fa-flex-column-center">
          <div style="position: absolute; top: 12px; font-size: 30px; font-weight: 600; ">公司级</div>
          <Decoration5
            :dur="2"
            style="width:600px;height:60px; position: absolute; top: 24px; "
          />
        </div>
      </div>
      <div class="fa-flex-1">
        <Decoration3 style="width:250px;height:30px;position: absolute; top: 34px;right: 30px;"/>
      </div>
    </div>

    <div class="fa-flex-1 fa-flex-row">
      <div class="fa-flex-1" style="height: 900px;">
        <BorderBox12 style="width: 600px; height: 300px; margin-bottom: 24px;" @click="handleToBu(division_Map[divisionEnum.HYXT])">
          <text class="fa-h3" style="color: #FFFFFF;position: absolute; top: 20px;left:20px;">事业部-{{ divisionEnum.HYXT }}</text>
          <div style="width: 550px;height: 280px;position: absolute; top: 80px;left:20px;">
            <CapsuleChart :config="barConfig1"/>
          </div>
        </BorderBox12>

        <BorderBox12 style="width: 600px; height: 300px;" @click="handleToBu(division_Map[divisionEnum.JZGC])">
          <text class="fa-h3" style="color: #FFFFFF;position: absolute; top: 20px;left:20px;">事业部-{{divisionEnum.JZGC}}</text>
          <div style="width: 550px;height: 280px;position: absolute; top: 80px;left:20px;">
            <CapsuleChart :config="barConfig2" />
          </div>
        </BorderBox12>
      </div>
      <div class="fa-flex-1 fa-flex-column" style="height: 800px;">
        <div class="fa-flex-1 fa-flex-row fa-h2" style="color: #FFFFFF;">
          <div class="fa-flex-1">
            <div class="fa-flex-1 fa-flex-column-center">
              <Decoration9 style="width:150px;height:150px;">
                <div style="color:#28dd84;" font-600 class="content" bg="~ dark/0">
                  <div class="fa-flex-column-center">
                    <text >今日项目人数</text>
                    <text>{{ centerData.todayProjNum }}</text>
                  </div>
                </div>
              </Decoration9>
            </div>
            <div class="fa-flex-1 fa-flex-column-center">
              <Decoration9 style="width:150px;height:150px;">
                <div style="color:#28dd84;" font-600 class="content" bg="~ dark/0">
                  <div class="fa-flex-column-center">
                    <text>总自有人数</text>
                    <text>{{ centerData.totalSelfEmployNum }}</text>
                  </div>
                </div>
              </Decoration9>
            </div>
          </div>
          <div class="fa-flex-1">
            <div class="fa-flex-1 fa-flex-column-center" style="height: 150px;">
              <Decoration9 style="width:150px;height:150px;">
                <div style="color:#28dd84;" font-600 class="content" bg="~ dark/0">
                  <div class="fa-flex-column-center">
                    <text>今日培训人数</text>
                    <text>{{ centerData.todayEduNum }}</text>
                  </div>
                </div>
              </Decoration9>
            </div>
            <div class="fa-flex-1 fa-flex-column-center">
              <div class="fa-flex-1 fa-flex-column-center" style="height: 150px;">
                <Decoration9 style="width:150px;height:150px;">
                  <div style="color:#28dd84;" font-600 class="content" bg="~ dark/0">
                    <div class="fa-flex-column-center">
                      <text>总分包人数</text>
                      <text>{{ centerData.totalOtherEmployNum }}</text>
                    </div>
                  </div>
                </Decoration9>
              </div>
            </div>
          </div>
          <div class="fa-flex-1 fa-flex-column-center fa-h2" style="color: #FFFFFF;">
          自有/分包占比
          <ActiveRingChart :config="ringConfig" style="width:250px;height:250px"/>
          </div>
        </div>
        <div class="fa-flex-1 fa-h2" style="color: #FFFFFF;">
          <BorderBox12 style="width: 600px; height: 300px;">
            <text style="position: absolute;top:10px;left: 10px;">每日进场人数走势图</text>
            <Chart :options="lineOptions" />
          </BorderBox12>
        </div>
      </div>

      <div class="fa-flex-1">
        <BorderBox12 style="width: 600px; height: 300px; margin-bottom: 24px;" @click="handleToBu(division_Map[divisionEnum.GJGC])">
          <text class="fa-h3" style="color: #FFFFFF;position: absolute; top: 20px;left:20px;">事业部-{{divisionEnum.GJGC}}</text>
          <div style="width: 550px;height: 280px;position: absolute; top: 80px;left:20px;">
            <CapsuleChart :config="barConfig3" />
          </div>
        </BorderBox12>

        <BorderBox12 style="width: 600px; height: 300px;" @click="handleToBu(division_Map[divisionEnum.HGYT])">
          <text class="fa-h3" style="color: #FFFFFF;position: absolute; top: 20px;left:20px;">事业部-{{divisionEnum.HGYT}}</text>
          <div style="width: 550px;height: 280px;position: absolute; top: 80px;left:20px;">
            <CapsuleChart :config="barConfig4" />
          </div>
        </BorderBox12>
      </div>
    </div>

  </div>
</template>
<script setup lang="ts">
import { BorderBox12, Decoration3, Decoration5,Decoration9,CapsuleChart,ActiveRingChart } from '@kjgl77/datav-vue3'
import { useRouter } from 'vue-router';
import {onMounted, reactive, ref} from "vue";
import {Chart} from "/@/components/Chart";
import {division_Map, divisionEnum} from "/@/enums/bigDataEnum";
import {dataVApi, organizeTreeApi} from "/@/api";
import {useMessage} from "/@/hooks/web/useMessage";

const router = useRouter();
const { createMessage } = useMessage();
const currentOrg = ref({})

const barConfig1 = reactive({
  data: [],
  colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
  unit: '人',
  fontSize:14,
  labelNum: 8,
})
const barConfig2 = reactive({
  data: [],
  colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
  unit: '人',
  fontSize:14,
  labelNum: 8,
})
const barConfig3 = reactive({
  data: [],
  colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
  unit: '人',
  fontSize:14,
  labelNum: 8,
})
const barConfig4 = reactive({
  data: [],
  colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
  unit: '人',
  fontSize:14,
  labelNum: 8,
})
const ringConfig = reactive({
  lineWidth: 24,
  digitalFlopStyle: {
    fill: 'pink',
  },
  data: [],
})
const lineOptions = reactive({
  xAxis: {
    type: 'category',
    data: [],
    // min:0,
    // max:6,
    // interval:1,
    // splitNumber:7,
    // axisLabel: {
    //   //x轴文字的配置
    //   show: true,
    //   interval: 0,//使x轴文字显示全
    //   rotate: -60
    // },
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [],
      type: 'line'
    }
  ]
});
let centerData = reactive({
  todayProjNum: 0,
  todayEduNum: 0,
  totalSelfEmployNum: 0,
  totalOtherEmployNum: 0,
})

// 生命周期钩子：组件挂载后调用 API
onMounted(async () => {
  try {
    await initInstance()
  } catch (error) {
    console.error('Error fetching data:', error);
    // 在这里您可以处理错误，比如显示一个错误消息给用户
  }
});

async function initInstance(){
  console.log('initInstance')
  let res= await organizeTreeApi.list({parentId:-1})
  console.log('res',res)
  if (!res || !res.data && res.data.length !== 1) return

  currentOrg.value = res.data[0]
  console.log('currentOrg',currentOrg.value)

  setDivisionData(barConfig1,division_Map[divisionEnum.HYXT])
  setDivisionData(barConfig2,division_Map[divisionEnum.JZGC])
  setDivisionData(barConfig3,division_Map[divisionEnum.GJGC])
  setDivisionData(barConfig4,division_Map[divisionEnum.HGYT])
  setRingData()
  setLineData()
  setCenterData()
}

async function setDivisionData(barConfig:any,divsionCode:string){
  dataVApi.getOrgBarData(divsionCode)
    .then(res=>{
      if (res && res.data) barConfig.data = res.data
    })
}

async function setRingData(){
  dataVApi.getEmployPieChartData(currentOrg.value.code)
    .then(res=>{
      if (res && res.data) ringConfig.data = res.data
    })
}

async function setLineData(){
  dataVApi.getInFacChartData(currentOrg.value.code)
    .then(res=>{
      if (!res || !res.data) return
      // 赋值
      let nameArr = res.data.map(item => item.name);
      let countArr = res.data.map(item => {
          return {
            value: item.count,
            name: item.name,
            id: item.id,
          }
        }
      );

      console.log('countArr',countArr)
      console.log('nameArr',nameArr)
      lineOptions.xAxis.data = nameArr;
      lineOptions.series[0].data = countArr;
    })
}

async function setCenterData(){
  dataVApi.getCenterData(currentOrg.value.code)
    .then(res=>{
      if(res && res.data) {
        centerData.todayProjNum = res.data.todayProjNum
        centerData.todayEduNum = res.data.todayEduNum
        centerData.totalSelfEmployNum = res.data.totalSelfEmployNum
        centerData.totalOtherEmployNum = res.data.totalOtherEmployNum
      }
      console.log('centerData', centerData)
    })
}

function handleToBu(division) {
  organizeTreeApi.list({code:division})
    .then(res=>{
      if (res && res.data && res.data.length === 1) {
        console.log('handleToBu res', res)
        router.push(`/datav/edu/bu?id=${res.data[0].id}`)
      } else {
        createMessage.error('未匹配到对应组织');
      }
    })
}
</script>
<style scoped>
.content {
  text-shadow: 0 0 3px #7acaec;
}
</style>
