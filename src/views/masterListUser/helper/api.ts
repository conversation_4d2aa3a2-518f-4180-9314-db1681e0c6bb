import { defHttp } from '/@/utils/http/axios';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/getMasterList', data });
}
// 获取列表优化
export function pageOptimized(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/pageOptimized', data });
}
// 新建
export function create(data) {
  return defHttp.post({ url:'/api/example/RemoveUser', data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: '/api/example/RemoveUser/'+ data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: '/api/example/RemoveUser/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: '/api/example/RemoveUser/detail/' + id });
}
// 撤场
export function del(id) {
  return defHttp.get({ url: '/api/example/RemoveUser/remove/' + id });
}
// 批量撤场
export function batchDelete(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/batchRemove', data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/Actions/Export', data });
}

// 导出
export function exportExcel(data) {
  return defHttp.requestDownload({ url: '/api/example/RemoveUser/exportExcel', data });
}

// 导入撤场人员
export function removeImportData(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/removeImportData', data });
}

// 导入撤场预览
export function removeImportPreview(data) {
  return defHttp.get({ url: '/api/example/RemoveUser/removeImportPreview', data });
}

// 获取列表
export function getFile(id) {
  return defHttp.get({ url: '/api/example/RemoveUser/getFile/' + id });
}

// 上传文件
export function uploadSubmitFile(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/uploadSubmitFile', data });
}

// 上传退场承诺书
export function uploadExitCommitment(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/uploadExitCommitment', data });
}

