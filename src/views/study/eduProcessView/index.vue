<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px;">
      <BasicLeftTree
        title="培训分类"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar/>
    </div>

    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
      <eduProcessViewDrawer @register="registerProcessViewDrawer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, toRefs, unref} from "vue";
import {BasicLeftTree, TreeActionType} from "/@/components/Tree";
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from "/@/components/Table";
import {testEduClassTreeApi, testEduRecordApi} from "/@/api";
import {useMessage} from "/@/hooks/web/useMessage";
import {genEditBtn, genDeleteBtn, genQueryKeyword, genQueryDept, genQueryCommon, genQuerySearch} from "/@/utils/tableUtils";
import { useDrawer } from "/@/components/Drawer";
import eduProcessViewDrawer from "/@/views/study/eduProcessView/EduProcessViewDrawer.vue";
import {eduRecordColumn} from "/@/enums/eduEnum";

defineOptions({name: 'testManage-eduRecordManage'});

interface State {
  treeData: any[];
  treeLoading: boolean;
}

const state = reactive<State>({
  treeData: [],
  treeLoading: false,
});
const {treeData, treeLoading} = toRefs(state);
const leftTreeRef = ref<Nullable<TreeActionType>>(null);
const [registerProcessViewDrawer, { openDrawer: openViewDrawer }] = useDrawer();

async function init() {
  setLoading(true);
  await reloadTree();
  searchInfo.eduClassId = state.treeData[0]?.id;
  const leftTree = unref(leftTreeRef);
  leftTree?.setSelectedKeys([searchInfo.eduClassId]);
  getForm().resetFields();
  reload()
}

// ----------------------------------- left tree -----------------------------------
/** 重新加载Tree数据 */
async function reloadTree() {
  state.treeLoading = true;
  const ret = await testEduClassTreeApi.allTree();
  // state.treeData = ret.data;
  state.treeData = [
    {id: '', name: '全部'},
    ...ret.data,
  ];
  state.treeLoading = false
}

/** Tree节点点击 */
function handleTreeSelect(id: any) {
  console.log('handleTreeSelect', id)
  // if (!id || searchInfo.eduClassId === id) return;
  searchInfo.eduClassId = id;
  reload()
}

// ----------------------------------- right table -----------------------------------
const {createMessage, createConfirm} = useMessage();

const searchInfo = reactive({
  eduClassId: '', // 右table所属左treeId
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});
const selList = ref<any[]>([]); // 选中的试题列表

const [registerTable, {reload, setLoading, getForm}] = useTable({
  api: testEduRecordApi.page,
  columns: eduRecordColumn,
  searchInfo,
  useSearchForm: true,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys) => { // 选中行change
      console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  immediate: false,
  ellipsis: false,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryDept('部门', 'organizeIds'),
      genQueryCommon('岗位', 'posId', 'PosSelect'),
    ],
  },
  actionColumn: {
    width: 190,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    {
      label: '进度统计',
      onClick: viewHandle.bind(null, record),
    }
  ];
}

function viewHandle(record) {
  console.log('openViewDrawer')
  openViewDrawer(true, {eduId:record.id, eduRecord:record,userId:undefined});
}

onMounted(() => init());
</script>

<style scoped>
</style>
