<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center bg-white p-10px">
      <Chart ref="chartRef" :options="options" class="mt-30px" height="500px"/>
      <Chart ref="chartRef2" :options="options2" class="mt-30px new-chart" height="500px"/>
      <Chart ref="chartRef3" :options="options3" class="mt-30px new-chart" height="500px"/>
      <Chart ref="chartRef4" :options="options4" class="mt-30px new-chart" height="500px"/>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {onMounted, ref} from 'vue';
import {Chart} from '/@/components/Chart';
import {eduDataCalApi} from "/@/api";

defineOptions({name: 'issueHandleCountBar'});

const chartRef = ref<any>(null); // ref
const options = ref({
  title: {
    text: '目前为止记录总统计',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      // 坐标轴指示器，坐标轴触发有效
      type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
    },
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    feature: {
      mark: {show: true},
      dataView: {show: true, readOnly: false},
      magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
      restore: {show: true},
      saveAsImage: {show: true},
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '自有',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
    {
      name: '劳务',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
  ],
});

const chartRef2 = ref<any>(null);
const options2 = ref({
  title: {
    text: '当季记录统计',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    feature: {
      mark: {show: true},
      dataView: {show: true, readOnly: false},
      magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
      restore: {show: true},
      saveAsImage: {show: true},
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '自有',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
    {
      name: '劳务',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
  ],
});

const chartRef3 = ref<any>(null);
const options3 = ref({
  title: {
    text: '当月记录统计',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    feature: {
      mark: {show: true},
      dataView: {show: true, readOnly: false},
      magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
      restore: {show: true},
      saveAsImage: {show: true},
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '自有',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
    {
      name: '劳务',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
  ],
});

const chartRef4 = ref<any>(null);
const options4 = ref({
  title: {
    text: '当日记录统计',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    feature: {
      mark: {show: true},
      dataView: {show: true, readOnly: false},
      magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
      restore: {show: true},
      saveAsImage: {show: true},
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '自有',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
    {
      name: '劳务',
      data: [],
      stack: 'email',
      emphasis: {
        focus: 'series',
      },
      type: 'bar',
      barWidth:'100',
    },
  ],
});

const TIME_TYPE_ONE = "until_now"
const TIME_TYPE_TWO = "current_season"
const TIME_TYPE_THREE = "current_month"
const TIME_TYPE_TOUR = "current_day"


// 生命周期钩子：组件挂载后调用 API
onMounted(async () => {
  try {
    // 替换为您的后台API端点，获取数据
    setOP(TIME_TYPE_ONE, options)
    setOP(TIME_TYPE_TWO, options2)
    setOP(TIME_TYPE_THREE, options3)
    setOP(TIME_TYPE_TOUR, options4)

    setTimeout(() => {
      console.log(chartRef.value.getInstance());
      const instance = chartRef.value.getInstance();
      if (instance) {
        instance.on('click', function (params) {
          // const selectYear = params.data.name;
          // setOP(TIME_TYPE_TWO, selectYear, options2)
        });
      }
    }, 100);
  } catch (error) {
    console.error('Error fetching area data:', error);
    // 在这里您可以处理错误，比如显示一个错误消息给用户
  }
});

const self_employ = "自有职工";
const other_employ = "劳务职工";

//柱状图参数赋值
async function setOP(timeType, commonOption) {
  const res = await eduDataCalApi.getDivisionData(timeType);
  if (!res.data) return;

  // 赋值
  commonOption.value.xAxis.data = ["核与系统工程事业部", "建筑工程事业部", "国际工程事业部", "化工与天然气工程事业部"]
  commonOption.value.series[0].data = res.data[self_employ];
  commonOption.value.series[1].data = res.data[other_employ];
}

</script>

<style lang="less" scoped>
.jnpf-content-wrapper-center {
  overflow-y: auto;
}
</style>
