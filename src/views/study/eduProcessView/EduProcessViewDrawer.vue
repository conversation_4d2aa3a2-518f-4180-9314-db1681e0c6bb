<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="1000px" class="full-drawer" title="进度可视化" @close="handleClose">
    <div class="fa-full fa-pl12 fa-pr12 fa-pb12 fa-scroll-auto-y">
      <div class="jnpf-content-wrapper-center bg-white p-10px">
        <Chart :options="options" class="mt-30px" height="500px" />
      </div>
    </div>

  </BasicDrawer>
</template>
<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import {reactive, ref} from "vue";
import {ActionItem, BasicTable, TableAction, useTable} from "/@/components/Table";
import {Chart} from "/@/components/Chart";
import {eduProcessApi} from "/@/api";
import {any} from "vue-types";

defineOptions({name: 'eduProcessViewDrawer'});
const emit = defineEmits(['register', 'refresh']);
const [registerDrawer, { changeLoading, closeDrawer }] = useDrawerInner(init);

const id = ref('')
const approvalId = ref('')
const eduId = ref('')

const options = reactive({
  title: {
    text: '培训进度',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      // 坐标轴指示器，坐标轴触发有效
      type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
    },
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    feature: {
      mark: { show: true },
      dataView: { show: true, readOnly: false },
      magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
      restore: { show: true },
      saveAsImage: { show: true },
    },
  },
  legend: {
    // data: ['直接访问', '邮件营销', '联盟广告', '视频广告', '搜索引擎', '百度', '谷歌', '必应', '其他'],
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: ['总人数', '视频已学完人数', '视频未学完人数', '考试通过人数', '考试未通过人数',],
    },
  ],
  yAxis: [
    {
      type: 'value',
    },
  ],
  series: [
    {
      name: '人数',
      type: 'bar',
      data: [any],
    },
  ],
});


async function init(data) {
  console.log('data',data)
  eduId.value = data.eduId

  changeLoading(true)
  await setOpData()
  changeLoading(false)
}

async function setOpData(){
  let eduRecordProcess = await eduProcessApi.getEduRecordProcess(eduId.value);
  console.log('eduRecordProcess',eduRecordProcess)

  let dataArr = new Array()
  if (eduRecordProcess.data) {
    dataArr[0] = eduRecordProcess.data.totalUserNum ? eduRecordProcess.data.totalUserNum : 0
    dataArr[1] = eduRecordProcess.data.eduFinishUserNum ? eduRecordProcess.data.eduFinishUserNum : 0
    dataArr[2] = eduRecordProcess.data.eduUnfinishUserNum ? eduRecordProcess.data.eduUnfinishUserNum : 0
    dataArr[3] = eduRecordProcess.data.examPassUserNum ? eduRecordProcess.data.examPassUserNum : 0
    dataArr[4] = eduRecordProcess.data.examUnpassUserNum ? eduRecordProcess.data.examUnpassUserNum : 0
  }
  options.series[0].data =dataArr
}


function handleClose() {
  console.log('close the Drawer。。。')

  approvalId.value = ''
  closeDrawer();
}
</script>
<style lang="less">
</style>
