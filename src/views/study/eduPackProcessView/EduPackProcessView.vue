<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="1000px" class="full-drawer" title="进度可视化" @close="handleClose">
    <a-tabs class="jnpf-content-wrapper-tabs" destroyInactiveTabPane>
      <a-tab-pane key="1" tab="一级培训进度">
        <div class="fa-full fa-pl12 fa-pr12 fa-pb12 fa-scroll-auto-y">
          <div class="jnpf-content-wrapper-center bg-white p-10px">
            <Chart :options="options1" class="mt-30px" height="500px" />
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="二级培训进度">
        <div class="fa-full fa-pl12 fa-pr12 fa-pb12 fa-scroll-auto-y">
          <div class="jnpf-content-wrapper-center bg-white p-10px">
            <Chart :options="options2" class="mt-30px" height="500px" />
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="3" tab="三级培训进度">
        <div class="fa-full fa-pl12 fa-pr12 fa-pb12 fa-scroll-auto-y">
          <div class="jnpf-content-wrapper-center bg-white p-10px">
            <Chart :options="options3" class="mt-30px" height="500px" />
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>


  </BasicDrawer>
</template>
<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import {reactive, ref} from "vue";
import {ActionItem, BasicTable, TableAction, useTable} from "/@/components/Table";
import {Chart} from "/@/components/Chart";
import {eduPackProcessApi, eduProcessApi} from "/@/api";
import {any} from "vue-types";

defineOptions({name: 'eduPackProcessView'});
const emit = defineEmits(['register', 'refresh']);
const [registerDrawer, { changeLoading, closeDrawer }] = useDrawerInner(init);

const id = ref('')
const packId = ref('')

const options1 = reactive({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    x: 'left',
    top: '10%',
    data: ['视频已看完', '视频未看完','考试已通过','考试未通过'],
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      selectedMode: 'single',
      radius: [0, '30%'],
      label: {
        position: 'inner',
      },
      labelLine: {
        show: false,
      },
      data: [
        // { value: 335, name: '视频已看完' },
        // { value: 679, name: '视频未看完' },
      ],
    },
    {
      name: '访问来源',
      type: 'pie',
      radius: ['40%', '55%'],
      label: {
        formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
        backgroundColor: '#eee',
        borderColor: '#aaa',
        borderWidth: 1,
        borderRadius: 4,
        rich: {
          a: {
            color: '#999',
            lineHeight: 22,
            align: 'center',
          },
          hr: {
            borderColor: '#aaa',
            width: '100%',
            borderWidth: 0.5,
            height: 0,
          },
          b: {
            fontSize: 16,
            lineHeight: 33,
          },
          per: {
            color: '#eee',
            backgroundColor: '#334455',
            padding: [2, 4],
            borderRadius: 2,
          },
        },
      },
      data: [
        // { value: 335, name: '考试已通过' },
        // { value: 310, name: '考试未通过' },
      ],
    },
  ],
});
const options2 = reactive({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    x: 'left',
    top: '10%',
    data: ['视频已看完', '视频未看完','考试已通过','考试未通过'],
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      selectedMode: 'single',
      radius: [0, '30%'],
      label: {
        position: 'inner',
      },
      labelLine: {
        show: false,
      },
      data: [
        // { value: 335, name: '视频已看完', selected: true },
        // { value: 679, name: '视频未看完' },
      ],
    },
    {
      name: '访问来源',
      type: 'pie',
      radius: ['40%', '55%'],
      label: {
        formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
        backgroundColor: '#eee',
        borderColor: '#aaa',
        borderWidth: 1,
        borderRadius: 4,
        rich: {
          a: {
            color: '#999',
            lineHeight: 22,
            align: 'center',
          },
          hr: {
            borderColor: '#aaa',
            width: '100%',
            borderWidth: 0.5,
            height: 0,
          },
          b: {
            fontSize: 16,
            lineHeight: 33,
          },
          per: {
            color: '#eee',
            backgroundColor: '#334455',
            padding: [2, 4],
            borderRadius: 2,
          },
        },
      },
      data: [
        // { value: 335, name: '考试已通过' },
        // { value: 310, name: '考试未通过' },
      ],
    },
  ],
});
const options3 = reactive({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    x: 'left',
    top: '10%',
    data: ['视频已看完', '视频未看完','考试已通过','考试未通过'],
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      selectedMode: 'single',
      radius: [0, '30%'],
      label: {
        position: 'inner',
      },
      labelLine: {
        show: false,
      },
      data: [
        // { value: 335, name: '视频已看完', selected: true },
        // { value: 679, name: '视频未看完' },
      ],
    },
    {
      name: '访问来源',
      type: 'pie',
      radius: ['40%', '55%'],
      label: {
        formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
        backgroundColor: '#eee',
        borderColor: '#aaa',
        borderWidth: 1,
        borderRadius: 4,
        rich: {
          a: {
            color: '#999',
            lineHeight: 22,
            align: 'center',
          },
          hr: {
            borderColor: '#aaa',
            width: '100%',
            borderWidth: 0.5,
            height: 0,
          },
          b: {
            fontSize: 16,
            lineHeight: 33,
          },
          per: {
            color: '#eee',
            backgroundColor: '#334455',
            padding: [2, 4],
            borderRadius: 2,
          },
        },
      },
      data: [
        // { value: 335, name: '考试已通过' },
        // { value: 310, name: '考试未通过' },
      ],
    },
  ],
});
const opArr:any[] = [options1,options2,options3]

async function init(data) {
  console.log('data',data)
  packId.value = data.id
  let typeSortArr:number[] =[0,1,2]

  changeLoading(true)
  await typeSortArr.forEach(typeSort => {
    setOptionData(typeSort)
  })
  changeLoading(false)
}

async function setOptionData(typeSort: number) {
  let eduPackProcess = await eduPackProcessApi.getEduProcessPack({
    packId:packId.value,
    typeSort:typeSort
  });

  let data1 = [
    { value: eduPackProcess.data.eduFinishUserNum, name: '视频已看完' },
    {value: eduPackProcess.data.eduUnfinishUserNum, name: '视频未看完'},
  ]
  opArr[typeSort].series[0].data= data1

  let data2 = [
    { value: eduPackProcess.data.examPassUserNum, name: '考试已通过' },
    {value: eduPackProcess.data.examUnpassUserNum, name: '考试未通过'},
  ]
  opArr[typeSort].series[1].data= data2

}


function handleClose() {
  console.log('close the Drawer。。。')

  closeDrawer();
}
</script>
<style lang="less">
</style>
