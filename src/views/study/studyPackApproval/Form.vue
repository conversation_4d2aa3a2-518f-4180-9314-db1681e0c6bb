<template>
  <div class="flow-form">
    <div class="flow-com-title">
      <h1>{{ STUDY_SPECIAL_TYPE_MAP[type] }}</h1>
    </div>
    <a-form :colon="false" :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled" style="height: 50%">
      <a-row>
        <a-col :span="24" v-if="judgeShow('studyPackId')">
          <a-form-item label="选择培训合集" name="studyPackId">
            <jnpf-select v-model:value="dataForm.studyPackId" :options="studyPackIdOptions" :allowClear="true" placeholder="请选择培训合集" :disabled="false"/>
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="judgeShow('selectPer')">
          <a-form-item label="参加人员" name="selectPerIds">
            <jnpf-user-select
              v-model:value="dataForm.selectPerIds"
              placeholder="请选择参加人员"
              :disabled="judgeWrite('selectPer')"
              :allowClear="true"
              :multiple="true"
              selectType="all"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24" class="ant-col-item" v-show="!dataForm.id">
          <a-space style="margin-left: 100px;">
            <AUpload :beforeUpload="importUserInfo" :showUploadList="false" accept=".xls,.xlsx">
              <AButton>导入培训用户信息表</AButton>
            </AUpload>
            <a href="/file/培训导入用户信息模版.xlsx">导出用户信息模版</a>
          </a-space>
        </a-col>
        <!-- 表单结束 -->
      </a-row>
    </a-form>

  </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, ref, toRefs, unref} from 'vue';
import {useFlowForm} from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
import type {FormInstance} from 'ant-design-vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {testEduRecordPackApi} from "/@/api";
import {Rule} from "/@/components/Form";
import {STUDY_SPECIAL_TYPE_MAP} from "/@/enums/zzEnums";
import JnpfSelect from "/@/components/Jnpf/Select/src/Select.vue";
import JnpfUserSelect from "/@/components/Jnpf/Organize/src/UserSelect.vue";
import {importUserInfoExcel} from "/@/importUser";

interface State {
  dataForm: any;
  dataRule: Record<string, Rule[]>;
}

defineOptions({ name: 'studyPack' });
const props = defineProps(['config']);
const emit = defineEmits(['setPageLoad', 'eventReceiver']);

const formRef = ref<FormInstance>();
const state = reactive<State>({
  dataForm: {
    flowId: '',
    flowTitle: '',
    id: '',
    // 业务字段
    studyPackId: '',
    selectPerIds: [],
    type: '',
  },
  dataRule: {
    studyPackId: [{ required: true, message: '培训计划集合不能为空', trigger: 'change' }],
    selectPerIds: [{ required: true, type: 'array', message: '参加人员不能为空', trigger: 'change' }],
  },
});

const {createConfirm} = useMessage();
const {dataForm, dataRule} = toRefs(state);
const {init, getUserInfo, judgeShow, judgeWrite, dataFormSubmit} = useFlowForm({
  config: props.config,
  selfState: state,
  emit,
  formRef,
  selfInit,
  selfGetInfo: selfInit,
});

const studyPackIdOptions = ref([])
const type = ref(window.studyType)
const id = ref("")

defineExpose({dataFormSubmit});

async function selfInit(formInfo:any) {
  console.log('formInfo', formInfo)

  // 培训类型
  if (formInfo.type) { // 更新表单
    type.value = formInfo.type
  } else { // 初始化表单
    state.dataForm.type = window.studyType; // 从列表页面传入的类型
    state.dataForm.flowTitle = unref(getUserInfo).userName + '的' + STUDY_SPECIAL_TYPE_MAP[type.value] + '申请';
  }

  let data = {
    "_sorter": "f_creator_time DESC",
  };

  if (formInfo.id) { // 编辑表单，展示培训信息
    data['id'] = formInfo.studyPackId
    id.value = formInfo.id
  } else { // 新增表单，查询未使用的培训记录
    data['isUsed'] = '0'
  }

  const res = await testEduRecordPackApi.page(data)
  studyPackIdOptions.value = res.data.list.map(i => ({ id: i.id, fullName: i.packName }))
  console.log('dataForm',state.dataForm)
}

async function importUserInfo(file: any) {
  state.dataForm.checkPerIds = await importUserInfoExcel(file, state.dataForm.checkPerIds)
  console.log('importUserInfo perIds',state.dataForm.checkPerIds)
}

onMounted(() => {
  init();
});
</script>

