<template>
  <div class="flow-form">
    <div class="flow-com-title">
      <h1>{{ STUDY_SPECIAL_TYPE_MAP[type] }}</h1>
    </div>
    <a-form :colon="false" :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled" style="height: 50%">
      <a-row>
        <a-col :span="24" v-if="judgeShow('selectTrainingId')">
          <a-form-item label="选择培训" name="selectTrainingId">
            <jnpf-select v-model:value="dataForm.selectTrainingId" :options="selectTrainingIdOptions" :allowClear="true" placeholder="请选择选择培训" :disabled="judgeWrite('selectTrainingId')" />
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="judgeShow('selectPer')">
          <a-form-item label="参加人员" name="selectPerIds">
            <jnpf-user-select
              v-model:value="dataForm.selectPerIds"
              placeholder="请选择参加人员"
              :disabled="judgeWrite('selectPer')"
              :allowClear="true"
              :multiple="true"
              selectType="all"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24" class="ant-col-item" v-show="!dataForm.id">
          <a-space style="margin-left: 100px;">
            <AUpload :beforeUpload="importUserInfo" :showUploadList="false" accept=".xls,.xlsx">
              <AButton>导入培训用户信息表</AButton>
            </AUpload>
            <a href="/file/培训导入用户信息模版.xlsx">导出用户信息模版</a>
          </a-space>
        </a-col>
        <!-- 表单结束 -->
      </a-row>
    </a-form>

    <div style="margin-top: 30px;flex: 1" class="fa-flex-center" v-if="id">
      <table style="width: 100%" class="fa-table">
        <tr>
          <td class="fa-p4 fa-text-center fa-h3">总人数</td>
          <td class="fa-p4 fa-text-center fa-h3">是否需要考试</td>
          <td class="fa-p4 fa-text-center fa-h3">视频已学完人数</td>
          <td class="fa-p4 fa-text-center fa-h3">视频未学完人数</td>
          <td class="fa-p4 fa-text-center fa-h3">考试通过人数</td>
          <td class="fa-p4 fa-text-center fa-h3">考试未通过人数</td>
          <td class="fa-p4 fa-text-center fa-h3">操作</td>
        </tr>

        <tr>
          <td class="fa-p4 fa-text-center">{{eduProcess.totalUserNum}}</td>
          <td class="fa-p4 fa-text-center">{{ eduProcess.isNeedTest === 1 ? '是' : '否' }}</td>
          <td class="fa-p4 fa-text-center">{{eduProcess.eduFinishUserNum}}</td>
          <td class="fa-p4 fa-text-center">{{eduProcess.eduUnfinishUserNum}}</td>
          <td class="fa-p4 fa-text-center">{{eduProcess.examPassUserNum}}</td>
          <td class="fa-p4 fa-text-center">{{eduProcess.examUnpassUserNum}}</td>
          <td class="fa-p4 fa-text-center" style="color: #409eff;" @click="getDetail(eduProcess)">总明细</td>
        </tr>
      </table>
    </div>

    <eduBarCode v-if="[STUDY_SPECIAL_TYPE_ENUM.MORNING_MEETING,STUDY_SPECIAL_TYPE_ENUM.ASSEMBLE_TRAIN].includes(type)" :id="id" :content="type"></eduBarCode>
    <ProcessForm @register="registerProcessForm" />
  </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, ref, toRefs, unref} from 'vue';
import {useFlowForm} from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
import type {FormInstance} from 'ant-design-vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {eduProcessApi, testEduRecordApi} from "/@/api";
import {Rule} from "/@/components/Form";
import {STUDY_SPECIAL_TYPE_ENUM, STUDY_SPECIAL_TYPE_MAP} from "/@/enums/zzEnums";
import {usePopup} from "/@/components/Popup";
import ProcessForm from "/@/views/study/eduProcessManage/ProcessForm.vue";
import eduBarCode from "./BarCode.vue";
import {importUserInfoExcel} from "/@/importUser";

interface State {
  dataForm: any;
  dataRule: Record<string, Rule[]>;
}

defineOptions({ name: 'specialTraining' });
const props = defineProps(['config']);
const emit = defineEmits(['setPageLoad', 'eventReceiver']);

const formRef = ref<FormInstance>();
const state = reactive<State>({
  dataForm: {
    flowId: '',
    flowTitle: '',
    id: '',
    // 业务字段
    selectTrainingId: '',
    selectPerIds: [],
    type: '',
  },
  dataRule: {
    selectTrainingId: [{ required: true, message: '培训计划不能为空', trigger: 'change' }],
    selectPerIds: [{ required: true, type: 'array', message: '参加人员不能为空', trigger: 'change' }],
  },
});

const {createConfirm} = useMessage();
const {dataForm, dataRule} = toRefs(state);
const {init, getUserInfo, judgeShow, judgeWrite, dataFormSubmit} = useFlowForm({
  config: props.config,
  selfState: state,
  emit,
  formRef,
  selfInit,
  selfGetInfo: selfInit,
});
const [registerProcessForm, {openPopup: openFormPopup}] = usePopup();

const selectTrainingIdOptions = ref([])
const type = ref(window.studyType)
const eduProcess = ref({
  totalUserNum:undefined,
  isNeedTest:0,
  eduFinishUserNum:undefined,
  eduUnfinishUserNum:undefined,
  examPassUserNum:undefined,
  examUnpassUserNum:undefined,
})
const id = ref("")
const eduName = ref("")

defineExpose({dataFormSubmit});

async function selfInit(formInfo:any) {
  console.log('formInfo', formInfo)

  // 培训类型
  if (formInfo.type) { // 更新表单
    type.value = formInfo.type
  } else { // 初始化表单
    state.dataForm.type = window.studyType; // 从列表页面传入的类型
    state.dataForm.flowTitle = unref(getUserInfo).userName + '的' + STUDY_SPECIAL_TYPE_MAP[type.value] + '申请';
  }

  let data = {
    "_sorter": "f_creator_time DESC",
  };
  if (formInfo.id) { // 编辑表单，展示培训信息
    data['id'] = formInfo.selectTrainingId
    id.value = formInfo.id
    eduName.value = formInfo.selectTrainingName
    await eduProcessApi.getEduRecordProcess(formInfo.selectTrainingId).then(_res=>{
      console.log("res",_res.data)
      eduProcess.value= _res.data
    })

  } else { // 新增表单，查询未使用的培训记录
    data['isUsed'] = '0'
  }
  const res = await testEduRecordApi.list(data)
  selectTrainingIdOptions.value = res.data.map(i => ({ id: i.id, fullName: i.eduName }))
}


async function importUserInfo(file: any) {
  state.dataForm.selectPerIds = await importUserInfoExcel(file, state.dataForm.selectPerIds)
  console.log('importUserInfo perIds',state.dataForm.checkPerIds)
}

async function getDetail(record) {
  const res = await eduProcessApi.getUserStatusListByEdu({
    eduRecordId: record.id,
    processType: ""
  });

  if (res.data) {
    openFormPopup(true, {detail: res.data, id: id.value,eduName:eduName.value});
  } else {
    openFormPopup(true, {detail: [], id: id.value,eduName:eduName.value});
  }
}
onMounted(() => {
  init();
});
</script>

