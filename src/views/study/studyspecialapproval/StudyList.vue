<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增</a-button>
          </template>
          <template #bodyCell="{ column, record }" style="width: 100%;">
            <template v-if="column.key === 'selectPerNames'">
              <span>{{record.selectPerNames.join(',')}}</span>
            </template>
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" style="width:30px"/>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <FaFlowCube ref="flowRef" :flow-en-code="flowCode" @reload="reload" />
    <UsersSelectModal @register="registerUsersSelect" :getDataApi="api.getSelectedPerList" :saveDataApi="api.saveSelectedPerList" @initData="reload()"/>
    <UsersSelectModal @register="registerUsersAdd" :getDataApi="api.emptyList" :saveDataApi="api.addPer" @initData="reload"/>

  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
import { studySpecialApprovalApi as api } from '/@/api';
import { genFlowDetailBtn, genFlowDeleteBtn, genFlowEditBtn, genQueryInput } from "/@/utils/tableUtils";
import FaFlowCube from "/@/components/Fa/common/src/FaFlowCube.vue";
import { useMessage } from "/@/hooks/web/useMessage";
import { FLOW_OP_TYPE, STUDY_SPECIAL_TYPE_MAP } from "/@/enums/zzEnums";
import {useModal} from "/@/components/Modal";
import UsersSelectModal from "/@/views/permission/authorize/components/UsersSelectModal.vue";
import {studySpecialColumns} from "/@/enums/studyEnum";


const [registerUsersSelect, { openModal: registerUsersSelectModal }] = useModal();
const [registerUsersAdd, { openModal: registerUsersAddModal }] = useModal();

defineOptions({name: 'StudyList'});
const props = defineProps(['type', 'flowCode'])

const { type } = props;
window.studyType = type;
console.log('指定培训类型', type, STUDY_SPECIAL_TYPE_MAP[window.studyType]);

const flowRef = ref<any>();
const { createMessage } = useMessage();

const [registerTable, {reload}] = useTable({
  api: api.page,
  columns:studySpecialColumns,
  useSearchForm: true,
  ellipsis:true,
  formConfig: {
    schemas: [
      genQueryInput('培训编号', 'selectTrainingNum'),
    ],
  },
  searchInfo: {
    type,
    '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
  },
  actionColumn: { width: 200, title: '操作', dataIndex: 'action' },
});

function getTableActions(record): ActionItem[] {
  return [
    genFlowEditBtn(record, toDetail),
    genFlowDeleteBtn(record, handleDelete),
    genFlowDetailBtn(record, toDetail),
    { label: '新增用户',
      onClick: addSelectedPer.bind(null, record.id)
    },
    { label: '编辑用户',
      onClick: editSelectedPer.bind(null, record.id)
    },
  ];
}

function emptyList(id) {
  console.log('emptyList id',id)
  return {
    data: {
      list: []
    }
  }
}

function addSelectedPer(id){
  registerUsersAddModal(true, { id });
}

function editSelectedPer(id){
  registerUsersSelectModal(true, { id });
}

function handleAdd() {
  flowRef.value.handleAdd()
}

function toDetail(record:any, opType: FLOW_OP_TYPE) {
  flowRef.value.toDetail(record, opType)
}

function handleDelete(id:string) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
