<template>
  <div class="jnpf-content-wrapper bg-white jnpf-extend-barCode" style="margin-top:30px;">
    <div style="margin-left: 30px;">
      <a-button @click="getQrcode" class="ant-btn ant-btn-primary">生成二维码</a-button>
      <div>
        <text style="margin-top: 5px;">二维码图像</text>
        <div class="fa-flex-center" style="width:300px;height: 300px; margin-top: 5px;border: solid 1px #b6b6b6;">
          <canvas id="qrcode" ref="qrCodeRef"></canvas>
        </div>
        <p class="tips" style="margin-top: 5px;">使用数智app扫一扫</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {toCanvas} from "qrcode";
import {useMessage} from "/@/hooks/web/useMessage";

defineOptions({name:'eduBarCode'})
const props = defineProps(["id","content"])
const qrCodeRef = ref();

const { createMessage } = useMessage();

function getQrcode() {
  if (!props.id) return createMessage.error('请输入二维码内容');
  let data = {
    id:props.id,
    t: props.content,
  }
  toCanvas(qrCodeRef.value, JSON.stringify(data), {
    margin: 0,
    width: 265,
  });
}

</script>

<style scoped lang="less">
</style>
