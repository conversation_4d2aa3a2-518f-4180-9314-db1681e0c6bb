<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="width"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <div class="header-steps">
      <a-steps v-model:current="activeStep" type="navigation" size="small">
        <a-step title="上传文件" disabled/>
        <a-step title="数据预览" disabled/>
        <a-step title="校验数据" disabled/>
      </a-steps>
    </div>

    <div class="import-main" v-show="activeStep == 0">
      <div class="upload">
        <div class="up_left">
          <img src="../../../assets/images/upload.png"/>
        </div>
        <div class="up_right">
          <p class="title">上传填好的数据表</p>
          <p class="tip">文件后缀名必须是xls或xlsx，文件大小不超过500KB，最多支持导入1000条数据</p>
          <jnpf-fa-upload-file-qiniu
            v-model:value="fileId"
            accept=".xls,.xlsx"
          />
        </div>
      </div>
      <div class="upload">
        <div class="up_left">
          <img src="../../../assets/images/import.png"/>
        </div>
        <div class="up_right">
          <p class="title">填写导入数据信息</p>
          <p class="tip">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除</p>
          <a-button type="link" @click="handleTemplateDownload()">下载模板</a-button>
        </div>
      </div>
    </div>

    <div class="import-main" v-show="activeStep == 1">
      <a-table :data-source="list" :columns="previewColumns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '440px' }" class="import-preview-table">
        <template #bodyCell="{ column, record, index }">
          <template v-for="item in tableData">
            <template v-if="column.key === item.key">
              <a-input v-model:value="record[column.key]" :disabled="column.key === 'age' || column.key ==='errorMsg'"/>
            </template>
<!--            <template v-if="column.key==='preApproachTime' || column.key==='startTime'">-->
<!--              {{ formatToDate(record[column.key]) }}-->
<!--            </template>-->
          </template>
          <template v-if="column.key === 'action'">
            <a-button class="action-btn" type="link" color="error" @click="handleDelItem(index)" size="small">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>

    <div class="import-main" v-show="activeStep == 2">
      <FaImportSuccessCard v-if="!result.resultType" :success-num="result.snum"/>
      <FaImportFailCard v-if="result.resultType" :success-num="result.snum" :fail-num="result.fnum">
        <a-table :data-source="resultList" :columns="errorColumns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '205px' }">
        </a-table>
      </FaImportFailCard>
    </div>

    <template #insertFooter>
      <a-button @click="handleClose(false)" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1">{{ t('common.prev') }}</a-button>
      <a-button type="primary" @click="handleNext" :loading="btnLoading" v-if="activeStep < 3" :disabled="activeStep === 0 && !fileId">
        {{ t('common.next') }}
      </a-button>
      <a-button type="primary" @click="handleClose(true)" v-else>关闭</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
import {reactive, toRefs, unref} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {useMessage} from '/@/hooks/web/useMessage';
import {useI18n} from '/@/hooks/web/useI18n';
import type {UploadFile} from 'ant-design-vue';
import JnpfFaUploadFileQiniu from "/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue";
import {checkUserInfos, importUserPreview} from '../studyadmissionrequest/helper/api';


interface State {
  activeStep: number;
  fileId: string;
  fileName: string;
  fileList: UploadFile[];
  btnLoading: boolean;
  list: any[];
  result: any;
  resultList: any[];
}

const width = document.body.clientWidth > 1000 ? document.body.clientWidth - 300 : 1000;
const emit = defineEmits(['register', 'reload', 'import']);
const [registerModal, {closeModal}] = useModalInner(init);
const {createMessage} = useMessage();
const {t} = useI18n();
const tableData = [
  {title: '姓名', dataIndex: 'name', key: 'name', width: 150},
  {title: '性别', dataIndex: 'gender', key: 'gender', width: 150},
  {title: '身份证', dataIndex: 'idCard', key: 'idCard', width: 150},
  {title: '年龄', dataIndex: 'age', key: 'age', width: 150 },
  {title: '部门', dataIndex: 'dept', key: 'dept', width: 150},
  {title: '岗位', dataIndex: 'job', key: 'job', width: 150},
  {title: '学历', dataIndex: 'degree', key: 'degree', width: 150},
  {title: '用工方式', dataIndex: 'employmentMode', key: 'employmentMode', width: 150},
  {title: '劳动单位', dataIndex: 'laborUnit', key: 'laborUnit', width: 150},
  {title: '联系方式', dataIndex: 'phone', key: 'phone', width: 150},
  {title: '持证情况', dataIndex: 'certificateStatus', key: 'certificateStatus', width: 150},
  {title: '民族', dataIndex: 'clans', key: 'clans', width: 150},
  {title: '资料状态', dataIndex: 'profileStatus', key: 'profileStatus', width: 150},
  {title: '培训状态', dataIndex: 'trainingStatus', key: 'trainingStatus', width: 150},
  {title: '特种证件状态', dataIndex: 'specialState', key: 'specialState', width: 150},
  {title: '预进场时间', dataIndex: 'preApproachTime', key: 'preApproachTime', width: 150},
  {title: '开始工作时间', dataIndex: 'startTime', key: 'startTime', width: 150},
];
const previewColumns: any[] = [
  {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
  ...tableData,
  {title: '操作', dataIndex: 'action', key: 'action', width: 50, fixed: 'right'},
];
const errorColumns: any[] = [
  {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
  {title: '异常', dataIndex: 'errorMsg', key: 'errorMsg', width: 180},
  ...tableData,
];
const state = reactive<State>({
  activeStep: 0,
  fileId: undefined,
  fileName: '',
  fileList: [],
  btnLoading: false,
  list: [],
  result: {},
  resultList: [],
});
const {activeStep, fileId, btnLoading,list, result, resultList} = toRefs(state);

function init() {
  state.activeStep = 0;
  state.fileName = '';
  state.fileList = [];
  state.btnLoading = false;
}

function handlePrev() {
  if (state.activeStep == 0) return;
  state.activeStep -= 1;
}

function handleNext() {
  if (state.activeStep == 0) {
    if (!state.fileId) return createMessage.warning('请先上传文件');
    state.btnLoading = true;
    importUserPreview(state.fileId).then(res => {
      state.list = res.data || [];
      state.btnLoading = false;
      state.activeStep += 1;
    }).catch(() => state.btnLoading = false);
    return;
  }
  // console.log('activeStep', state.activeStep)

  if (state.activeStep == 1) {
    if (!state.list.length) return createMessage.warning('导入数据为空');
    state.btnLoading = true;
    checkUserInfos({list:state.list})
      .then(res =>{
        state.result = res.data;
        state.resultList = res.data.failResult || [];
        /* 过滤异常数据*/
        filterList(res.data.failResult)
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch((e) => {
        console.log('e',e)
        state.btnLoading = false;
      });
    console.log('result',state.result )
    console.log('activeStep', state.activeStep)
  }

  if (state.activeStep == 2) {
    console.log('current step',state.activeStep)
    if (!state.list.length) return createMessage.warning('导入数据为空');
    emit('import', unref(state.list));
    // emit('import', []);
    handleClose(false);
  }
}


function filterList(failList) {
  console.log('failList',failList)
  if(!failList || !failList.length) return
  // Create a Set of unique identifiers (idCard + '-' + phone) from the failList
  const errUserSet = new Set(failList.map(i => `${i.idCard}-${i.phone}`));

  // Filter out the items from state.list that are in the errUserSet
  state.list = state.list.filter(i => !errUserSet.has(`${i.idCard}-${i.phone}`));
}

function handleTemplateDownload() {
  window.open('/file/入场人员申请模板.xlsx')
  // templateDownload().then(res => {
  //   downloadByUrl({ url: res.data?.url });
  // });
}

function handleDelItem(index) {
  state.list.splice(index, 1);
}

function handleClose(reload) {
  closeModal()
  if (reload) emit('reload');
}
</script>
