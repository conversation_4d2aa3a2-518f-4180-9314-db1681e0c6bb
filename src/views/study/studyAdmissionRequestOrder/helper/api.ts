import { defHttp } from '/@/utils/http/axios';

// 获取列表
export function getFile(data) {
  return defHttp.post({ url: '/api/example/StudyAdmissionRequest/getFile', data });
}

// 上传文件
export function uploadSubmitFile(data) {
  return defHttp.post({ url: '/api/example/StudyAdmissionRequest/uploadSubmitFile', data });
}

// 根据id删除
export function deleteById(id) {
  return defHttp.delete({ url: '/api/example/StudyAdmissionRequest/deleteById/' + id });
}