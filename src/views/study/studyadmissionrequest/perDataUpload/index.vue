<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle> 
          
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>{{ t('common.exportText') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-download button-preIcon" @click="handelBatchSubmit()" :disabled="selList.length === 0">批量提交</a-button>

          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'allotsState'">
              <LastTermState :state="record.allotsState" />
            </template>

            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
    <ExportModal @register="registerExportModal" />
    <UploadNineFile @register="registerUploadNineFile" />
  </div>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { studyPerDataApi } from '/@/api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePopup } from '/@/components/Popup';
  import Form from './Form.vue';
  import {genQueryInput, genQueryKeyword, genQuerySearch, genQueryTimeInput} from '/@/utils/tableUtils';
  import { useModal } from '/@/components/Modal';
  import LastTermState from '/@/views/extend/lastTerm/LastTermAllotsState.vue';
  import { ref } from 'vue';
  import { useUserStore } from '/@/store/modules/user';
  import ExportModal from './ExportModal.vue';
  import UploadNineFile from './UploadNineFile.vue';
  import {genSelect} from "/@/utils/formUtils";
  import {COMMON_OPTION} from "/@/enums/commonEnum";

  defineOptions({ name: 'extend-zz-project-contract-sub' });

  const selList = ref<any[]>([]); // 选中的

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const FILE_STATE_MAP = {
    1:"未提交",
    2:"已提交",
    4:"被退回，待修改",
  }

  const { t } = useI18n();
  const { createMessage, createConfirm } = useMessage();
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  const [registerUploadNineFile, { openModal: openUploadNineFile }] = useModal();
  const columns: BasicColumn[] = [
    // { title: '序号', dataIndex: 'id', width: 80 },
    { title: '姓名', dataIndex: 'name' },
    { title: '文件上传状态', dataIndex: 'fileUploadState' },
    {
      title: '流程状态', dataIndex: 'currentState',
      customRender: ({record}) => (FILE_STATE_MAP[record.fileState]), width: 180
    },
    { title: '问题原因', dataIndex: 'problemCause', width: 280 },
    { title: '性别', dataIndex: 'gender' },
    { title: '身份证', dataIndex: 'idCard', width: 180 },
    { title: '年龄', dataIndex: 'age' },
    { title: '部门', dataIndex: 'dept' },
    { title: '岗位/工种', dataIndex: 'job' },
    { title: '学历', dataIndex: 'degree' },
    { title: '用工方式', dataIndex: 'employmentMode' },
    { title: '劳动单位', dataIndex: 'laborUnit' },
    { title: '联系方式', dataIndex: 'phone' },
    { title: '持证情况', dataIndex: 'certificateStatus' },
    { title: '民族', dataIndex: 'clans' },
    { title: '资料状态', dataIndex: 'profileStatus' },
    { title: '培训状态', dataIndex: 'trainingStatus' },
    { title: '未上传文件', dataIndex: 'unUploadNames', width: 700 },
    { title: '预进场时间', dataIndex: 'preApproachTime', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '开始工作时间', dataIndex: 'startTime', width: 100, format: 'date|YYYY-MM-DD' },
  ];
  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerTable, { reload, getFetchParams, clearSelectedRowKeys }] = useTable({
    api: studyPerDataApi.page,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        genQuerySearch(),
        genQueryInput('姓名', 'name'),
        genQueryInput('身份证', 'idCard'),
        genSelect('提交状态', 'fileState', false, 'number', [
          {"fullName": "未提交", "id": 1},
          {"fullName": "已提交", "id": 2},
          {"fullName": "被退回", "id": 4},
        ]),
      ],
    },
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
    },
    rowSelection: {
      // 选中行
      onChange: (selectedRowKeys) => {
        selList.value = selectedRowKeys;
        // console.log('value:', selList.value);
        // selectedRows.forEach(row => {
        //   console.log('dept:', row.responsibleDept);
        // });
      },
    },
    searchInfo: { creatorUserId: userInfo.userId, 'fileState#$in': [1,2,4] },
  });

  function getTableActions(record: any): ActionItem[] {
    return [
      {
        label: t('common.editText'),
        onClick: addOrUpdateHandle.bind(null, record.id),
      },
      {
        label: '上传资料',
        onClick: uploadFile.bind(null, record),
      },
      {
        label: '提交',
        disabled: record.fileState === 2,
        onClick: goReview.bind(null, record),
      },
      {
        label: t('common.delText'),
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }

  // 批量提交
  function handelBatchSubmit() {
    const allotsList = selList.value;
    console.log(allotsList)
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要提交这些数据吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.batchSubmit(allotsList).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 提交
  function goReview(record) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要提交这些数据吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.submit(record.id).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 上传资料
  function uploadFile(data) {
    console.log(data);
    // if(data.isUploadFile != 1){
    //   createMessage.error('尚未通过考试，不可上传资料');
    //   return;
    // }
    openUploadNineFile(true, data);
  }

  function handleDelete(id: any) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.remove(id).then(res => {
          createMessage.success(res.msg);
          reload();
        });
      },
    });
  }

  function addOrUpdateHandle(id = '') {
    openFormPopup(true, { id });
  }

  function handleExport() {
    const listQuery = {
      ...getFetchParams(),
    };
    openExportModal(true, { listQuery });
  }
</script>
