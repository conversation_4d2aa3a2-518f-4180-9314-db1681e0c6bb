<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" title="上传人员资料附件" :width="800">
    <a-row class="dynamic-form" style="min-height: 500px">
      <a-form
        :colon="false"
        size="default"
        layout="horizontal"
        labelAlign="right"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="name">
              <template #label>姓名</template>
              <JnpfInput v-model:value="dataForm.name" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }" :disabled="true"/>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="附件批量">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.fileList" :multiple="true" @success="handleFileListSuccess" accept=".pdf" />
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <div>附件批量支持同时上传多个附件，系统会根据附件后缀名自动解析对应到相应的文件上。</div>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item label="合同">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.contractFile" prefix="contract" accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="保险文件">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.insuranceFile" prefix="contract" accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="合同签收表">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.contractReceiptedFile" prefix="test"  accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="进场承诺书">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.promiseFile" prefix="test"  accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="进场登记表">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.registrationFile" prefix="test"  accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="进场申请表">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.requestFile" prefix="test"  accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="身份证文件">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.idCardFile" prefix="test"  accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="体检文件">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.physicalExamFile" prefix="test"  accept=".pdf" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item label="知情反馈书">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.feedbackFile" prefix="test"  accept=".pdf" />
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, toRefs } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { studyPerDataApi } from '/@/api';
import JnpfFaUploadFileQiniu from '/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue';
import { useMessage } from "/@/hooks/web/useMessage";
import { each } from "lodash-es";


const emit = defineEmits(['register', 'reload']);
const [registerModal, {changeLoading, closeModal, changeOkLoading}] = useModalInner(init);
const {createMessage} = useMessage();

interface State {
  dataForm: {
    fileList: string[],
    contractFile?: string,
    insuranceFile?: string,
    contractReceiptedFile?: string,
    promiseFile?: string,
    registrationFile?: string,
    requestFile?: string,
    idCardFile?: string,
    physicalExamFile?: string,
    feedbackFile?: string,
  };
  dataRule: any;
}

const state = reactive<State>({
  dataForm: {
    fileList: [],
    contractFile: undefined,
    insuranceFile: undefined,
    contractReceiptedFile: undefined,
    promiseFile: undefined,
    registrationFile: undefined,
    requestFile: undefined,
    idCardFile: undefined,
    physicalExamFile: undefined,
    feedbackFile: undefined,
  },
  dataRule: {},
});

const {dataRule, dataForm} = toRefs(state);

function init(data) {
  // 打印外部传给modal的参数
  // console.log('init', data);
  state.dataForm = {
    fileList: [],
    contractFile: undefined,
  }
  changeLoading(true);
  studyPerDataApi.getFile(data).then(res => {
    // 打印返回数据
    console.log('res', res);
    state.dataForm = res.data;
    changeLoading(false);
  }).catch(() => changeLoading(false))
}

/**
 * 判断文件结尾字符串，自动分配到指定文件字段
 * @param file
 */
function handleFileListSuccess(file: any) {
  console.log('handleFileListSuccess', file)
  const fileName = file.originalFilename.toLocaleLowerCase()
  if (fileName.endsWith('合同.pdf')) {
    dataForm.value.contractFile = file.id;
  }
  // TODO 补全剩下的8个文件
  if (fileName.endsWith('保险.pdf')) {
    dataForm.value.insuranceFile = file.id;
  }
  if (fileName.endsWith('合同签收表.pdf')) {
    dataForm.value.contractReceiptedFile = file.id;
  }
  if (fileName.endsWith('进场承诺书.pdf')) {
    dataForm.value.promiseFile = file.id;
  }
  if (fileName.endsWith('进场登记表.pdf')) {
    dataForm.value.registrationFile = file.id;
  }
  if (fileName.endsWith('进场申请表.pdf')) {
    dataForm.value.requestFile = file.id;
  }
  if (fileName.endsWith('身份证.pdf')) {
    dataForm.value.idCardFile = file.id;
  }
  if (fileName.endsWith('体检报告.pdf')) {
    dataForm.value.physicalExamFile = file.id;
  }
  if (fileName.endsWith('知情反馈书.pdf')) {
    dataForm.value.feedbackFile = file.id;
  }
}

async function handleSubmit() {
  // 打印提交信息
  const params = {
    id: state.dataForm.id,
    fileList: state.dataForm.fileList,
    fileIdsList: state.dataForm,
    contractFile: state.dataForm.contractFile,
    insuranceFile: state.dataForm.insuranceFile,
    contractReceiptedFile: state.dataForm.contractReceiptedFile,
    promiseFile: state.dataForm.promiseFile,
    registrationFile: state.dataForm.registrationFile,
    requestFile: state.dataForm.requestFile,
    idCardFile: state.dataForm.idCardFile,
    physicalExamFile: state.dataForm.physicalExamFile,
    feedbackFile: state.dataForm.feedbackFile,
  }

  changeOkLoading(true);
  studyPerDataApi.uploadSubmitFile(params).then(res => {
    // 打印返回数据
    console.log('res', res);
    createMessage.success("上传附件成功")
    changeOkLoading(false);
    closeModal();
    emit('reload'); // 发布reload事件，外部组件接受此事件
  }).catch(() => changeOkLoading(false))
}
</script>
