<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit">
    <BasicForm @register="registerForm" class="!px-10px !mt-10px"> </BasicForm>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicPopup, usePopupInner } from '/@/components/Popup';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { studyPerDataApi } from '/@/api';
  import dayjs from 'dayjs';
  import { genDate, genInput, genSelect, genCommon } from '/@/utils/formUtils';

  const id = ref('');

  const schemas: FormSchema[] = [
    genInput('姓名', 'name', false),
    genInput('性别', 'gender', false),
    genInput('身份证', 'idCard', false),
    genInput('年龄', 'age', false),
    genInput('部门', 'dept', false),
    genInput('岗位/工种', 'job', false),
    genInput('学历', 'degree', false),
    genInput('用工方式', 'employmentMode', false),
    genInput('劳动单位', 'laborUnit', false),
    genInput('联系方式', 'phone', false),
    genInput('持证情况', 'certificateStatus', false),
    genInput('民族', 'clans', false),
    genInput('资料状态', 'profileStatus', false),
    genInput('培训状态', 'trainingStatus', false),
    genDate('预进场时间', 'preApproachTime', false),
    genDate('开始工作时间', 'startTime', false),
    
  ];
  const getTitle = computed(() => (!unref(id) ? '新建人员信息' : '编辑人员信息'));
  const emit = defineEmits(['register', 'reload']);
  const { createMessage } = useMessage();
  const [registerForm, { setFieldsValue, validate, resetFields, updateSchema }] = useForm({ labelWidth: 120, schemas: schemas });
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

  async function init(data: any) {
    resetFields();
    id.value = data.id;
    setFieldsValue({
      picyear: dayjs().year(),
    });
    if (id.value) {
      changeLoading(true);
      studyPerDataApi.getById(id.value).then(res => {
        setFieldsValue(res.data);
        changeLoading(false);
      });
    }
  }

  
  async function handleSubmit() {
    // console.log('id:',id)
    const values = await validate();
    // console.log('values:',values)
    if (!values) return;
    changeOkLoading(true);
    const data = {
      ...values,
      id: id.value,
    };
    // console.log('id:',id.value,)
    const formMethod = id.value ? studyPerDataApi.update : studyPerDataApi.save;
    formMethod(data)
      .then(res => {
        createMessage.success(res.msg);
        changeOkLoading(false);
        closePopup();
        emit('reload');
      })
      .catch(() => {
        changeOkLoading(false);
      });
  }

</script>
