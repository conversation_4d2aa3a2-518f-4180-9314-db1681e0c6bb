<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>{{ t('common.exportText') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-download button-preIcon" @click="handelBatchSubmit()" :disabled="selList.length === 0">批量审批</a-button>
            <a-button type="link" @click="chooseExport" :disabled="selList.length === 0"><i class="icon-ym icon-ym-btn-download button-preIcon"></i> 选择导出 </a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'allotsState'">
              <LastTermState :state="record.allotsState" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
    <ExportModal @register="registerExportModal" />
    <UploadNineFile @register="registerUploadNineFile" />
  </div>
</template>
<script lang="ts" setup>
import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import { studyPerDataApi } from '/@/api';
import { useMessage } from '/@/hooks/web/useMessage';
import { usePopup } from '/@/components/Popup';
import Form from './Form.vue';
import { genQueryInput, genQueryKeyword, genQuerySearch, genQueryTimeInput } from '/@/utils/tableUtils';
import { useModal } from '/@/components/Modal';
import LastTermState from '/@/views/extend/lastTerm/LastTermAllotsState.vue';
import { ref } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import ExportModal from './ExportModal.vue';
import UploadNineFile from './UploadNineFile.vue';

defineOptions({ name: 'extend-zz-project-contract-sub' });

const selList = ref<any[]>([]); // 选中的

const userStore = useUserStore();
const userInfo = userStore.getUserInfo;

const { t } = useI18n();
const { createMessage, createConfirm } = useMessage();
const [registerExportModal, { openModal: openExportModal }] = useModal();
const [registerUploadNineFile, { openModal: openUploadNineFile }] = useModal();

// 文件上传状态选项
const fileUploadStatusOptions = [
  { id: '待上传资料', fullName: '待上传资料' },
  { id: '资料齐全', fullName: '资料齐全' },
  { id: '资料未齐全', fullName: '资料未齐全' },
];

const columns: BasicColumn[] = [
  { title: '姓名', dataIndex: 'name' },
  { title: '文件上传状态', dataIndex: 'fileUploadState' },
  { title: '问题原因', dataIndex: 'problemCause', width: 280 },
  { title: '性别', dataIndex: 'gender' },
  { title: '身份证', dataIndex: 'idCard', width: 180 },
  { title: '年龄', dataIndex: 'age' },
  { title: '部门', dataIndex: 'dept' },
  { title: '岗位/工种', dataIndex: 'job' },
  { title: '学历', dataIndex: 'degree' },
  { title: '用工方式', dataIndex: 'employmentMode' },
  { title: '劳动单位', dataIndex: 'laborUnit' },
  { title: '联系方式', dataIndex: 'phone' },
  { title: '持证情况', dataIndex: 'certificateStatus' },
  { title: '民族', dataIndex: 'clans' },
  { title: '资料状态', dataIndex: 'profileStatus' },
  { title: '培训状态', dataIndex: 'trainingStatus' },
  { title: '预进场时间', dataIndex: 'preApproachTime', width: 100, format: 'date|YYYY-MM-DD' },
  { title: '开始工作时间', dataIndex: 'startTime', width: 100, format: 'date|YYYY-MM-DD' },
];

const [registerForm, { openPopup: openFormPopup }] = usePopup();

const [registerTable, { reload, getFetchParams, clearSelectedRowKeys }] = useTable({
  api: studyPerDataApi.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      // 第一行：关键词搜索（占满整行24格，单独一行展示）
      // {
      //   field: '_search',
      //   label: '关键词',
      //   component: 'Input',
      //   componentProps: {
      //     placeholder: '请输入关键词查询',
      //     submitOnPressEnter: true,
      //   },
      //   colProps: { span: 24 }, // 单独一行，宽度充足
      // },
      // 第二行：基础搜索条件（总24格）
      {
        field: 'name',
        component: 'Input',
        label: '姓名',
        componentProps: {
          placeholder: '请输入姓名',
        },
        colProps: { span: 5 }, // 加宽基础项
      },
      {
        field: 'idCard',
        component: 'Input',
        label: '身份证',
        componentProps: {
          placeholder: '请输入身份证',
        },
        colProps: { span: 6 }, // 加宽基础项
      },
      {
        field: 'fileUploadState',
        component: 'Select',
        label: '文件上传状态',
        componentProps: {
          options: fileUploadStatusOptions,
          placeholder: '请选择上传状态',
          labelField: 'fullName',
          valueField: 'id',
          allowClear: true,
        },
        // 核心配置：强制作为基础项展示
        advanced: false,
        defaultShow: true,
        colProps: { span: 7 }, // 显著加宽，确保不被隐藏
        style: { minWidth: '260px' },
      },
      // 高级搜索项（默认隐藏）
      {
        field: 'employmentMode',
        component: 'Input',
        label: '用工方式',
        componentProps: {
          placeholder: '请输入用工方式搜索',
          allowClear: true,
        },
        colProps: { span: 6 }, // 高级项同样加宽
        advanced: true,
      },
    ],
    showAdvancedButton: true,
    isAdvanced: false,
    labelWidth: 100,
    layout: 'inline',
    // 允许表单换行，避免拥挤
    wrap: true,
  },
  actionColumn: {
    width: 220,
    title: '操作',
    dataIndex: 'action',
  },
  rowSelection: {
    onChange: (selectedRowKeys, selectedRows) => {
      selList.value = selectedRows;
    },
  },
  searchInfo: { fileState: 2 },
});

  function getTableActions(record: any): ActionItem[] {
    return [
      {
        label: t('common.editText'),
        onClick: addOrUpdateHandle.bind(null, record.id),
      },
      {
        label: '查看资料',
        onClick: uploadFile.bind(null, record),
      },
      {
        label: '审批',
        onClick: goReview.bind(null, record),
      },
      {
        label: '退回',
        onClick: withDraw.bind(null, record),
      },
      {
        label: t('common.delText'),
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }

  // 选择导出
  function chooseExport() {
    const allotsList = selList.value;
    console.log(allotsList)
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '是否选择这些数据导出吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.chooseExport(allotsList);
        clearSelectedRowKeys();
        reload();
      },
    });
  }

  // 批量审批
  function handelBatchSubmit() {
    const allotsList = selList.value;
    console.log(allotsList)
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要提交这些数据吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.batchReview(allotsList).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 审批
  function goReview(record) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要审批这些数据吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.review(record.id).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 退回
  function withDraw(record) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要退回这条数据吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.withDraw(record.id).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 上传资料
  function uploadFile(data) {
    console.log(data);
    openUploadNineFile(true, data);
  }

  function handleDelete(id: any) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        studyPerDataApi.remove(id).then(res => {
          createMessage.success(res.msg);
          reload();
        });
      },
    });
  }

  function addOrUpdateHandle(id = '') {
    openFormPopup(true, { id });
  }

  function handleExport() {
    const listQuery = {
      ...getFetchParams(),
    };
    openExportModal(true, { listQuery });
  }
</script>
