import { defHttp } from '/@/utils/http/axios';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: '/api/example/StudyAdmissionRequest/getList', data });
}
// 获取入场列表
export function getRequestList(data) {
  return defHttp.get({ url: '/api/example/StudyAdmissionRequestOrder', data });
}
// 获取人员子表
export function getUserEntryList(id) {
  return defHttp.get({ url: '/api/example/StudyAdmissionRequestOrder' + `/userEntry/${id}/Items` });
}
// 删除入场信息
export function delAdmission(id) {
  return defHttp.delete({ url: '/api/example/StudyAdmissionRequestOrder' + '/' + id });
}
// 新建
export function create(data) {
  return defHttp.post({ url:'/api/example/StudyAdmissionRequest', data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: '/api/example/StudyAdmissionRequest/'+ data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: '/api/example/StudyAdmissionRequest/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: '/api/example/StudyAdmissionRequest/detail/' + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: '/api/example/StudyAdmissionRequest/' + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: '/api/example/StudyAdmissionRequest/batchRemove', data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: '/api/example/StudyAdmissionRequest/Actions/Export', data });
}

// 上传人员信息
export function importUserInfo() {
  return defHttp.post({ url: '/api/example/StudyAdmissionRequestOrder/importUserInfo' });
}

// 上传入场人员信息预览
export function importUserPreview(fileId) {
  return defHttp.get({ url: `/api/example/StudyAdmissionRequestOrder/importUserPreview/${fileId}` });
}

export function templateDownload() {
  return defHttp.get({ url: '/api/example/StudyAdmissionRequest/TemplateDownload' });
}

// 上传入场人员信息预览
export function checkUserInfos(params) {
  return defHttp.post({ url: `/api/example/StudyAdmissionRequestOrder/checkUserInfos`,params });
}
