<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新建入场申请单</a-button>
          </template>
          <template #expandedRowRender="{ record }">
            <a-tabs size="small" v-loading="record.childTableLoading">
              <a-tab-pane key="1" tab="入场人员列表">
                <BasicTable @register="registerGoodsTable" :data-source="record.goodsList"> </BasicTable> </a-tab-pane
              >currentState
            </a-tabs>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'selectPerNames'">
              <span>{{ record.selectPerNames.join(',') }}</span>
            </template>
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <FaFlowCube ref="flowRef" flow-en-code="StudyAdmissionRequestOrder" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { delAdmission, getRequestList, getUserEntryList } from './helper/api';
  import { studyAdmissionApi } from '/@/api';
  import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import FaFlowCube from '/@/components/Fa/common/src/FaFlowCube.vue';
  import { genQueryKeyword } from '/@/utils/tableUtils';
  import { FLOW_OP_TYPE } from '/@/enums/zzEnums';
  import { genFlowDetailBtn, genFlowDeleteBtn, genFlowEditBtn } from '/@/utils/tableUtils';

  defineOptions({ name: 'study-admission-request' });

  const { createMessage } = useMessage();
  const { t } = useI18n();
  const flowRef = ref<any>();
  const columns: BasicColumn[] = [
    { title: '申请人', dataIndex: 'requestUserName', width: 150 },
    { title: '申请部门', dataIndex: 'admissionRequestName' },
    { title: '用工形式', dataIndex: 'candidateApproval' },
    { title: '创建时间', dataIndex: 'creatorTime', width: 170, sorter: true, format: 'date|YYYY-MM-DD HH:mm:ss' },
    { title: '状态', dataIndex: 'currentState', width: 120, align: 'center', sorter: true },
  ];
  const goodsColumns: BasicColumn[] = [
    { title: '姓名', dataIndex: 'name' },
    { title: '性别', dataIndex: 'gender' },
    { title: '身份证', dataIndex: 'idCard' },
    { title: '年龄', dataIndex: 'age' },
    { title: '部门', dataIndex: 'dept' },
    { title: '岗位', dataIndex: 'job' },
    { title: '学历', dataIndex: 'degree' },
    { title: '用工方式', dataIndex: 'employmentMode' },
    { title: '劳动单位', dataIndex: 'laborUnit' },
    { title: '联系方式', dataIndex: 'phone' },
    { title: '持证情况', dataIndex: 'certificateStatus' },
    { title: '民族', dataIndex: 'clans' },
    { title: '预进场时间', dataIndex: 'preApproachTime', width: 120, format: 'date|YYYY-MM-DD' },
    { title: '开始工作时间', dataIndex: 'startTime', width: 120, format: 'date|YYYY-MM-DD' },
  ];

  const [registerTable, { reload }] = useTable({
    api: getRequestList,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        genQueryKeyword(),
        {
          field: 'pickerVal',
          label: '日期',
          component: 'DateRange',
        },
      ],
      fieldMapToTime: [['pickerVal', ['startTime', 'endTime']]],
    },
    actionColumn: { width: 200, title: '操作', dataIndex: 'action' },
    onExpand: handleExpand,
  });
  const [registerGoodsTable] = useTable({
    columns: goodsColumns,
    pagination: false,
    showTableSetting: false,
    canResize: false,
    scroll: { x: undefined },
  });

  function getTableActions(record): ActionItem[] {
    return [
      genFlowEditBtn(record, toDetail),
      genFlowDeleteBtn(record, handleDelete),
      genFlowDetailBtn(record, toDetail),
      // {
      //   label: '生成名单',
      //   modelConfirm: {
      //     content: '您确定要生成人员进场名单吗？',
      //     onOk: generateFile.bind(null, record.id),
      //   },
      // },
    ];
  }

  function handleAdd() {
    flowRef.value.handleAdd();
  }

  function toDetail(record: any, opType: FLOW_OP_TYPE) {
    flowRef.value.toDetail(record, opType);
  }

  function handleDelete(id: string) {
    delAdmission(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  function handleExpand(expanded, record) {
    if (!expanded || record.goodsList?.length || record.planList?.length) return;
    record.childTableLoading = true;
    console.log(record);
    getUserEntryList(record.id)
      .then(res => {
        record.childTableLoading = false;
        record.goodsList = res.data.list;
      })
      .catch(() => {
        record.childTableLoading = false;
      });
  }
</script>
