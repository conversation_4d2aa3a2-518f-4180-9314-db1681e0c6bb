<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" @click="syncType()">同步用户培训类型</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fullName'"><i :class="'mr-6px ' + record.icon"></i>{{ record.approvalNum }}</template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
            <template v-if="isProcessType(column.key)">
              <TableAction :actions="getProcessUserNum(record,column.key)"/>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <ProcessForm @register="registerForm" @reload="reload"/>
  </div>
</template>
<script lang="ts" setup>
import {ActionItem, BasicTable, TableAction, useTable} from '/@/components/Table';
import {useI18n} from '/@/hooks/web/useI18n';
import {useMessage} from '/@/hooks/web/useMessage';
import {usePopup} from '/@/components/Popup';
import ProcessForm from './ProcessForm.vue';
// import {batchDelete} from "/@/views/testManage/questionManage/helper/api";
import {eduProCol, isProcessType, proDetailsCol} from "/@/enums/eduEnum";
import {exportTable} from "/@/utils/file/table";
import {isNil} from "lodash-es";
import {eduProcessApi, testUserEduRecordApi} from "/@/api";

defineOptions({name: 'eduProcessManage'});

const {t} = useI18n();
const {createMessage} = useMessage();

const [registerForm, {openPopup: openFormPopup}] = usePopup();
const [registerTable, {reload}] = useTable({
  api: eduProcessApi.getList,
  columns: eduProCol,
  isTreeTable: true,
  useSearchForm: true,
  pagination: false,
  formConfig: {
    schemas: [
      {
        field: 'approvalNum',
        label: '审批编号',
        component: 'Input',
        componentProps: {
          submitOnPressEnter: true,
        },
      },
      {
        field: 'selectTrainingNum',
        label: '培训计划编号',
        component: 'Input',
        componentProps: {
          submitOnPressEnter: true,
        },
      }
    ],
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
  },
  afterFetch: data => setTableIndex(data),
});
// const exportUserStatus = ref([]);


// 树形列表index层级
function setTableIndex(arr, index = 0) {
  arr.forEach(item => {
    item.index = 1;
    if (index) item.index = index + 1;
    if (item.children) setTableIndex(item.children, item.index);
  });
}

function syncType(){
  testUserEduRecordApi.syncType()
}

function getTableActions(record): ActionItem[] {
  return [
    {
      label: record.parentId === '-1' ? '' : t('总明细'),
      onClick: getDetail.bind(null, record),
    }
  ];
}

function getProcessUserNum(record,proType):ActionItem[]{
  return [
    {
      label: isNil(record[proType]) ? '' : JSON.stringify(record[proType]),
      onClick: getUserDetail.bind(null, record.id, proType),
    }
  ];
}

async function getUserDetail(recordId, proType) {
  let userStatusList = await getUserStatus(recordId, proType)
  exportTable(proDetailsCol, userStatusList, '用户明细.xlsx')
}

async function getUserStatus(eduRecordId: string, processType: string) {
  const res = await eduProcessApi.getUserStatusListByEdu({
    eduRecordId:eduRecordId,
    processType:processType
  });
  if (!Array.isArray(res.data) || res.data.length <= 0) return new Array;
  return res.data
  // exportUserStatus.value = res.data
}


async function getDetail(record) {
  let userStatusList =await getUserStatus(record.id, "")
  openFormPopup(true, {detail: userStatusList, id: record.selectTrainingId,eduName:record.selectTrainingName});
}
</script>
