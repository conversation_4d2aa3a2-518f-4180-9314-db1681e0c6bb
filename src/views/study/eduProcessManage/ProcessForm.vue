<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <div class="fa-flex-row" style="margin: 10px;border-bottom: solid 1px black">
      <button class="ant-btn ant-btn-primary" style="margin-right: 5px;margin-bottom: 5px;" @click="exportUserVideoProcess()">导出课时进度明细</button>
      <button class="ant-btn ant-btn-primary" style=" margin-right: 5px;margin-bottom: 5px;" @click="exportUserProcess">导出课程进度明细</button>
      <button class="ant-btn ant-btn-primary" style=" margin-right: 5px;margin-bottom: 5px;" @click="changeByUsers">选择用户查看</button>
    </div>
    <a-table :columns="proDetailsCol" :data-source="userStatusList">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'isPass'">
          <div>{{ record.isPass == 1 ? '及格' : record.isPass == 0 ? '不及格' : '' }}</div>
        </template>
      </template>
    </a-table>

    <UsersSelectModal @register="registerUsersSelect" :getDataApi="approvalApi.getSelectedPerList" :saveDataApi="eduProcessApi.getProcessListByUsers" @initData="reloadData"/>
  </BasicPopup>
</template>
<script lang="ts" setup>
import {ref} from 'vue';
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import * as XLSX from 'xlsx';
import {exportProCol, proDetailsCol} from "/@/enums/eduEnum";
import {exportTable} from "/@/utils/file/table";
import UsersSelectModal from "/@/views/permission/authorize/components/UsersSelectModal.vue";
import {useModal} from "/@/components/Modal";
import {eduProcessApi, studySpecialApprovalApi as approvalApi} from '/@/api';
import {formatToDate} from "/@/utils/dateUtil";

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);
const [registerUsersSelect, { openModal: registerUsersSelectModal }] = useModal();

const userStatusList = ref<any[]>([]);
const id = ref('');
const eduName = ref('');

async function init(data) {
  console.log('data',data)
  id.value = data.id
  eduName.value = data.eduName
  userStatusList.value = data.detail;
}

// async function exportUserVideoProcess() {
//   exportTable(exportProCol,userStatusList.value,'用户课时进度明细.xlsx')
// }

async function exportUserVideoProcess() {
  //提取表头
  const tHeader = exportProCol.map(exportColumns => exportColumns.title);
  // 将数据映射到表头，只取列定义中指定的键
  let data = userStatusList.value.reduce((acc, item) => {
    // 将每个userVideoProcess数组映射并展平到data数组中
    acc.push(...item.userVideoProcess.map(processItem => {
      return exportProCol.map(column => processItem[column.dataIndex]);
    }));
    return acc;
  }, []);

  console.log('tHeader', tHeader)
  console.log('data', data)
  // 将数据数组转换为工作表
  const ws = XLSX.utils.aoa_to_sheet([tHeader, ...data]);
  // 将工作表转换为工作簿
  const wb = XLSX.utils.book_new();
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  // 生成Excel文件
  XLSX.writeFile(wb, eduName.value+'_课时进度明细'+formatToDate(new Date()).replaceAll(':','_')+'.xlsx')
}

async function exportUserProcess() {
  exportTable(proDetailsCol, userStatusList.value, eduName.value+'_课程进度明细'+formatToDate(new Date()).replaceAll(':','_')+'.xlsx')
}

function changeByUsers(){
  registerUsersSelectModal(true, {id: id.value});
}

function handleClose() {
}

function reloadData(data){
  userStatusList.value = data
}
async function handleSubmit() {
  closePopup();
  emit('reload');
}
</script>

