<template>
  <div class="flow-form">
    <a-form
      :colon="false"
      size="default"
      layout="horizontal"
      labelAlign="right"
      :labelCol="{ style: { width: '100px' } }"
      :model="dataForm"
      :rules="dataRule"
      ref="formRef"
      :disabled="config.disabled">
      <a-row :gutter="15">
        <!-- 具体表单 -->
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('name')">
          <a-form-item name="name">
            <template #label>姓名</template>
            <JnpfInput
              v-model:value="dataForm.name"
              @change="changeData('name', -1)"
              placeholder="请输入"
              :disabled="judgeWrite('name')"
              :allowClear="true"
              :style="{ width: '100%' }">
            </JnpfInput>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('idCard')">
          <a-form-item name="idCard">
            <template #label>身份证</template>
            <JnpfInput
              v-model:value="dataForm.idCard"
              @change="changeData('idCard', -1)"
              placeholder="请输入"
              :disabled="judgeWrite('idCard')"
              :allowClear="true"
              :style="{ width: '100%' }">
            </JnpfInput>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('dept')">
          <a-form-item name="dept">
            <template #label>部门</template>
            <JnpfDepSelect
              v-model:value="dataForm.dept"
              @change="changeData('dept', -1)"
              placeholder="请选择"
              :disabled="judgeWrite('dept')"
              :allowClear="true"
              :style="{ width: '100%' }"
              :showSearch="false"
              selectType="all">
            </JnpfDepSelect>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('trainingType')">
          <a-form-item name="trainingType">
            <template #label>培训类型</template>
            <JnpfSelect
              v-model:value="dataForm.trainingType"
              @change="changeData('trainingType', -1)"
              placeholder="请选择"
              :disabled="judgeWrite('trainingType')"
              :allowClear="true"
              :style="{ width: '100%' }"
              :showSearch="false"
              :options="optionsObj.trainingTypeOptions"
              :fieldNames="optionsObj.trainingTypeProps">
            </JnpfSelect>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('idCardLink')">
          <a-form-item name="idCardLink">
            <template #label>身份证照片</template>
            <JnpfUploadImg
              v-model:value="dataForm.idCardLink"
              @change="changeData('idCardLink', -1)"
              :fileSize="10"
              :disabled="judgeWrite('idCardLink')"
              sizeUnit="MB"
              :limit="9"
              pathType="defaultPath"
              :isAccount="0">
            </JnpfUploadImg>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('physicalExaminationLink')">
          <a-form-item name="physicalExaminationLink">
            <template #label>体检报告</template>
            <JnpfUploadFile
              v-model:value="dataForm.physicalExaminationLink"
              @change="changeData('physicalExaminationLink', -1)"
              :fileSize="10"
              :disabled="judgeWrite('physicalExaminationLink')"
              sizeUnit="MB"
              :limit="9"
              pathType="defaultPath"
              :isAccount="0"
              buttonText="点击上传">
            </JnpfUploadFile>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('noCrime')">
          <a-form-item name="noCrime">
            <template #label>无犯罪证明</template>
            <JnpfUploadFile
              v-model:value="dataForm.noCrime"
              @change="changeData('noCrime', -1)"
              :fileSize="10"
              :disabled="judgeWrite('noCrime')"
              sizeUnit="MB"
              :limit="9"
              pathType="defaultPath"
              :isAccount="0"
              buttonText="点击上传">
            </JnpfUploadFile>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('dataInfo')">
          <a-form-item name="dataInfo">
            <template #label>资料信息</template>
            <JnpfInput
              v-model:value="dataForm.dataInfo"
              @change="changeData('dataInfo', -1)"
              placeholder="请输入"
              :disabled="judgeWrite('dataInfo')"
              :allowClear="true"
              :style="{ width: '100%' }">
            </JnpfInput>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" v-if="judgeShow('trainingInfo')">
          <a-form-item name="trainingInfo">
            <template #label>培训状态</template>
            <JnpfInput
              v-model:value="dataForm.trainingInfo"
              @change="changeData('trainingInfo', -1)"
              placeholder="请输入"
              :disabled="judgeWrite('trainingInfo')"
              :allowClear="true"
              :style="{ width: '100%' }">
            </JnpfInput>
          </a-form-item>
        </a-col>
        <!-- 表单结束 -->
      </a-row>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, toRefs, onMounted, ref, unref, computed, nextTick, toRaw } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { JnpfRelationForm } from '/@/components/Jnpf';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { thousandsFormat, getDateTimeUnit, getTimeUnit } from '/@/utils/jnpf';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
  import { getDataInterfaceRes } from '/@/api/systemData/dataInterface';
  import dayjs from 'dayjs';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
  }
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);

  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      name: undefined,
      idCard: undefined,
      dept: undefined,
      trainingType: '',
      idCardLink: [],
      physicalExaminationLink: [],
      noCrime: [],
      dataInfo: undefined,
      trainingInfo: undefined,
    },

    tableRows: {},

    dataRule: {},

    optionsObj: {
      trainingTypeOptions: [
        { fullName: '安全培训（一级）', id: '1' },
        { fullName: '安全培训（二级）', id: '2' },
        { fullName: '安全培训（三级）', id: '3' },
        { fullName: '企业文化', id: '4' },
        { fullName: '质量培训', id: '5' },
      ],
      trainingTypeProps: { label: 'fullName', value: 'id' },
    },

    childIndex: -1,
    isEdit: false,
    interfaceRes: {
      trainingType: [],
      dataInfo: [],
      idCard: [],
      idCardLink: [],
      trainingInfo: [],
      name: [],
      dept: [],
      physicalExaminationLink: [],
      noCrime: [],
    },
    //可选范围默认值
    ableAll: {},
  });

  const { createMessage, createConfirm } = useMessage();
  const { dataForm, dataRule, optionsObj, ableAll } = toRefs(state);
  const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
    selfInit,
    selfGetInfo,
  });

  defineExpose({ dataFormSubmit });
  function selfInit() {
    state.childIndex = -1;
  }
  function selfGetInfo(dataForm) {}
  onMounted(() => {
    init();
  });

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function changeDataFormData(type, data, model, index, defaultValue) {
    if (!state.isEdit) {
      if (type == 2) {
        for (let i = 0; i < state.dataForm[data].length; i++) {
          if (index == -1) {
            state.dataForm[data][i][model] = defaultValue;
          } else if (index == i) {
            state.dataForm[data][i][model] = defaultValue;
          }
        }
      } else {
        state.dataForm[data] = defaultValue;
      }
    }
  }
  function getRelationDate(timeRule, timeType, timeTarget, timeValueData, dataValue) {
    let timeDataValue: any = null;
    let timeValue = Number(timeValueData);
    if (timeRule) {
      if (timeType == 1) {
        timeDataValue = timeValue;
      } else if (timeType == 2) {
        timeDataValue = dataValue;
      } else if (timeType == 3) {
        timeDataValue = new Date().getTime();
      } else if (timeType == 4 || timeType == 5) {
        const type = getTimeUnit(timeTarget);
        const method = timeType == 4 ? 'subtract' : 'add';
        timeDataValue = dayjs()[method](timeValue, type).valueOf();
      }
    }
    return timeDataValue;
  }
  function getRelationTime(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
    let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType;
    let timeDataValue: any = null;
    if (timeRule) {
      if (timeType == 1) {
        timeDataValue = timeValue || '00:00:00';
        if (timeDataValue.split(':').length == 3) {
          timeDataValue = timeDataValue;
        } else {
          timeDataValue = timeDataValue + ':00';
        }
      } else if (timeType == 2) {
        timeDataValue = dataValue;
      } else if (timeType == 3) {
        timeDataValue = dayjs().format(format);
      } else if (timeType == 4 || timeType == 5) {
        const type = getTimeUnit(timeTarget + 3);
        const method = timeType == 4 ? 'subtract' : 'add';
        timeDataValue = dayjs()[method](timeValue, type).format(format);
      }
    }
    return timeDataValue;
  }
</script>
