const columnList = [
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '姓名',
    label: '姓名',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 101,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '姓名',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'study_info_approval',
      renderKey: 1712797907338,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'name',
    width: null,
    __vModel__: 'name',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'name',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'depSelect',
    ableIds: [],
    multiple: false,
    fullName: '部门',
    label: '部门',
    sortable: false,
    align: 'left',
    __config__: {
      formId: 102,
      visibility: ['pc', 'app'],
      jnpfKey: 'depSelect',
      defaultValue: null,
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '部门',
      trigger: 'change',
      showLabel: true,
      required: false,
      tableName: 'study_info_approval',
      renderKey: 1712797911823,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-tree-department1',
      defaultCurrent: false,
      tag: 'JnpfDepSelect',
      regList: [],
      span: 24,
    },
    prop: 'dept',
    width: null,
    __vModel__: 'dept',
    fixed: 'none',
    style: { width: '100%' },
    selectType: 'all',
    disabled: false,
    id: 'dept',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '资料信息',
    label: '资料信息',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 106,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '资料信息',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'study_info_approval',
      renderKey: 1712797929108,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'dataInfo',
    width: null,
    __vModel__: 'dataInfo',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'dataInfo',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '培训状态',
    label: '培训状态',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 107,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '培训状态',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'study_info_approval',
      renderKey: 1712797930435,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'trainingInfo',
    width: null,
    __vModel__: 'trainingInfo',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'trainingInfo',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '身份证',
    label: '身份证',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 109,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '身份证',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'study_info_approval',
      renderKey: 1712814626991,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'idCard',
    width: null,
    __vModel__: 'idCard',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'idCard',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'select',
    multiple: false,
    fullName: '培训类型',
    label: '培训类型',
    sortable: false,
    align: 'left',
    props: { label: 'fullName', value: 'id' },
    __config__: {
      formId: 110,
      visibility: ['pc', 'app'],
      jnpfKey: 'select',
      defaultValue: '',
      noShow: false,
      dataType: 'static',
      dictionaryType: '',
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '培训类型',
      trigger: 'change',
      propsUrl: '',
      templateJson: [],
      showLabel: true,
      required: false,
      tableName: 'study_info_approval',
      renderKey: 1712885857596,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-select',
      propsName: '',
      tag: 'JnpfSelect',
      regList: [],
      span: 24,
    },
    prop: 'trainingType',
    width: null,
    options: [
      { fullName: '安全培训（一级）', id: '1' },
      { fullName: '安全培训（二级）', id: '2' },
      { fullName: '安全培训（三级）', id: '3' },
      { fullName: '企业文化', id: '4' },
      { fullName: '质量培训', id: '5' },
    ],
    __vModel__: 'trainingType',
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'trainingType',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
];
export default columnList;
