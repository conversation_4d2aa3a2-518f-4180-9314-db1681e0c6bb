<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" :width="800">
    <div class="detail-container">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="申请单号">
          {{ detailData.applicationNo }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人">
          {{ detailData.applicantName }}
        </a-descriptions-item>
        <a-descriptions-item label="申请部门">
          {{ detailData.applyDepartment }}
        </a-descriptions-item>
        <a-descriptions-item label="用车日期">
          {{ formatToDate(detailData.useDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="预计时长">
          {{ detailData.estimatedDuration }} 小时
        </a-descriptions-item>
        <a-descriptions-item label="优先级">
          <a-tag :color="getPriorityColor(detailData.priority)">
            {{ getPriorityText(detailData.priority) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="用车目的" :span="2">
          {{ detailData.usePurpose }}
        </a-descriptions-item>
        <a-descriptions-item label="分配车辆" v-if="detailData.assignedVehicleNumber">
          {{ detailData.assignedVehicleNumber }}
        </a-descriptions-item>
        <a-descriptions-item label="申请状态" v-if="detailData.status">
          <a-tag :color="getStatusColor(detailData.status)">
            {{ getStatusText(detailData.status) }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 审核信息 -->
      <div v-if="detailData.teamLeaderOpinion || detailData.engineeringDeptOpinion" class="review-section">
        <a-divider>审核信息</a-divider>
        
        <div v-if="detailData.teamLeaderOpinion" class="review-item">
          <h4>队长审核</h4>
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item label="审核意见">
              {{ detailData.teamLeaderOpinion }}
            </a-descriptions-item>
            <a-descriptions-item label="审核时间">
              {{ formatToDateTime(detailData.teamLeaderReviewTime) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div v-if="detailData.engineeringDeptOpinion" class="review-item">
          <h4>工程部审核</h4>
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item label="审核意见">
              {{ detailData.engineeringDeptOpinion }}
            </a-descriptions-item>
            <a-descriptions-item label="审核时间">
              {{ formatToDateTime(detailData.engineeringDeptReviewTime) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>

      <!-- 使用记录 -->
      <div v-if="detailData.actualStartTime || detailData.actualEndTime" class="usage-section">
        <a-divider>使用记录</a-divider>
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="实际开始时间">
            {{ formatToDateTime(detailData.actualStartTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际结束时间">
            {{ formatToDateTime(detailData.actualEndTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际使用时长" :span="2">
            {{ detailData.actualDuration }} 台班
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 基础信息 -->
      <div class="base-info-section">
        <a-divider>基础信息</a-divider>
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="创建人">
            {{ detailData.creatorUserName }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatToDateTime(detailData.creatorTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="最后修改人" v-if="detailData.lastModifyUserName">
            {{ detailData.lastModifyUserName }}
          </a-descriptions-item>
          <a-descriptions-item label="最后修改时间" v-if="detailData.lastModifyTime">
            {{ formatToDateTime(detailData.lastModifyTime) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { vehicleApplicationApi } from '/@/api';
  import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';
  import { 
    getVehicleApplicationPriorityText,
    getVehicleApplicationStatusText 
  } from '/@/enums/zzEnums';

  defineOptions({ name: 'vehicle-application-detail' });

  const detailData = ref<any>({});

  const getTitle = computed(() => `用车申请详情 - ${detailData.value.applicationNo || ''}`);

  const [registerModal] = useModalInner(init);

  async function init(data: any) {
    if (data.record) {
      detailData.value = data.record;
    } else if (data.id) {
      try {
        const result = await vehicleApplicationApi.getById(data.id);
        detailData.value = result.data || {};
      } catch (error) {
        console.error('获取详情失败:', error);
      }
    }
  }

  function getPriorityColor(priority: string) {
    return priority === '2' ? 'red' : 'blue';
  }

  function getPriorityText(priority: string) {
    return getVehicleApplicationPriorityText(priority);
  }

  function getStatusColor(status: string) {
    const colorMap = {
      '1': 'processing',
      '2': 'success',
      '3': 'warning',
      '4': 'processing',
      '5': 'success',
      '6': 'error',
    };
    return colorMap[status] || 'default';
  }

  function getStatusText(status: string) {
    return getVehicleApplicationStatusText(status);
  }
</script>

<style scoped lang="less">
.detail-container {
  .review-section,
  .usage-section,
  .base-info-section {
    margin-top: 16px;
  }

  .review-item {
    margin-bottom: 16px;

    h4 {
      margin-bottom: 8px;
      color: #1890ff;
    }
  }
}
</style>
