import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Form';
import { 
  VEHICLE_APPLICATION_PRIORITY_OPTIONS,
  VEHICLE_APPLICATION_STATUS_OPTIONS,
  getVehicleApplicationPriorityText,
  getVehicleApplicationStatusText
} from '/@/enums/zzEnums';

// 表格列定义
export const columns: BasicColumn[] = [
  { title: '申请单号', dataIndex: 'applicationNo', width: 180, fixed: 'left' },
  { title: '申请人', dataIndex: 'applicantName', width: 100 },
  { title: '申请部门', dataIndex: 'applyDepartment', width: 120 },
  { title: '用车日期', dataIndex: 'useDate', width: 120, format: 'date|YYYY-MM-DD' },
  { title: '预计时长(小时)', dataIndex: 'estimatedDuration', width: 120 },
  { title: '用车目的', dataIndex: 'usePurpose', width: 200, ellipsis: true },
  { title: '优先级', dataIndex: 'priority', width: 80 },
  { title: '分配车辆', dataIndex: 'assignedVehicleNumber', width: 120 },
  { title: '审批状态', dataIndex: 'currentState', width: 100 },
  { title: '当前节点', dataIndex: 'currentNodeName', width: 120 },
  { title: '制单人员', dataIndex: 'creatorUser', width: 100 },
  { title: '发起时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm' },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'applicationNo',
    label: '申请单号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入申请单号',
    },
  },
  {
    field: 'applicantId',
    label: '申请人',
    component: 'UserSelect',
    componentProps: {
      placeholder: '请选择申请人',
    },
  },
  {
    field: 'applyDepartment',
    label: '申请部门',
    component: 'Input',
    componentProps: {
      placeholder: '请输入申请部门',
    },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    componentProps: {
      placeholder: '请选择优先级',
      options: VEHICLE_APPLICATION_PRIORITY_OPTIONS,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: VEHICLE_APPLICATION_STATUS_OPTIONS,
    },
  },
  {
    field: 'useDate',
    label: '用车日期',
    component: 'DateRange',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    field: 'creatorTime',
    label: '申请时间',
    component: 'DateRange',
    componentProps: {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
    },
  },
];

// 工具函数
export function getPriorityColor(priority: string): string {
  return priority === '2' ? 'red' : 'blue';
}

export function getPriorityText(priority: string): string {
  return getVehicleApplicationPriorityText(priority);
}

export function getStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    '1': 'processing',
    '2': 'success', 
    '3': 'warning',
    '4': 'processing',
    '5': 'success',
    '6': 'error',
  };
  return colorMap[status] || 'default';
}

export function getStatusText(status: string): string {
  return getVehicleApplicationStatusText(status);
}

// 表单验证规则
export const formRules = {
  applicationNo: [
    { required: true, message: '申请单号不能为空', trigger: 'blur' },
  ],
  applicantId: [
    { required: true, message: '申请人不能为空', trigger: 'change' },
  ],
  applyDepartment: [
    { required: true, message: '申请部门不能为空', trigger: 'blur' },
  ],
  useDate: [
    { required: true, message: '用车日期不能为空', trigger: 'change' },
  ],
  usePurpose: [
    { required: true, message: '用车目的不能为空', trigger: 'blur' },
    { max: 500, message: '用车目的不能超过500个字符', trigger: 'blur' },
  ],
  priority: [
    { required: true, message: '优先级不能为空', trigger: 'change' },
  ],
};

// 导出字段映射
export const exportFieldMap = {
  applicationNo: '申请单号',
  applicantName: '申请人',
  applyDepartment: '申请部门',
  useDate: '用车日期',
  estimatedDuration: '预计时长(小时)',
  usePurpose: '用车目的',
  priority: '优先级',
  assignedVehicleNumber: '分配车辆',
  status: '申请状态',
  teamLeaderOpinion: '队长审核意见',
  engineeringDeptOpinion: '工程部审核意见',
  actualStartTime: '实际开始时间',
  actualEndTime: '实际结束时间',
  actualDuration: '实际使用时长',
  creatorUserName: '创建人',
  creatorTime: '创建时间',
};

// 状态流转映射
export const statusFlowMap = {
  '1': ['2', '6'], // 待审核 -> 已审核/已拒绝
  '2': ['3'],      // 已审核 -> 已分配
  '3': ['4'],      // 已分配 -> 使用中
  '4': ['5'],      // 使用中 -> 已完成
  '5': [],         // 已完成 -> 无后续状态
  '6': [],         // 已拒绝 -> 无后续状态
};


