<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增申请</a-button>
            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon" />导入</a-button>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon" />导出</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'useDate'">
              <span>{{ formatToDate(record.useDate) || "" }}</span>
            </template>
            <template v-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">{{ getPriorityText(record.priority) }}</a-tag>
            </template>
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <FaFlowCube ref="flowRef" flow-en-code="vehicleApplication" @reload="reload" />
    <ImportModal @register="registerImportModal" @success="reload" />
    <ExportModal @register="registerExportModal" />

  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table';
  import { vehicleApplicationApi as api } from "/@/api";
  import { genFlowDetailBtn, genFlowEditBtn } from "/@/utils/tableUtils";
  import FaFlowCube from '/@/components/Fa/common/src/FaFlowCube.vue';
  import FaFlowStatus from '/@/components/Fa/common/src/FaFlowStatus.vue';
  import ImportModal from './ImportModal.vue';
  import ExportModal from './ExportModal.vue';

  import { useModal } from '/@/components/Modal';
  import { FLOW_OP_TYPE } from '/@/enums/zzEnums';
  import { formatToDate } from '/@/utils/dateUtil';
  import {
    columns,
    searchFormSchema,
    getPriorityColor,
    getPriorityText
  } from './helper';

  defineOptions({ name: 'vehicle-vehicleApplication' });

  const flowRef = ref<any>();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerExportModal, { openModal: openExportModal }] = useModal();

  const [registerTable, { reload, getForm }] = useTable({
    api: api.page,
    columns,
    useSearchForm: true,
    ellipsis: false,
    formConfig: {
      schemas: searchFormSchema,
    },
    searchInfo: {
      _sorter: 'f_creator_time DESC',
    },
    actionColumn: { width: 180, title: '操作', dataIndex: 'action' },
  });

  function getTableActions(record): ActionItem[] {
    return [
      genFlowEditBtn(record, toDetail),
      genFlowDetailBtn(record, toDetail),
    ];
  }

  function handleAdd() {
    flowRef.value.handleAdd();
  }

  function toDetail(record: any, opType: FLOW_OP_TYPE) {
    flowRef.value.toDetail(record, opType);
  }

  function handleImport() {
    openImportModal(true);
  }

  async function handleExport() {
    const listQuery = {
      ...getForm().getFieldsValue(),
    };
    openExportModal(true, { listQuery });
  }
</script>

<style scoped lang="less">
</style>
