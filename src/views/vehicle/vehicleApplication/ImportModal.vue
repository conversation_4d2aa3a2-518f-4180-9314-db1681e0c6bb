<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <div class="import-container">
      <a-steps :current="currentStep" size="small">
        <a-step title="上传文件" />
        <a-step title="数据预览" />
        <a-step title="导入完成" />
      </a-steps>

      <div class="step-content">
        <!-- 步骤1: 上传文件 -->
        <div v-if="currentStep === 0" class="upload-step">
          <div class="template-download">
            <a-button type="link" @click="downloadTemplate">
              <i class="icon-ym icon-ym-btn-download" />
              下载导入模板
            </a-button>
          </div>
          
          <a-upload-dragger
            v-model:fileList="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            accept=".xlsx,.xls"
            :multiple="false"
          >
            <p class="ant-upload-drag-icon">
              <i class="icon-ym icon-ym-btn-upload" style="font-size: 48px; color: #1890ff;" />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">支持扩展名：.xlsx .xls</p>
          </a-upload-dragger>
        </div>

        <!-- 步骤2: 数据预览 -->
        <div v-if="currentStep === 1" class="preview-step">
          <div class="preview-info">
            <a-alert
              :message="`共解析到 ${previewData.length} 条数据，其中 ${validCount} 条有效，${errorCount} 条异常`"
              type="info"
              show-icon
            />
          </div>
          
          <BasicTable
            :columns="previewColumns"
            :dataSource="previewData"
            :pagination="{ pageSize: 10 }"
            size="small"
          />
        </div>

        <!-- 步骤3: 导入完成 -->
        <div v-if="currentStep === 2" class="result-step">
          <a-result
            :status="importResult.success ? 'success' : 'warning'"
            :title="importResult.title"
            :sub-title="importResult.message"
          />
        </div>
      </div>

      <div class="step-actions">
        <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
        <a-button
          v-if="currentStep < 2"
          type="primary"
          :disabled="!canNext"
          @click="nextStep"
        >
          {{ currentStep === 1 ? '开始导入' : '下一步' }}
        </a-button>
        <a-button v-if="currentStep === 2" type="primary" @click="closeModal">完成</a-button>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, computed, reactive, h } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { vehicleApplicationApi } from '/@/api';

  defineOptions({ name: 'vehicle-application-import-modal' });

  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();

  const currentStep = ref(0);
  const fileList = ref<any[]>([]);
  const previewData = ref<any[]>([]);
  const importResult = reactive({
    success: false,
    title: '',
    message: '',
  });

  const getTitle = computed(() => '导入用车申请数据');

  const canNext = computed(() => {
    if (currentStep.value === 0) return fileList.value.length > 0;
    if (currentStep.value === 1) return previewData.value.length > 0;
    return false;
  });

  const validCount = computed(() => 
    previewData.value.filter(item => !item.errorMsg).length
  );

  const errorCount = computed(() => 
    previewData.value.filter(item => item.errorMsg).length
  );

  const previewColumns = [
    { title: '申请单号', dataIndex: 'applicationNo', width: 150 },
    { title: '申请人', dataIndex: 'applicantName', width: 100 },
    { title: '申请部门', dataIndex: 'applyDepartment', width: 120 },
    { title: '用车日期', dataIndex: 'useDate', width: 120 },
    { title: '用车目的', dataIndex: 'usePurpose', width: 200 },
    { title: '优先级', dataIndex: 'priority', width: 80 },
    {
      title: '状态',
      dataIndex: 'errorMsg',
      width: 100,
      customRender: ({ record }) => {
        if (record.errorMsg) {
          return h('a-tag', { color: 'error' }, '异常');
        } else {
          return h('a-tag', { color: 'success' }, '正常');
        }
      },
    },
    {
      title: '异常信息',
      dataIndex: 'errorMsg',
      width: 200,
      customRender: ({ text }) => text || '-',
    },
  ];

  const [registerModal, { closeModal }] = useModalInner(init);

  function init() {
    currentStep.value = 0;
    fileList.value = [];
    previewData.value = [];
    importResult.success = false;
    importResult.title = '';
    importResult.message = '';
  }

  function beforeUpload(file: File) {
    fileList.value = [file];
    return false; // 阻止自动上传
  }

  function handleRemove() {
    fileList.value = [];
  }

  async function downloadTemplate() {
    try {
      await vehicleApplicationApi.getImportTemplate();
      createMessage.success('模板下载成功');
    } catch (error) {
      createMessage.error('模板下载失败');
    }
  }

  async function nextStep() {
    if (currentStep.value === 0) {
      // 上传文件并预览
      await uploadAndPreview();
    } else if (currentStep.value === 1) {
      // 执行导入
      await executeImport();
    }
    currentStep.value++;
  }

  function prevStep() {
    currentStep.value--;
  }

  async function uploadAndPreview() {
    try {
      const formData = new FormData();
      formData.append('file', fileList.value[0]);
      
      const result = await vehicleApplicationApi.validateImportData(formData);
      previewData.value = result.data || [];
    } catch (error) {
      createMessage.error('文件解析失败');
      throw error;
    }
  }

  async function executeImport() {
    try {
      const formData = new FormData();
      formData.append('file', fileList.value[0]);
      
      const result = await vehicleApplicationApi.importData(formData);
      
      importResult.success = true;
      importResult.title = '导入成功';
      importResult.message = `成功导入 ${result.data.successCount} 条数据`;
      
      emit('success');
    } catch (error) {
      importResult.success = false;
      importResult.title = '导入失败';
      importResult.message = '导入过程中发生错误，请检查数据格式';
    }
  }

  async function handleSubmit() {
    // 这个方法由模态框的确定按钮触发，但我们使用自定义按钮
  }
</script>

<style scoped lang="less">
.import-container {
  .step-content {
    margin: 24px 0;
    min-height: 300px;
  }

  .upload-step {
    .template-download {
      margin-bottom: 16px;
      text-align: center;
    }
  }

  .preview-step {
    .preview-info {
      margin-bottom: 16px;
    }
  }

  .step-actions {
    text-align: right;
    border-top: 1px solid #e8e8e8;
    padding-top: 16px;
    
    .ant-btn {
      margin-left: 8px;
    }
  }
}
</style>
