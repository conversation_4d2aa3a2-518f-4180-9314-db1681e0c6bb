# 用车申请管理

## 功能概述

用车申请管理模块提供了完整的车辆申请流程管理功能，包括申请提交、审核、车辆分配、使用记录等全流程管理。

## 主要功能

### 1. 申请管理
- **新增申请**: 通过流程引擎创建新的用车申请
- **申请列表**: 查看所有用车申请记录
- **申请详情**: 查看申请的详细信息
- **申请编辑**: 对未提交或退回的申请进行编辑

### 2. 流程管理
- **流程状态**: 实时显示申请的审批状态
- **流程节点**: 显示当前所在的审批节点
- **流程操作**: 支持提交、审核、退回等流程操作

### 3. 数据管理
- **数据导入**: 支持Excel格式的批量数据导入
- **数据导出**: 支持多种格式的数据导出
- **数据筛选**: 提供多维度的数据筛选功能

## 页面结构

```
src/views/vehicle/vehicleApplication/
├── index.vue           # 主页面 - 申请列表
├── Detail.vue          # 详情页面（备用，现使用流程引擎详情）
├── ImportModal.vue     # 导入弹窗
├── ExportModal.vue     # 导出弹窗
├── helper/
│   └── index.ts        # 工具函数和配置
└── README.md           # 说明文档

src/views/workFlow/workFlowForm/vehicleApplication/
└── index.vue           # 流程表单 - 编辑/详情页面
```

## 组件说明

### index.vue (主页面)
- 使用 `BasicTable` 组件展示申请列表
- 集成 `FaFlowCube` 组件处理流程操作
- 使用 `FaFlowStatus` 组件显示流程状态
- 使用 `genFlowEditBtn` 和 `genFlowDetailBtn` 生成标准流程按钮
- 提供搜索、导入、导出等功能

### workFlow/workFlowForm/vehicleApplication/index.vue (流程表单)
- **主要用途**: 流程引擎自动加载的编辑/详情表单
- **位置**: 必须放在 `workFlow/workFlowForm/` 目录下，以便流程引擎自动发现
- 使用 `useFlowForm` hook 处理流程表单逻辑
- 支持权限控制：`judgeShow` 控制显示，`judgeWrite` 控制编辑
- 自动生成申请单号（格式：YC + YYYYMMDD + 流水号）
- 根据流程节点动态显示不同字段
- 支持车辆选择和可用车辆查询

### Detail.vue (详情页面)
- **注意**: 当前版本使用流程引擎的 Form.vue 作为详情页面
- 此文件作为备用，可用于非流程数据的详情展示
- 使用 `a-descriptions` 组件展示申请详情
- 分区域显示基本信息、审核信息、使用记录等

### ImportModal.vue (导入弹窗)
- 三步式导入流程：上传文件 → 数据预览 → 导入完成
- 支持数据验证和错误提示
- 提供导入模板下载功能

### ExportModal.vue (导出弹窗)
- 支持多种导出类型：当前页、全部数据、查询结果
- 支持多种文件格式：Excel、CSV
- 可配置导出字段

## 流程引擎集成

### 流程编码
- 流程编码：`vehicleApplication`
- 需要在后台流程引擎中配置对应的流程模板
- 表单文件：`workFlow/workFlowForm/vehicleApplication/index.vue` 会被流程引擎自动加载

### 流程状态
- `0`: 草稿/等待提交
- `1`: 等待审核
- `2`: 审核通过
- `3`: 审核退回
- `4`: 流程撤回
- `5`: 审核终止
- `6`: 已被挂起

### 流程字段
- `flowId`: 流程实例ID
- `currentState`: 当前流程状态
- `currentNodeName`: 当前节点名称

### 表单权限控制
- `judgeShow(fieldName)`: 控制字段是否显示
- `judgeWrite(fieldName)`: 控制字段是否可编辑
- `config.disabled`: 控制整个表单是否禁用（详情模式）

### 申请单号生成规则
- 格式：`YC + YYYYMMDD + 流水号`
- 示例：`YC202412251234`
- 自动生成，不可手动修改

## 数据字段说明

### 基本字段
- `applicationNo`: 申请单号（系统自动生成）
- `applicantId/applicantName`: 申请人ID/姓名
- `applyDepartment`: 申请部门
- `useDate`: 用车日期
- `estimatedDuration`: 预计用车时长（小时）
- `usePurpose`: 用车目的
- `priority`: 优先级（1-一般，2-紧急）

### 审核字段
- `teamLeaderOpinion`: 队长审核意见
- `teamLeaderReviewTime`: 队长审核时间
- `engineeringDeptOpinion`: 工程部审核意见
- `engineeringDeptReviewTime`: 工程部审核时间

### 车辆分配字段
- `assignedVehicleId`: 分配车辆ID
- `assignedVehicleNumber`: 分配车辆车牌号

### 使用记录字段
- `actualStartTime`: 实际使用开始时间
- `actualEndTime`: 实际使用结束时间
- `actualDuration`: 实际使用时长（台班）

## API接口

### 基础接口
- `GET /api/base/vehicle/application` - 获取申请列表
- `GET /api/base/vehicle/application/{id}` - 获取申请详情
- `POST /api/base/vehicle/application` - 创建申请
- `PUT /api/base/vehicle/application/{id}` - 更新申请
- `DELETE /api/base/vehicle/application/{id}` - 删除申请

### 扩展接口
- `POST /api/base/vehicle/application/importData` - 导入数据
- `POST /api/base/vehicle/application/exportData` - 导出数据
- `GET /api/base/vehicle/application/getImportTemplate` - 获取导入模板
- `GET /api/base/vehicle/application/getAvailableVehicles` - 获取可用车辆

## 使用说明

### 1. 新增申请
1. 点击"新增申请"按钮
2. 在流程表单中填写申请信息
3. 提交申请进入审批流程

### 2. 查看申请
1. 在列表中点击"详情"按钮
2. 查看申请的详细信息和审批记录

### 3. 编辑申请
1. 对于草稿状态或退回的申请，点击"编辑"按钮
2. 在流程表单中修改申请信息
3. 重新提交申请

### 4. 数据导入
1. 点击"导入"按钮
2. 下载导入模板并填写数据
3. 上传文件并预览数据
4. 确认无误后执行导入

### 5. 数据导出
1. 点击"导出"按钮
2. 选择导出类型和文件格式
3. 确认导出

## 注意事项

1. **流程配置**: 使用前需要在后台配置对应的流程模板
2. **权限控制**: 不同角色用户看到的操作按钮可能不同
3. **数据验证**: 导入数据时会进行格式和业务规则验证
4. **状态流转**: 申请状态按照预定义的流程进行流转
5. **后端依赖**: 前端页面依赖后端实体类继承SuperFlowEntity

## 后续优化

1. 添加车辆选择器组件
2. 增加申请统计图表
3. 支持申请模板功能
4. 添加消息通知功能
5. 优化移动端适配
