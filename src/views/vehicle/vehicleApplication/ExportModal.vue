<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { vehicleApplicationApi } from '/@/api';

  defineOptions({ name: 'vehicle-application-export-modal' });

  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();
  const listQuery = ref<any>({});

  const getTitle = computed(() => '导出用车申请数据');

  const [registerForm, { setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: [
      {
        field: 'exportType',
        label: '导出类型',
        component: 'RadioGroup',
        defaultValue: 'current',
        componentProps: {
          options: [
            { label: '当前页数据', value: 'current' },
            { label: '全部数据', value: 'all' },
            { label: '查询结果', value: 'query' },
          ],
        },
        required: true,
      },
      {
        field: 'fileFormat',
        label: '文件格式',
        component: 'RadioGroup',
        defaultValue: 'xlsx',
        componentProps: {
          options: [
            { label: 'Excel格式(.xlsx)', value: 'xlsx' },
            { label: 'CSV格式(.csv)', value: 'csv' },
          ],
        },
        required: true,
      },
    ],
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(init);

  function init(data: any) {
    listQuery.value = data.listQuery || {};
    setFieldsValue({
      exportType: 'current',
      fileFormat: 'xlsx',
    });
  }

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      const params = {
        ...values,
        ...listQuery.value,
      };

      await vehicleApplicationApi.exportData(params);
      createMessage.success('导出成功');
      closeModal();
      emit('success');
    } catch (error) {
      console.error('导出失败:', error);
      createMessage.error('导出失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
