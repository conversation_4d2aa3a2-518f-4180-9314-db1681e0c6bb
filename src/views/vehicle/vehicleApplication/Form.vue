<template>
  <div class="flow-form">
    <div class="flow-com-title">
      <h1>用车申请</h1>
    </div>
    <a-form :colon="false" :labelCol="{ style: { width: '120px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>
        <!-- 申请单号 -->
        <a-col :span="12" v-if="judgeShow('applicationNo')">
          <a-form-item label="申请单号" name="applicationNo">
            <a-input v-model:value="dataForm.applicationNo" placeholder="申请单号" disabled />
          </a-form-item>
        </a-col>

        <!-- 申请人 -->
        <a-col :span="12" v-if="judgeShow('applicantId')">
          <a-form-item label="申请人" name="applicantId">
            <jnpf-user-select v-model:value="dataForm.applicantId" placeholder="申请人" :disabled="judgeWrite('applicantId')" />
          </a-form-item>
        </a-col>

        <!-- 申请部门 -->
        <a-col :span="12" v-if="judgeShow('applyDepartment')">
          <a-form-item label="申请部门" name="applyDepartment">
            <jnpf-dep-select v-model:value="dataForm.applyDepartment" placeholder="申请部门" :disabled="judgeWrite('applyDepartment')" />
          </a-form-item>
        </a-col>

        <!-- 用车日期 -->
        <a-col :span="12" v-if="judgeShow('useDate')">
          <a-form-item label="用车日期" name="useDate">
            <jnpf-date-picker v-model:value="dataForm.useDate" placeholder="用车日期" :disabled="judgeWrite('useDate')" />
          </a-form-item>
        </a-col>

        <!-- 预计用车时长 -->
        <a-col :span="12" v-if="judgeShow('estimatedDuration')">
          <a-form-item label="预计用车时长" name="estimatedDuration">
            <a-input-number v-model:value="dataForm.estimatedDuration" placeholder="预计用车时长（小时）" :min="0" :max="24" :precision="1" :disabled="judgeWrite('estimatedDuration')" style="width: 100%" />
          </a-form-item>
        </a-col>

        <!-- 优先级 -->
        <a-col :span="12" v-if="judgeShow('priority')">
          <a-form-item label="优先级" name="priority">
            <jnpf-select v-model:value="dataForm.priority" :options="priorityOptions" placeholder="优先级" :disabled="judgeWrite('priority')" />
          </a-form-item>
        </a-col>

        <!-- 用车目的 -->
        <a-col :span="24" v-if="judgeShow('usePurpose')">
          <a-form-item label="用车目的" name="usePurpose">
            <a-textarea v-model:value="dataForm.usePurpose" placeholder="请详细描述用车目的" :rows="3" :maxlength="500" :disabled="judgeWrite('usePurpose')" />
          </a-form-item>
        </a-col>

        <!-- 分配车辆 -->
        <a-col :span="12" v-if="judgeShow('assignedVehicleId')">
          <a-form-item label="分配车辆" name="assignedVehicleId">
            <jnpf-select v-model:value="dataForm.assignedVehicleId" :options="vehicleOptions" placeholder="分配车辆" :disabled="judgeWrite('assignedVehicleId')" />
          </a-form-item>
        </a-col>

        <!-- 队长审核意见 -->
        <a-col :span="24" v-if="judgeShow('teamLeaderOpinion')">
          <a-form-item label="队长审核意见" name="teamLeaderOpinion">
            <a-textarea v-model:value="dataForm.teamLeaderOpinion" placeholder="请填写审核意见" :rows="2" :disabled="judgeWrite('teamLeaderOpinion')" />
          </a-form-item>
        </a-col>

        <!-- 工程部审核意见 -->
        <a-col :span="24" v-if="judgeShow('engineeringDeptOpinion')">
          <a-form-item label="工程部审核意见" name="engineeringDeptOpinion">
            <a-textarea v-model:value="dataForm.engineeringDeptOpinion" placeholder="请填写审核意见" :rows="2" :disabled="judgeWrite('engineeringDeptOpinion')" />
          </a-form-item>
        </a-col>

        <!-- 实际开始时间 -->
        <a-col :span="12" v-if="judgeShow('actualStartTime')">
          <a-form-item label="实际开始时间" name="actualStartTime">
            <jnpf-date-picker v-model:value="dataForm.actualStartTime" placeholder="实际开始时间" format="YYYY-MM-DD HH:mm" :disabled="judgeWrite('actualStartTime')" />
          </a-form-item>
        </a-col>

        <!-- 实际结束时间 -->
        <a-col :span="12" v-if="judgeShow('actualEndTime')">
          <a-form-item label="实际结束时间" name="actualEndTime">
            <jnpf-date-picker v-model:value="dataForm.actualEndTime" placeholder="实际结束时间" format="YYYY-MM-DD HH:mm" :disabled="judgeWrite('actualEndTime')" />
          </a-form-item>
        </a-col>

        <!-- 实际使用时长 -->
        <a-col :span="12" v-if="judgeShow('actualDuration')">
          <a-form-item label="实际使用时长" name="actualDuration">
            <a-input-number v-model:value="dataForm.actualDuration" placeholder="实际使用时长（小时）" :min="0" :precision="1" :disabled="judgeWrite('actualDuration')" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { vehicleApplicationApi, vehicleInfoApi } from '/@/api';
  import { isNil } from 'lodash-es';
  import { Rule } from '/@/components/Form';
  import { useUserStore } from '/@/store/modules/user';
  import { formatToDate } from '/@/utils/dateUtil';

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  interface State {
    dataForm: any;
    dataRule: Record<string, Rule[]>;
  }

  defineOptions({ name: 'vehicleApplication' });
  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);
  const formRef = ref<FormInstance>();

  const state = reactive<State>({
    dataForm: {
      flowId: '',
      id: '',
      applicationNo: '',
      applicantId: '',
      applicantName: '',
      applyDepartment: '',
      useDate: null,
      estimatedDuration: null,
      usePurpose: '',
      priority: '1', // 默认一般
      assignedVehicleId: '',
      teamLeaderOpinion: '',
      engineeringDeptOpinion: '',
      actualStartTime: null,
      actualEndTime: null,
      actualDuration: null,
    },
    dataRule: {
      applicantId: [{ required: true, message: '申请人不能为空', trigger: 'change' }],
      applyDepartment: [{ required: true, message: '申请部门不能为空', trigger: 'change' }],
      useDate: [{ required: true, message: '用车日期不能为空', trigger: 'change' }],
      estimatedDuration: [{ required: true, message: '预计用车时长不能为空', trigger: 'blur' }],
      usePurpose: [
        { required: true, message: '用车目的不能为空', trigger: 'blur' },
        { max: 500, message: '用车目的不能超过500个字符', trigger: 'blur' }
      ],
      priority: [{ required: true, message: '优先级不能为空', trigger: 'change' }],
    },
  });

  const { dataForm, dataRule } = toRefs(state);
  const { init, judgeShow, judgeWrite, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
    selfInit,
    selfGetInfo,
    beforeSubmit
  });

  // 优先级选项
  const priorityOptions = ref([
    { id: '1', fullName: '一般' },
    { id: '2', fullName: '紧急' },
  ]);

  // 车辆选项
  const vehicleOptions = ref([]);

  async function beforeSubmit() {
    // 在提交前生成申请单号
    if (!state.dataForm.applicationNo) {
      await generateApplicationNo();
    }
    return state.dataForm;
  }

  /** 表单初始化信息 */
  function selfInit() {
    console.log("selfInit");
    // 设置默认值
    state.dataForm.applicantId = userInfo.userId;
    state.dataForm.applicantName = userInfo.userName;
    state.dataForm.applyDepartment = userInfo.departmentId;
    state.dataForm.flowTitle = userInfo.userName + '用车申请';
    
    // 生成申请单号
    generateApplicationNo();
    
    // 加载车辆选项
    loadVehicleOptions();
  }

  /** 表单获取信息 */
  function selfGetInfo() {
    console.log("selfGetInfo", state.dataForm.id);
    if (state.dataForm.id) {
      // 如果是编辑模式，加载车辆选项
      loadVehicleOptions();
    }
  }

  /** 生成申请单号 */
  async function generateApplicationNo() {
    if (state.dataForm.id) {
      return; // 编辑模式不重新生成
    }
    
    const now = new Date();
    const dateStr = formatToDate(now, 'YYYYMMDD');
    const timeStr = now.getTime().toString().slice(-4); // 取时间戳后4位
    state.dataForm.applicationNo = `YC${dateStr}${timeStr}`;
  }

  /** 加载车辆选项 */
  async function loadVehicleOptions() {
    try {
      if (state.dataForm.useDate) {
        const res = await vehicleApplicationApi.getAvailableVehicles(state.dataForm.useDate);
        vehicleOptions.value = res.data.map(item => ({
          id: item.id,
          fullName: `${item.vehicleNumber} - ${item.vehicleType}`
        }));
      }
    } catch (error) {
      console.error('加载车辆选项失败:', error);
    }
  }

  // 监听用车日期变化，重新加载可用车辆
  watch(
    () => state.dataForm.useDate,
    (newDate) => {
      if (newDate) {
        loadVehicleOptions();
      } else {
        vehicleOptions.value = [];
      }
    }
  );

  onMounted(() => {
    init();
  });

  defineExpose({ dataFormSubmit });
</script>

<style scoped>
.flow-form {
  padding: 20px;
}

.flow-com-title {
  text-align: center;
  margin-bottom: 20px;
}

.flow-com-title h1 {
  color: #333;
  font-size: 24px;
  font-weight: bold;
}
</style>
