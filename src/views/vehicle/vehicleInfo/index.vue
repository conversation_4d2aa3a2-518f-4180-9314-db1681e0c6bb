<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable" :searchInfo="searchInfo">
          <template #tableTitle>
            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新增车辆</a-button>
            <a-button @click="handleExport" class="ml-2" preIcon="icon-ym icon-ym-btn-export"> 导出 </a-button>
            <a-button @click="handleImport" class="ml-2" preIcon="icon-ym icon-ym-btn-import"> 导入 </a-button>
            <a-button v-if="selectedRowKeys.length > 0" @click="handleBatchDelete()" type="primary" danger preIcon="icon-ym icon-ym-btn-delete" class="ml-2">
              批量删除
            </a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'tonnage'">
              {{ record.tonnage ? `${record.tonnage}吨` : '-' }}
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <ExportModal @register="registerExportModal" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ActionItem, BasicTable, useTable, TableAction } from '/@/components/Table';
import { usePopup } from '/@/components/Popup';
import { useModal } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  genQueryInput,
  genQuerySelect,
  genQuerySearch,
  genEditBtn,
  genDeleteBtn
} from '/@/utils/tableUtils';
import { vehicleInfoApi } from '/@/api';
import {
  VEHICLE_PROCESS_STATE_OPTIONS,
  getVehicleProcessStateColor,
  getVehicleProcessStateText
} from '/@/enums/zzEnums';
import Form from './Form.vue';
import ImportModal from './ImportModal.vue';
import ExportModal from './ExportModal.vue';

defineOptions({ name: 'vehicle-vehicleInfo' });

const { createMessage } = useMessage();
const selectedRowKeys = ref<string[]>([]);
const searchInfo = reactive({});

// 状态选项 - 直接使用枚举数据（已经是正确的格式：fullName和id）
const statusOptions = VEHICLE_PROCESS_STATE_OPTIONS;

// 表格列配置
const columns = [
  { title: '车牌号', dataIndex: 'vehicleNumber', width: 120, fixed: 'left' },
  { title: '车辆型号', dataIndex: 'vehicleModel', width: 150 },
  { title: '使用单位', dataIndex: 'vehicleUseUnit', width: 200 },
  { title: '吨位', dataIndex: 'tonnage', width: 100, align: 'right' },
  { title: '状态', dataIndex: 'status', width: 100 },
  { title: '备注', dataIndex: 'remark', width: 200, ellipsis: true },
  { title: '创建时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm' },
];

// 表格配置
const [registerTable, { reload, getForm }] = useTable({
  api: vehicleInfoApi.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('车牌号', 'vehicleNumber'),
      genQueryInput('车辆型号', 'vehicleModel'),
      genQueryInput('使用单位', 'vehicleUseUnit'),
      genQuerySelect('状态', 'status', statusOptions),
    ],
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
  rowSelection: {
    onChange: (selectedKeys: string[]) => {
      selectedRowKeys.value = selectedKeys;
    },
  },
  searchInfo: {
    _sorter: 'f_creator_time DESC',
  },
});

// 弹窗注册
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerImportModal, { openModal: openImportModal }] = useModal();
const [registerExportModal, { openModal: openExportModal }] = useModal();



// 状态相关方法
function getStatusColor(status: string | number) {
  return getVehicleProcessStateColor(status);
}

function getStatusText(status: string | number) {
  return getVehicleProcessStateText(status);
}

// 表格操作按钮
function getTableActions(record: any): ActionItem[] {
  return [
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
  ];
}

// 操作方法
function addOrUpdateHandle(id = '') {
  openFormPopup(true, { id });
}

function handleDelete(id: string) {
  vehicleInfoApi.remove(id).then(res => {
    createMessage.success(res.msg || '删除成功');
    reload();
  });
}

function handleBatchDelete() {
  if (!selectedRowKeys.value.length) {
    createMessage.warning('请选择要删除的数据');
    return;
  }

  vehicleInfoApi.removeByIds(selectedRowKeys.value).then(res => {
    createMessage.success(res.msg || '批量删除成功');
    selectedRowKeys.value = [];
    reload();
  });
}

function handleImport() {
  openImportModal(true);
}

// 处理导出功能
function handleExport() {
  const listQuery = {
    ...getForm().getFieldsValue(),
  };
  openExportModal(true, { listQuery });
}


</script>

<style scoped lang="less">
  .ml-2 {
    margin-left: 8px;
  }

  .mr-1 {
    margin-right: 4px;
  }

  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }
</style>
