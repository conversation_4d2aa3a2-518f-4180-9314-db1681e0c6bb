<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="800"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <FaImportHeaderStep v-model:current="activeStep"/>

    <div class="import-main" v-show="activeStep == 0">
      <FaUploadCard v-model:value="fileId"/>
      <FaDownloadCard download-url="/file/车辆信息导入模板.xlsx"/>

      <a-alert class="mt-4">
        <template #message>
          <ol>
            <li>1. 导入从第4行开始，请注意；</li>
            <li>2. 所有标*的字段为必填项；</li>
            <li>3. 状态字段请填写：编制、待审核、退回、已接受、已确认 或对应数字 0、1、2、3、4；</li>
            <li>4. 购置日期格式：YYYY-MM-DD。</li>
          </ol>
        </template>
      </a-alert>
    </div>

    <div class="import-main" v-show="activeStep == 1">
      <div class="preview-header" style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
        <div>
          <a-alert
            v-if="hasErrors"
            message="数据验证失败，请修正错误后重新检验"
            type="error"
            show-icon
            style="margin: 0;"
          />
          <a-alert
            v-else
            message="数据验证通过，可以进行导入"
            type="success"
            show-icon
            style="margin: 0;"
          />
        </div>
        <a-button
          type="primary"
          @click="handleRevalidate"
          :loading="revalidateLoading"
          size="small"
        >
          重新检验
        </a-button>
      </div>
      <a-table :data-source="list" :columns="columns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '440px' }" class="import-preview-table">
        <template #bodyCell="{ column, record, index }">
          <template v-for="item in tableData">
            <template v-if="column.key === item.dataIndex && item.dataIndex !== 'errorsInfo'">
              <a-input v-model:value="record[column.key]" style="background: #FFF" />
            </template>
          </template>
          <template v-if="column.key === 'errorsInfo'">
            <a-tooltip v-if="record.errorsInfo" :title="record.errorsInfo" placement="topLeft">
              <span style="color: #ff4d4f; cursor: help;">
                {{ record.errorsInfo.length > 20 ? record.errorsInfo.substring(0, 20) + '...' : record.errorsInfo }}
              </span>
            </a-tooltip>
            <span v-else style="color: #52c41a;">正常</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-button class="action-btn" type="link" color="error" @click="handleDelItem(index)" size="small">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>

    <div class="import-main" v-show="activeStep == 2">
      <FaImportSuccessCard v-if="!result.resultType" :success-num="result.snum"/>
      <FaImportFailCard v-if="result.resultType" :success-num="result.snum" :fail-num="result.fnum">
        <a-table :data-source="resultList" :columns="resultColumns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '205px' }"/>
      </FaImportFailCard>
    </div>

    <template #insertFooter>
      <a-button @click="handleClose()" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1">{{ t('common.prev') }}</a-button>
      <a-button
        type="primary"
        @click="handleNext"
        :loading="btnLoading"
        v-if="activeStep < 2"
        :disabled="(activeStep === 0 && !fileId) || (activeStep === 1 && hasErrors)"
      >
        {{ t('common.next') }}
      </a-button>
      <a-button type="primary" @click="handleClose(true)" v-else>关闭</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { vehicleInfoApi } from '/@/api';
import FaDownloadCard from "/@/components/Fa/Import/src/FaDownloadCard.vue";
import FaUploadCard from "/@/components/Fa/Import/src/FaUploadCard.vue";
import FaImportHeaderStep from "/@/components/Fa/Import/src/FaImportHeaderStep.vue";
import FaImportSuccessCard from "/@/components/Fa/Import/src/FaImportSuccessCard.vue";
import FaImportFailCard from "/@/components/Fa/Import/src/FaImportFailCard.vue";

interface State {
  activeStep: number;
  fileId?: string;
  btnLoading: boolean;
  revalidateLoading: boolean;
  list: any[];
  result: any;
  resultList: any[];
}

const emit = defineEmits(['register', 'reload']);
const [registerModal, { closeModal, getVisible }] = useModalInner(init);
const { createMessage } = useMessage();
const { t } = useI18n();

// 定义表格字段
const tableData = [
  { title: '车牌号*', dataIndex: 'vehicleNumber', key: 'vehicleNumber' },
  { title: '车辆型号*', dataIndex: 'vehicleModel', key: 'vehicleModel' },
  { title: '使用单位*', dataIndex: 'vehicleUseUnit', key: 'vehicleUseUnit' },
  { title: '状态*', dataIndex: 'statusName', key: 'statusName' },
  { title: '吨位', dataIndex: 'tonnage', key: 'tonnage' },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
  {
    title: '错误信息',
    dataIndex: 'errorsInfo',
    width: 200,
    customHeaderCell: () => ({
      style: { color: '#ff4d4f !important', fontWeight: 'bold' }
    }),
    customCell: () => ({
      style: { color: '#ff4d4f' }
    })
  }
];

// 表格列定义
const columns: any[] = [
  ...tableData,
  { title: '操作', dataIndex: 'action', key: 'action', width: 50, fixed: 'right' },
];

const resultColumns: any[] = [
  ...tableData
];

const state = reactive<State>({
  activeStep: 0,
  fileId: undefined,
  btnLoading: false,
  revalidateLoading: false,
  list: [],
  result: {},
  resultList: [],
});

const { activeStep, fileId, btnLoading, revalidateLoading, list, result, resultList } = toRefs(state);

// 检查是否存在错误信息
const hasErrors = computed(() => {
  return state.list.some(item => item.errorsInfo && item.errorsInfo.trim() !== '');
});

// 添加一个监听，确保每次模态框打开时activeStep都是0
watch(getVisible, (visible) => {
  if (visible) {
    state.activeStep = 0;
    state.fileId = undefined;
    state.btnLoading = false;
    state.revalidateLoading = false;
    state.list = [];
    state.result = {};
    state.resultList = [];
  }
}, { immediate: true });

function init() {
  state.activeStep = 0;
  state.fileId = undefined;
  state.btnLoading = false;
  state.revalidateLoading = false;
  state.list = [];
  state.result = {};
  state.resultList = [];
}

// 上一步
function handlePrev() {
  if (state.activeStep == 0) return;
  state.activeStep -= 1;
}

// 下一步
function handleNext() {
  if (state.activeStep == 0) {
    if (!state.fileId) return createMessage.warning('请先上传文件');
    state.btnLoading = true;
    vehicleInfoApi.importPreview({
      fileId: state.fileId
    })
      .then(res => {
        state.list = res.data || [];
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
    return;
  }
  if (state.activeStep == 1) {
    if (!state.list.length) return createMessage.warning('导入数据为空');

    // 检查是否存在错误信息
    if (hasErrors.value) {
      return createMessage.warning('存在数据验证错误，请修正后再导入');
    }

    state.btnLoading = true;
    vehicleInfoApi.importData({ list: state.list })
      .then(res => {
        state.result = res.data;
        state.resultList = res.data.failResult || [];
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
  }
}

// 重新检验数据
async function handleRevalidate() {
  if (!state.list.length) {
    createMessage.warning('没有数据需要检验');
    return;
  }

  try {
    state.revalidateLoading = true;

    // 调用重新校验接口，传入当前编辑后的数据
    const res = await vehicleInfoApi.validateImportData({
      list: state.list
    });

    // 更新验证结果
    if (res.data && Array.isArray(res.data)) {
      state.list = res.data;
      createMessage.success('数据重新检验完成');
    } else {
      state.list = res.data || [];
      createMessage.success('数据重新检验完成');
    }
  } catch (error) {
    createMessage.error('数据检验失败，请稍后重试');
    console.error('重新检验失败:', error);
  } finally {
    state.revalidateLoading = false;
  }
}

// 删除一条记录
function handleDelItem(index) {
  state.list.splice(index, 1);
}

// 关闭并可选刷新
function handleClose(reload = false) {
  // 确保关闭前重置状态
  state.activeStep = 0;
  closeModal();
  if (reload) emit('reload');
}
</script>

<style scoped>
.mt-4 {
  margin-top: 16px;
}
</style>
