<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="导出数据" @ok="handleSubmit" destroyOnClose class="export-modal">
    <template #insertFooter>
      <div class="footer-tip">提示:系统将导出列表中选中的数据</div>
    </template>
    <a-form :colon="false" labelAlign="left" :labelCol="{ style: { width: '90px' } }">
      <a-form-item>
        <a-radio-group v-model:value="dataType">
          <a-radio :value="0">当前查询数据</a-radio>
          <a-radio :value="1">系统全部数据</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { vehicleInfoApi } from '/@/api';

const [registerModal, { closeModal, changeOkLoading }] = useModalInner(init);
const dataType = ref(0);
const listQuery = ref({});

function init(data) {
  dataType.value = 0;
  listQuery.value = data.listQuery;
}

function handleSubmit() {
  changeOkLoading(true);
  let query = {
    ...listQuery.value,
    _dataType: dataType.value,
  };
  vehicleInfoApi.exportExcel(query)
    .then(() => {
      changeOkLoading(false);
      closeModal();
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>


