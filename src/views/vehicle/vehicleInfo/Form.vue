<template>
  <BasicPopup
    v-bind="$attrs"
    @register="registerPopup"
    :title="getTitle"
    :width="800"
    :showOkBtn="true"
    @ok="handleSubmit"
    destroyOnClose
  >
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>

<script setup lang="ts">
import { ref, computed, unref } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { genInput, genSelect, genInputNumber } from '/@/utils/formUtils';
import formValidate from '/@/utils/formValidate';
import { vehicleInfoApi } from '/@/api';
import { VEHICLE_PROCESS_STATE_OPTIONS } from '/@/enums/zzEnums';

defineOptions({ name: 'vehicle-vehicleInfo-form' });

const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const id = ref('');

const getTitle = computed(() => (!unref(id) ? '新增车辆信息' : '编辑车辆信息'));

// 状态选项 - 直接使用枚举数据（已经是正确的格式：fullName和id）
const statusOptions = VEHICLE_PROCESS_STATE_OPTIONS;

// 表单配置
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  schemas: [
    {
      ...genInput('车牌号', 'vehicleNumber', true),
      rules: [
        { required: true, message: '车牌号不能为空', trigger: 'blur' },
        { validator: formValidate('plateNumber'), trigger: 'blur' },
        { validator: checkVehicleNumber, trigger: 'blur' },
      ],
    },
    genInput('车辆型号', 'vehicleModel', false),
    genInput('使用单位', 'vehicleUseUnit', false),
    {
      field: 'tonnage',
      label: '吨位',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入吨位',
        precision: 2,
        min: 0,
        max: 999.99,
        addonAfter: '吨',
      },
    },
    genSelect('状态', 'status', true, 'string', statusOptions),
    {
      field: 'remark',
      label: '备注',
      component: 'Textarea',
      componentProps: { 
        placeholder: '请输入备注信息',
        rows: 4,
        maxlength: 500,
        showCount: true,
      },
    },
  ],
});

const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);



// 车牌号唯一性验证
async function checkVehicleNumber(_rule: any, value: string) {
  if (!value) return Promise.resolve();

  try {
    const res = await vehicleInfoApi.checkVehicleNumber(value, unref(id));
    if (res.data && res.data.exists) {
      return Promise.reject('该车牌号已存在');
    }
    return Promise.resolve();
  } catch (error) {
    return Promise.resolve(); // 网络错误时不阻止提交
  }
}

function init(data: any) {
  resetFields();
  id.value = data.id;
  
  if (id.value) {
    changeLoading(true);
    vehicleInfoApi.getById(id.value).then(res => {
      setFieldsValue({
        ...res.data,
        status: res.data.status || '0', // 默认状态为编制
      });
      changeLoading(false);
    }).catch(() => {
      changeLoading(false);
    });
  } else {
    // 新增时设置默认值
    setFieldsValue({
      status: '0', // 默认状态为编制
    });
  }
}

async function handleSubmit() {
  try {
    changeOkLoading(true);
    const values = await validate();
    
    const params = {
      ...values,
      id: unref(id) || undefined,
    };

    if (unref(id)) {
      await vehicleInfoApi.update(params);
      createMessage.success('修改成功');
    } else {
      await vehicleInfoApi.save(params);
      createMessage.success('新增成功');
    }
    
    closePopup();
    emit('reload');
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    changeOkLoading(false);
  }
}


</script>
