<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit" :destroyOnClose="true">
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicPopup, usePopupInner } from "/@/components/Popup";
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { oaFixedQrApi as api } from '/@/api';
  import { genEditor, genInput, genSelectMulti, genTime } from '/@/utils/formUtils';

  const id = ref('');

  const getTitle = computed(() => (!unref(id) ? '新建数据' : '编辑数据'));
  const emit = defineEmits(['register', 'reload']);
  const { createMessage } = useMessage();

  // 周选项配置
  const weekOptions = [
    { fullName: '周一', id: '1' },
    { fullName: '周二', id: '2' },
    { fullName: '周三', id: '3' },
    { fullName: '周四', id: '4' },
    { fullName: '周五', id: '5' },
    { fullName: '周六', id: '6' },
    { fullName: '周日', id: '7' },
  ];

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      genInput('标题', 'title'),
      genInput('地点', 'location'),
      genInput('主持人', 'hostUser'),
      genTime('开始时间', 'startTime'),
      genTime('结束时间', 'endTime'),
      // genSelect('每周计划', 'weeklySchedule',true,'string',weekOptions),
      genSelectMulti('每周计划', 'weeklySchedule',true,weekOptions),
      genEditor('内容', 'content'),
    ],
  });
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

  function init(data:any) {
    resetFields();
    id.value = data.id;
    setFieldsValue({ treeId: data.treeId })
    if (id.value) {
      changeLoading(true);
      api.getById(id.value).then(res => {
        // 处理周计划数据，如果是字符串，转为数组
        if (res.data.weeklySchedule && typeof res.data.weeklySchedule === 'string') {
          res.data.weeklySchedule = res.data.weeklySchedule.split(',');
        }
        setFieldsValue(res.data);
        changeLoading(false);
      });
    }
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;
    
    // 手动校验时间字段
    if (!values.startTime) {
      createMessage.error('开始时间不能为空');
      return;
    }
    if (!values.endTime) {
      createMessage.error('结束时间不能为空');
      return;
    }
    
    // 转换时间格式用于比较
    const startTimeStr = typeof values.startTime === 'string' ? values.startTime : values.startTime.format('HH:mm:ss');
    const endTimeStr = typeof values.endTime === 'string' ? values.endTime : values.endTime.format('HH:mm:ss');
    
    // 比较开始时间和结束时间
    if (startTimeStr > endTimeStr) {
      createMessage.error('开始时间不能大于结束时间');
      return;
    }
    
    changeOkLoading(true);

    // 处理周计划数据，如果是数组，转为逗号分隔的字符串
    if (values.weeklySchedule && Array.isArray(values.weeklySchedule)) {
      values.weeklySchedule = values.weeklySchedule.join(',');
    }

    // 确保时间字段有值并格式化
    if (values.startTime && typeof values.startTime !== 'string') {
      values.startTime = values.startTime.format('HH:mm:ss');
    }
    if (values.endTime && typeof values.endTime !== 'string') {
      values.endTime = values.endTime.format('HH:mm:ss');
    }

    const query = {
      ...values,
      id: id.value,
    };
    const formMethod = id.value ? api.update : api.save;
    formMethod(query).then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closePopup();
      emit('reload');
    }).catch(() => changeOkLoading(false));
  }
</script>
