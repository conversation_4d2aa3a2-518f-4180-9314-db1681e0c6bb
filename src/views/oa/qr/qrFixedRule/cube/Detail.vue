<template>
  <div v-if="record" class="fa-flex-column fa-flex-column-center fa-p12">
    <ASpace class="fa-mb12">
      <a-button @click="handlePrintAll">打印</a-button>
<!--      <a-button @click="handlePrintInternal">打印内部二维码：具体会议</a-button>-->
<!--      <a-button @click="handlePrintExternal">打印外部二维码：具体会议</a-button>-->
<!--      <a-button @click="handlePrintQr3">打印内部二维码：会议列表</a-button>-->
<!--      <a-button @click="handlePrintQr4">打印外部二维码：会议列表</a-button>-->
    </ASpace>
    <!-- 显示区域 -->
    <div id="qrItemDetail" class="content-container" style="margin: 0 auto">
      <div class="fa-h2 fa-mb12 fa-text-center">{{record.title}}</div>
      <div class="fa-h4 fa-mb20 fa-text-center">{{record.startTime}} - {{record.endTime}}</div>
      
<!--      <div class="fa-flex-row fa-flex-center">-->
<!--        <div id="internalQrCode" class="fa-flex-column fa-flex-center fa-mr12">-->
<!--          <div class="fa-mb8 fa-text-center">内部二维码：具体会议</div>-->
<!--          <QrCode :value="JSON.stringify({t:'qrItemFixed',id:record.id})" :width="330" :options="{ margin: 1 }" />-->
<!--        </div>-->
<!--        <div id="externalQrCode" class="fa-flex-column fa-flex-center">-->
<!--          <div class="fa-mb8 fa-text-center">外部二维码：具体会议</div>-->
<!--          <QrCode :value="'http://192.168.5.116:8080/pages/public/noAuth/index?id=' + record.id" :width="330" :options="{ margin: 1 }" />-->
<!--        </div>-->
<!--      </div>-->
      
      <!-- 新增的二维码行 -->
      <div class="fa-flex-row fa-flex-center fa-mt20">
<!--        <div id="qrCode3" class="fa-flex-column fa-flex-center fa-mr12">-->
<!--          <div class="fa-mb8 fa-text-center">内部二维码：会议列表</div>-->
<!--          <QrCode :value="JSON.stringify({t:'qrItemFixed'})" :width="330" :options="{ margin: 1 }" />-->
<!--        </div>-->
        <div id="qrCode4" class="fa-flex-column fa-flex-center">
          <div class="fa-mb8 fa-text-center">会议列表</div>
          <QrCode :value="'http://zz.szh.h5.dward.cn/pages/public/noAuth/index'" :width="330" :options="{ margin: 1 }" />
        </div>
      </div>

      <div v-html="record.content"></div>
    </div>
    
    <!-- 打印专用区域 (隐藏) -->
    <div style="display: none;">
      <!-- 打印全部专用模板，去掉挤压二维码的文字 -->
      <div id="printAllContent" class="print-all-template">
        <div class="fa-h2 fa-mb12 fa-text-center">{{record.title}}</div>
        <div class="fa-h4 fa-mb20 fa-text-center">{{record.startTime}} - {{record.endTime}}</div>
        
        <!-- 使用表格布局避免挤压 -->
        <table class="qr-print-table">
          <tr>
<!--            <td class="qr-cell">-->
<!--              <div class="qr-container">-->
<!--                <QrCode :value="JSON.stringify({t:'qrItemFixed',id:record.id})" :width="330" :options="{ margin: 1 }" />-->
<!--                <div class="qr-label">内部二维码：具体会议</div>-->
<!--              </div>-->
<!--            </td>-->
<!--            <td class="qr-cell">-->
<!--              <div class="qr-container">-->
<!--                <QrCode :value="'http://192.168.5.116:8080/pages/public/noAuth/index?id=' + record.id+ '&type=only'" :width="330" :options="{ margin: 1 }" />-->
<!--                <div class="qr-label">外部二维码：具体会议</div>-->
<!--              </div>-->
<!--            </td>-->
          </tr>
          <tr>
<!--            <td class="qr-cell">-->
<!--              <div class="qr-container">-->
<!--                <QrCode :value="JSON.stringify({t:'qrItemFixed'})" :width="330" :options="{ margin: 1 }" />-->
<!--                <div class="qr-label">内部二维码：会议列表</div>-->
<!--              </div>-->
<!--            </td>-->
            <td class="qr-cell">
              <div class="qr-container">
                <QrCode :value="'http://zz.szh.h5.dward.cn/pages/public/noAuth/index'" :width="330" :options="{ margin: 1 }" />
                <div class="qr-label">会议列表</div>
              </div>
            </td>
          </tr>
        </table>
        
        <div v-if="record.content" class="fa-mt20 content-section" v-html="record.content"></div>
      </div>

      <!-- 内部二维码打印模板 -->
<!--      <div id="printInternalQrCode" class="fa-flex-column fa-flex-center print-template">-->
<!--        <div class="fa-h2 fa-mb12 fa-text-center">{{record.title}}</div>-->
<!--        <div class="fa-h4 fa-mb20 fa-text-center">{{record.startTime}} - {{record.endTime}}</div>-->
<!--        <div class="fa-mb8 fa-text-center">内部二维码：具体会议</div>-->
<!--        <QrCode :value="JSON.stringify({t:'qrItemFixed',id:record.id})" :width="330" :options="{ margin: 1 }" />-->
<!--      </div>-->
      
      <!-- 外部二维码打印模板 -->
<!--      <div id="printExternalQrCode" class="fa-flex-column fa-flex-center print-template">-->
<!--        <div class="fa-h2 fa-mb12 fa-text-center">{{record.title}}</div>-->
<!--        <div class="fa-h4 fa-mb20 fa-text-center">{{record.startTime}} - {{record.endTime}}</div>-->
<!--        <div class="fa-mb8 fa-text-center">外部二维码：具体会议</div>-->
<!--        <QrCode :value="'http://zz.szh.h5.dward.cn/pages/public/noAuth/index?id=' + record.id+'&type=only'" :width="330" :options="{ margin: 1 }" />-->
<!--      </div>-->
      
      <!-- 二维码3打印模板 -->
<!--      <div id="printQrCode3" class="fa-flex-column fa-flex-center print-template">-->
<!--        <div class="fa-h2 fa-mb12 fa-text-center">{{record.title}}</div>-->
<!--        <div class="fa-h4 fa-mb20 fa-text-center">{{record.startTime}} - {{record.endTime}}</div>-->
<!--        <div class="fa-mb8 fa-text-center">内部二维码：会议列表</div>-->
<!--        <QrCode :value="JSON.stringify({t:'qrItemFixed'})" :width="330" :options="{ margin: 1 }" />-->
<!--      </div>-->
      
      <!-- 二维码4打印模板 -->
      <div id="printQrCode4" class="fa-flex-column fa-flex-center print-template">
        <div class="fa-h2 fa-mb12 fa-text-center">{{record.title}}</div>
        <div class="fa-h4 fa-mb20 fa-text-center">{{record.startTime}} - {{record.endTime}}</div>
        <div class="fa-mb8 fa-text-center">会议列表</div>
        <QrCode :value="'http://zz.szh.h5.dward.cn/pages/public/noAuth/index'" :width="330" :options="{ margin: 1 }" />
      </div>
    </div>
  </div>
  <div v-else class="fa-flex-row fa-flex-center" style="padding: 40px;">
    <LoadingText />
  </div>
</template>
<script lang="ts" setup>
import { oaFixedQrApi as api } from '/@/api';
import { ref, watch } from "vue";
import { QrCode } from "/@/components/Qrcode";
import printJS from "print-js";
import { LoadingText } from "/@/components/Loading";

const props = defineProps(['id']); // 暴露给外部传入的属性

const record = ref<any>()

function handlePrintAll() {
  printJS({
    printable: 'printAllContent',
    type: 'html',
    documentTitle: record.value?.title || '会议二维码'
  })
}

function handlePrintInternal() {
  printJS({
    printable: 'printInternalQrCode',
    type: 'html',
    documentTitle: record.value?.title || '二维码'
  })
}

function handlePrintExternal() {
  printJS({
    printable: 'printExternalQrCode',
    type: 'html',
    documentTitle: record.value?.title || '二维码'
  })
}

function handlePrintQr3() {
  printJS({
    printable: 'printQrCode3',
    type: 'html',
    documentTitle: record.value?.title || '二维码3'
  })
}

function handlePrintQr4() {
  printJS({
    printable: 'printQrCode4',
    type: 'html',
    documentTitle: record.value?.title || '二维码4'
  })
}

watch(() => props.id, () => {
  if (props.id) {
    record.value = null
    api.getById(props.id).then(res => {
      record.value = res.data
    })
  }
}, { immediate: true })
</script>
<style lang="less">
.content-container {
  height: 80vh;
  overflow-y: auto;
  padding-right: 10px;
}

.print-template {
  padding: 20px;
  margin: 0 auto;
  max-width: 500px;
}

.print-all-template {
  padding: 20px;
  margin: 0 auto;
  width: 100%;
}

/* 表格布局样式 */
.qr-print-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 30px;
  table-layout: fixed;
}

.qr-cell {
  width: 50%;
  padding: 20px;
  text-align: center;
  vertical-align: middle;
}

.qr-container {
  display: inline-block;
  margin: 0 auto;
}

.qr-label {
  margin-top: 10px;
  text-align: center;
  font-size: 14px;
}

.content-section {
  page-break-before: always;
}

@media print {
  .fa-flex-column {
    display: flex;
    flex-direction: column;
  }
  
  .fa-flex-row {
    display: flex;
    flex-direction: row;
  }
  
  .fa-flex-center {
    align-items: center;
    justify-content: center;
  }
  
  .fa-mr12 {
    margin-right: 12px;
  }
  
  .fa-mb8 {
    margin-bottom: 8px;
  }

  .fa-mt8 {
    margin-top: 8px;
  }
  
  .fa-mb12 {
    margin-bottom: 12px;
  }
  
  .fa-mb20 {
    margin-bottom: 20px;
  }
  
  .fa-mt20 {
    margin-top: 20px;
  }
  
  .fa-text-center {
    text-align: center;
  }
  
  /* 保证打印时二维码不被挤压 */
  .qr-print-table {
    page-break-after: always;
  }
  
  .qr-cell {
    page-break-inside: avoid;
  }
  
  /* 内容区域单独分页 */
  .content-section {
    page-break-before: always;
  }
}
</style>
