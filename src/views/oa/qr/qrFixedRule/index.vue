<script setup lang="ts">
  import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
  import Form from '/@/views/oa/qr/qrFixedRule/Form.vue';
  import { oaFixedQrApi as api } from '/@/api';
  import { genDeleteBtn, genEditBtn, genQueryInput, genQuerySearch } from '/@/utils/tableUtils';
  import { usePopup } from '/@/components/Popup';
  import { useMessage } from '/@/hooks/web/useMessage';
  import DetailDrawer from '/@/views/oa/qr/qrFixedRule/DetailDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { computed, ref } from 'vue';

  const { createMessage, createConfirm } = useMessage();
  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
  const columns: BasicColumn[] = [
    { title: '每周排期', dataIndex: 'weeklySchedule', width: undefined },
    { title: '标题', dataIndex: 'title', width: undefined },
    { title: '地点', dataIndex: 'location', width: undefined },
    { title: '主持人', dataIndex: 'hostUser', width: undefined },
    { title: '开始时间', dataIndex: 'startTime', width: 150 },
    { title: '结束时间', dataIndex: 'endTime', width: 150 },
    { title: '创建时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
  ];

  // 选中的行keys
  const selectedRowKeys = ref<string[]>([]);

  // 判断生成会议按钮是否可用
  const generateBtnDisabled = computed(() => selectedRowKeys.value.length === 0);

  // 表格行选择配置
  const rowSelection = {
    onChange: (selectedKeys: string[]) => {
      selectedRowKeys.value = selectedKeys;
    }
  };

  const [registerTable, { reload }] = useTable({
    api: api.minePage,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [genQuerySearch(), genQueryInput('标题', 'title')],
    },
    actionColumn: {
      width: 140,
      title: '操作',
      dataIndex: 'action',
    },
    searchInfo: {
      _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
    },
    rowSelection, // 添加行选择配置
  });

  function getTableActions(record: any): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: () => openDetailDrawer(true, { id: record.id }),
      },
      genEditBtn(record, addOrUpdateHandle),
      genDeleteBtn(record, handleDelete),
    ];
  }

  function addOrUpdateHandle(id = '') {
    openFormPopup(true, { id });
  }

  function generationMeeting() {
    if (selectedRowKeys.value.length === 0) {
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '确认生成会议',
      content: `确定要生成${selectedRowKeys.value.length}个会议吗？`,
      onOk: () => {
        api.generateMeeting(selectedRowKeys.value).then(res => {
          createMessage.success(res.msg || '生成会议成功');
          reload();
          // 清空选择
          selectedRowKeys.value = [];
        }).catch(error => {
          createMessage.error(error.message || '生成会议失败');
        });
      }
    });
  }

  function handleDelete(id: any) {
    api.remove(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }
</script>

<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新建</a-button>
            <a-button 
              @click="generationMeeting()" 
              type="primary"
              :disabled="generateBtnDisabled"
            >生成会议</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isActive'">
              <a-tag v-if="record.isActive === 0">停用</a-tag>
              <a-tag v-if="record.isActive === 1" color="processing">启用</a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
  </div>
  <DetailDrawer @register="registerDetailDrawer" />
</template>

<style scoped lang="less"></style>
