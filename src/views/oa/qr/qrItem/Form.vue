<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit" :destroyOnClose="true">
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from "/@/components/Popup";
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { oaQrItemApi as api } from '/@/api';
import { genDateTime, genEditor, genInput } from "/@/utils/formUtils";

const id = ref('');

const getTitle = computed(() => (!unref(id) ? '新建数据' : '编辑数据'));
const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  schemas: [
    genInput('标题', 'title'),
    genInput('地点', 'location'),
    genInput('主持人', 'hostUser'),
    genDateTime('开始时间', 'startTime'),
    genDateTime('结束时间', 'endTime'),
    genEditor('内容', 'content'),
  ],
});
const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

function init(data:any) {
  resetFields();
  id.value = data.id;
  setFieldsValue({ treeId: data.treeId })
  if (id.value) {
    changeLoading(true);
    api.getById(id.value).then(res => {
      setFieldsValue(res.data);
      changeLoading(false);
    });
  }
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  
  // 根据开始时间和结束时间计算status
  const now = new Date();
  const startTime = values.startTime ? new Date(values.startTime) : null;
  const endTime = values.endTime ? new Date(values.endTime) : null;
  
  let status: number | undefined = undefined;
  if (startTime && endTime) {
    if (now < startTime) {
      status = 0; // 未开始
    } else if (now >= startTime && now <= endTime) {
      status = 1; // 进行中
    } else {
      status = 2; // 已结束
    }
  }
  
  const query = {
    ...values,
    id: id.value,
    status,
  };
  const formMethod = id.value ? api.update : api.save;
  formMethod(query).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closePopup();
    emit('reload');
  }).catch(() => changeOkLoading(false));
}
</script>
