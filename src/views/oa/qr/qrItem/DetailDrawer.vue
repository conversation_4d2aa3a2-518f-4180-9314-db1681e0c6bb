<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="二维码详情" width="1000px" class="full-drawer chat-drawer">
    <div class="fa-pl12 fa-pr12">
      <a-tabs>
        <a-tab-pane key="1" tab="详情">
          <Detail v-if="id" :id="id" />
        </a-tab-pane>
        <a-tab-pane key="2" tab="签到表">
          <SignList v-if="id" :id="id" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { ref } from "vue";
import Detail from './cube/Detail.vue'
import SignList from './cube/SignList.vue'

const [registerDrawer] = useDrawerInner(init);

const id = ref<any>()

function init(data:any) {
  console.log('init', data)
  id.value = data.id;
}

</script>
<style lang="less">
</style>
