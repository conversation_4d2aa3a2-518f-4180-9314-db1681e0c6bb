<template>
  <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printThePaper()">打印签到表</a-button>
  <div class="fa-flex-column fa-flex-column-center fa-p12">
    <BasicTable @register="registerTable" class="print-table">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'signImg'">
          <div>
            <img :src="record.signImg" alt="" class="qr-sign-img" />
          </div>
        </template>
        <template v-else-if="column.key === 'userName'">
          {{ isExternalPerson(record) ? record.userName : record.creatorUserName }}
        </template>
        <template v-else-if="column.key === 'department'">
          {{ isExternalPerson(record) ? record.userDept : record.department }}
        </template>
        <template v-else-if="column.key === 'position'">
          {{ isExternalPerson(record) ? record.userPos : record.position }}
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { oaQrItemApi, oaQrItemUserScanApi } from '/@/api';
  import { reactive, watch, ref } from 'vue';
  import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { useMessage } from '/@/hooks/web/useMessage';
  import logoImg from '/@/assets/images/qrLogo.png';

  const { createMessage } = useMessage();
  const props = defineProps(['id']);
  const searchInfo = reactive({
    qrId: props.id,
    _sorter: 'f_creator_time DESC',
  });

  const qrItemInfo = ref<any>({});

  // 判断是否为外来人员
  const isExternalPerson = (record: any) => {
    console.log('记录数据:', record);
    return record.source === 2 || record.source === '2';
  };

  const columns: BasicColumn[] = [
    { title: '用户姓名', key: 'userName', dataIndex: 'creatorUserName', width: 120 },
    { title: '部门/单位', key: 'department', dataIndex: 'department', width: 100 },
    { title: '职务/职称', key: 'position', dataIndex: 'position', width: 100 },
    { title: '签名', key: 'signImg', dataIndex: 'signImg', width: 200 },
    { title: '签到时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm' },
  ];

  const [registerTable, { reload, getDataSource }] = useTable({
    api: oaQrItemUserScanApi.list,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 140,
      title: '操作',
      dataIndex: 'action',
    },
    searchInfo,
    pagination: false,
    beforeFetch: params => {
      console.log('请求参数:', params);
      return params;
    },
    afterFetch: data => {
      console.log('API返回数据:', data);
      return data;
    },
  });

  function getTableActions(record: any): ActionItem[] {
    return [
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除此签到记录吗？',
          confirm: handleDelete.bind(null, record.id || ''),
        },
      },
    ];
  }

  // 删除设备档案
  function handleDelete(id: string) {
    if ((id || '') != '') {
      oaQrItemUserScanApi
        .remove(id)
        .then(res => {
          createMessage.success(res.msg || '删除成功');
          reload();
        })
        .finally(() => {});
    }
  }

  const fetchQrItemInfo = async () => {
    try {
      const res = await oaQrItemApi.getById(props.id);
      if (res && res.data) {
        qrItemInfo.value = res.data;
      }
    } catch (error) {
      console.error('获取签到表信息失败', error);
    }
  };

  const printThePaper = () => {
    const dataSource = getDataSource();
    console.log('打印数据:', dataSource);
    
    // 获取logo的完整URL
    const logoUrl = new URL(logoImg, window.location.origin).href;

    const printContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>会议签到表</title>
        <style>
          @media print {
            @page {
              margin: 20px 40px;
              size: auto;
            }
            body {
              font-family: SimSun, Arial, sans-serif;
              margin: 0;
              position: relative;
            }
            .logo {
              position: absolute;
              top: 10px;
              left: 10px;
              height: 40px;
              z-index: 100;
            }
            h1 {
              text-align: center;
              margin-bottom: 20px;
              font-size: 24px;
              font-weight: bold;
            }
            table {
              width: 100% !important;
              border-collapse: collapse;
              margin-bottom: 0;
            }
            table, th, td {
              border: 1px solid #000 !important;
            }
            th, td {
              padding: 8px 12px;
              text-align: center;
              font-size: 14px;
              height: 30px;
              line-height: 1.5;
            }
            th {
              font-weight: bold;
            }
            .title-cell {
              width: 80px;
              text-align: center;
              font-weight: bold;
              white-space: nowrap;
            }
            .value-cell {
              text-align: left;
              padding-left: 10px;
            }
            .info-value-cell {
              width: 25%;
              text-align: left;
              padding-left: 10px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .qr-sign-img {
              height: 50px !important;
              max-width: 150px;
            }
            .empty-cell {
              height: 40px;
            }
            .print-table {
              margin-top: 0 !important;
            }
            .print-table th {
              text-align: center !important;
            }
          }
        </style>
      </head>
      <body>
          <div style="text-align: left; margin-bottom: 10px;">
            <img src="${logoUrl}" style="height: 40px;" />
          </div>
          <h1 style="margin: 0 0 20px 0; text-align: center;">会议签到表</h1>
          <table>
            <tr>
              <td class="title-cell">主 题</td>
              <td class="value-cell" colspan="5">${qrItemInfo.value.title || ''}</td>
            </tr>
            <tr>
              <td class="title-cell">时 间</td>
              <td class="info-value-cell">${formatToDateTime(qrItemInfo.value.startTime) || ''}</td>
              <td class="title-cell">地 点</td>
              <td class="info-value-cell">${qrItemInfo.value.location || ''}</td>
              <td class="title-cell">主持人</td>
              <td class="info-value-cell">${qrItemInfo.value.hostUser || ''}</td>
            </tr>
          </table>
          
          <table>
            <thead>
              <tr>
                <th>序 号</th>
                <th>姓 名</th>
                <th>部门/单位</th>
                <th>职务/职称</th>
                <th>签 字</th>
              </tr>
            </thead>
            <tbody>
              ${dataSource
                .map(
                  (item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${isExternalPerson(item) ? item.userName || '' : item.creatorUserName || ''}</td>
                  <td>${isExternalPerson(item) ? item.userDept || '' : item.department || ''}</td>
                  <td>${isExternalPerson(item) ? item.userPos || '' : item.position || ''}</td>
                  <td>${item.signImg ? '<img src="' + item.signImg + '" class="qr-sign-img" />' : ''}</td>
                </tr>
              `,
                )
                .join('')}
              ${Array.from({ length: Math.max(0, 15 - dataSource.length) })
                .map(
                  (_, i) => `
                <tr>
                  <td>${dataSource.length + i + 1}</td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>
              `,
                )
                .join('')}
            </tbody>
          </table>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 100);
    }
  };

  watch(
    () => props.id,
    () => {
      if (props.id) {
        searchInfo.qrId = props.id;
        fetchQrItemInfo();
        reload();
      }
    },
    { immediate: true },
  );
</script>

<style lang="less">
  @media print {
    body * {
      visibility: hidden;
    }

    .print-table,
    .print-table * {
      visibility: visible;
    }

    .print-table {
      position: absolute;
      left: 0;
      top: 0;
      width: 100% !important;
    }
  }

  .qr-sign-img {
    height: 30px;
  }
</style>
