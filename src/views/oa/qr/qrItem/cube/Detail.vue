<template>
  <div v-if="record" class="fa-flex-column fa-flex-column-center fa-p12">
    <ASpace class="fa-mb12">
      <a-button @click="handlePrint">打印</a-button>
      <!--        <a-button @click="handleDownload">下载图片</a-button>-->
    </ASpace>
    <div id="qrItemDetail" style="margin: 0 auto">
      <div class="fa-h1 fa-mb12">{{record.title}}</div>
      <div class="fa-h3 fa-mb12">{{formatToDateTime(record.startTime)}} - {{formatToDateTime(record.endTime)}}</div>
      <div>
        <QrCode :value="JSON.stringify({t:'qrItem',id:record.id})" :width="330" :options="{ margin: 1 }" />
      </div>

      <div v-html="record.content"></div>
    </div>
  </div>
  <div v-else class="fa-flex-row fa-flex-center" style="padding: 40px;">
    <LoadingText />
  </div>
</template>
<script lang="ts" setup>
import { oaQrItemApi as api } from '/@/api';
import { ref, watch } from "vue";
import { formatToDateTime } from "/@/utils/dateUtil";
import { QrCode } from "/@/components/Qrcode";
import printJS from "print-js";
import { LoadingText } from "/@/components/Loading";

const props = defineProps(['id']); // 暴露给外部传入的属性

const record = ref<any>()

function handlePrint() {
  printJS('qrItemDetail', 'html')
}

watch(() => props.id, () => {
  if (props.id) {
    record.value = null
    api.getById(props.id).then(res => {
      record.value = res.data
    })
  }
}, { immediate: true })
</script>
<style lang="less">
</style>
