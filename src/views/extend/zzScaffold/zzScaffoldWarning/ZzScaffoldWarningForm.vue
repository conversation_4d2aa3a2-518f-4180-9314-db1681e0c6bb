<template>
  <BasicPopup v-bind="$attrs" @register="registerModal" title="脚手架维护记录" showOkBtn @ok="handleSubmit" :destroyOnClose="true">
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { toRefs, reactive, nextTick,ref, unref } from 'vue';
  import { getScheduleDetail } from '/@/api/onlineDev/portal';
  import{zzScaffoldApi ,zzScaffoldWarningApi}from "/@/api/index"
  import { BasicPopup, usePopupInner } from '/@/components/Popup';
  import { BasicForm, useForm } from '/@/components/Form';
  import { genDate, genTextarea,genUser,genDateTime } from '/@/utils/formUtils';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage } = useMessage();
  const id = ref('');
  interface State {
    dataForm: any;
    type: number;
    groupId: number;
    visible: boolean;
    checked: number;
    btnLoading: boolean;
  }

  const state = reactive<State>({
    dataForm: {},
    type: 0,
    groupId: 0,
    visible: false,
    checked: 1,
    btnLoading: false,
  });
  const [registerModal, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    schemas: [
      genTextarea('脚手架编码', 'scaffoldNo'),
      genUser('维护人名称', 'maintainPersonId'), 
      genTextarea('维护内容', 'maintainContent'), 
      genDateTime('维护时间', 'maintainDate')
    ],
  });

  const emit = defineEmits(['register', 'reload']);

  function init(data) {
    state.dataForm.scaffoldId = data.id || '';
    state.type = data.type || 0;
    state.groupId = data.groupId || 0;
    // console.log(state.dataForm.id)
    nextTick(() => {
      changeLoading(true);
      zzScaffoldApi.getDetailById(state.dataForm.scaffoldId)
        .then(res => {
          state.dataForm = res.data || {};
          console.log(state.dataForm)
          state.dataForm.maintainPersonId = userInfo.userId
          state.dataForm.maintainPersonName = userInfo.userName
          state.dataForm.maintainDate = new Date().getTime(); 
          state.dataForm.scaffoldId = data.id;
          state.dataForm.creatorTime = null;
          state.dataForm.creatorUserId = null;
          state.dataForm.creatorUserName = null;
          state.dataForm.lastModifyTime = null;
          state.dataForm.lastModifyUserId = null;
          setFieldsValue(state.dataForm)
          changeLoading(false);
        })
        .catch(() => {
          changeLoading(false);
        });
    });
  }
  
  // function handleDel() {
  //   state.checked = 1;
  //   if (state.dataForm.repetition != '1') {
  //     state.btnLoading = false;
  //     state.visible = true;
  //     return;
  //   }
  //   createConfirm({
  //     iconType: 'warning',
  //     title: t('common.tipTitle'),
  //     content: '此操作将永久删除此日程，同时删除所有参与人的日程，是否继续？',
  //     onOk: () => {
  //       handleDelFun();
  //     },
  //   });
  // }
  // function handleDelFun() {
  //   state.btnLoading = true;
  //   if (!state.checked && state.dataForm.repetition != '1') return createMessage.warning('请选择日程');
  //   delSchedule(state.dataForm.id, state.dataForm.repetition != '1' ? state.checked : 3).then(res => {
  //     createMessage.success(res.msg);
  //     state.visible = false;
  //     closeModal();
  //     emit('reload');
  //   });
  // }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;
    changeOkLoading(true);
    const query = {
      ...values,
    };
    const formMethod = zzScaffoldWarningApi.save;
    formMethod(query)
      .then(res => {
        createMessage.success(res.msg);
        changeOkLoading(false);
        closePopup();
        setTimeout(() => {
          emit('reload');
        }, 300);
      })
      .catch(() => changeOkLoading(false));
  }
</script>
