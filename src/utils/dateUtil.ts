/**
 * Independent time operation tool to facilitate subsequent switch to dayjs
 */
import dayjs from 'dayjs';
import { getDateFormat } from '/@/utils/jnpf';

import duration from 'dayjs/plugin/duration'
dayjs.extend(duration)

export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
export const DATE_FORMAT = 'YYYY-MM-DD';
export const DATE_FORMAT_NEW = 'YYYY年MM月DD日';

export type FormatDate = dayjs.ConfigType;

export function formatToDateTime(date: dayjs.ConfigType = undefined, format = DATE_TIME_FORMAT): string {
  return dayjs(date).format(getDateFormat(format));
}

export function formatToDate(date: dayjs.ConfigType = undefined, format = DATE_FORMAT): string {
  if(date===null)
    return ""
  return dayjs(date).format(getDateFormat(format));
}

export function formatToDateNew(date: dayjs.ConfigType = undefined, format = DATE_FORMAT): string {
  if(date===null)
    return ""
  let dataStrArr = dayjs(date).format(getDateFormat(format)).split('-');
  return `${dataStrArr[0]}年${dataStrArr[1]}月${dataStrArr[2]}日`
}

/**
 * 将视频类时长转换为文本描述
 * @param duration 秒
 */
export function durationStr(duration: number) {
  if (duration === undefined) return '';
  const dur = dayjs.duration(duration, 'seconds')
  const hour = dur.hours();
  const minute = dur.minutes();
  const second = dur.seconds();
  let str = '';
  if (hour > 0) {
    str += `${hour}h`
  }
  if (minute > 0) {
    str += `${minute}m`
  }
  if (second > 0) {
    str += `${second}s`
  }
  return str;
}

export function durationStrNew(duration: number) {
  console.log('durationStrNew',duration)
  if (duration === undefined) return '';
  const dur = dayjs.duration(duration, 'seconds')
  const hour = dur.hours();
  const minute = dur.minutes();
  const second = dur.seconds();
  let str = '';
  if (hour > 0) {
    str += `${hour}:`
  }else {
    str += '00:'
  }
  if (minute > 0) {
    str += `${minute}:`
  }else {
    str += '00:'
  }
  if (second > 0) {
    str += `${second}`
  }else {
    str += '00'
  }
  console.log('str',str)
  return str;
}

/* 一天的开始时间 */
export function startTime(time) {
  const nowTimeDate = new Date(time);
  return nowTimeDate.setHours(0, 0, 0, 0);
}

/* 一天的结束时间 */
export function endTime(time) {
  const nowTimeDate = new Date(time);
  return nowTimeDate.setHours(23, 59, 59, 999);
}

/* 获取当周开始时间和结束时间 */
export function getCurrentWeekStartTimeAndEndTime(time) {
  const current = time ? time : new Date();
  // current是本周的第几天
  let nowDayOfWeek = current.getDay();
  if (nowDayOfWeek === 0) nowDayOfWeek = 7;
  const dayNum = 1 * 24 * 60 * 60 * 1000;
  // 获取本周星期一的时间，星期一作为一周的第一天
  const firstDate = new Date(current.valueOf() - (nowDayOfWeek - 1) * dayNum);
  // 获取本周星期天的时间，星期天作为一周的最后一天
  const lastDate = new Date(new Date(firstDate).valueOf() + 6 * dayNum);
  return {
    startTime: new Date(startTime(firstDate)),
    endTime: new Date(endTime(lastDate)),
  };
}


/* 获取当月开始时间和结束时间 */
export function getCurrentMonthFirst(time) {
  const date = time ? time : new Date();
  date.setDate(1);
  return startTime(date);
}

export function getCurrentMonthLast(time) {
  const date = time ? time : new Date();
  const currentMonth = date.getMonth();
  const nextMonth = currentMonth + 1;
  const nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
  const oneDay = 24 * 60 * 60 * 1000;
  return endTime(new Date(nextMonthFirstDay - oneDay));
}


/* 获取当年开始时间和结束时间 */
export function getCurrentYearFirst(date) {
  date = date ? date : new Date();
  date.setDate(1);
  date.setMonth(0);
  return startTime(date);
}

export function getCurrentYearLast(date) {
  date = date ? date : new Date();
  date.setFullYear(date.getFullYear() + 1); // 设置到明年
  date.setMonth(0); // 明年的0月，也就是对应到1月，是存在的哦，不是不存在的0
  date.setDate(0); // 明年的0日
  return endTime(date);
}

export function timeStrToSeconds(timeStr:string){
  const timeArr = timeStr.split(':');
  if (timeArr.length !== 3) return 0

  const hour = parseInt(timeArr[0])
  const minute = parseInt(timeArr[1])
  const second = parseInt(timeArr[2])

  return hour * 3600 + minute * 60 + second
}


export const dateUtil = dayjs;
