import { cloneDeep, each, find, isNil, trim } from 'lodash-es';
import { allInList } from '../fa-utils';

export function parseNode<T = any>(nodeList: any[] | undefined): any[] | undefined {
  if (isNil(nodeList) || nodeList.length === 0) return undefined;
  return nodeList.map((d) => ({
    id: d.id,
    parentId: d.parentId,
    name: d.name,
    // tree
    label: d.name,
    value: d.id,
    isLeaf: !d.hasChildren,
    children: parseNode<T>(d.children),
    sourceData: d.sourceData,
  })) as any[];
}

/** 平铺Tree型结构 */
export function flatTree(tree: any[] = [], pid: string | number = 0): any[] {
  const list: any[] = [];
  tree.forEach((item, index) => {
    const { children, id, name } = item;
    if (children?.[0]) {
      list.push(...flatTree(children, item.id));
    }
    list.push({ key: id, index, pid, name });
  });
  return list;
}

/**
 * 平铺Tree型结构，抽取item中的sourceData，进行平铺。
 * @param tree
 */
export function flatTreeList(tree: any[] = []): any[] {
  const list: any[] = [];
  tree.forEach((item) => {
    const { children, sourceData } = item;
    list.push(sourceData);
    if (children?.[0]) {
      list.push(...flatTreeList(children));
    }
  });
  return list;
}

/**
 * 平铺Tree型结构，直接平铺完整的item。
 * @param tree
 */
export function flatTreeSourceList(tree: any[] = []): any[] {
  const list: any[] = [];
  tree.forEach((item) => {
    const { children } = item;
    if (children?.[0]) {
      list.push(...flatTreeSourceList(children));
    }
    list.push(item);
  });
  return list;
}

/**
 * 根据条件，查询树中一个节点
 * @param tree
 * @param checkFun
 */
export function findTreeNode(tree: any[] | undefined, checkFun: (item: any) => boolean): any | undefined {
  const list = findTreePath(tree, checkFun);
  if (list && list[0]) {
    return list[list.length - 1];
  }
  return undefined;
}

/**
 * 根据条件，查询树中一个节点的完整路径sourceData
 * @param tree
 * @param checkFun
 */
export function findTreePathSource(tree: any[] | undefined, checkFun: (item: any) => boolean): any[] {
  if (isNil(tree) || tree.length === 0) return [];
  const findPathArr: any[] = [];
  for (const item of tree) {
    if (checkFun(item)) {
      return [item.sourceData];
    }
    const childFound = findTreePathSource(item.children, checkFun);
    if (childFound && childFound.length > 0) {
      findPathArr.push(item.sourceData, ...childFound);
    }
  }
  return findPathArr;
}

/**
 * 根据条件，查询树中一个节点的完整路径
 * @param tree
 * @param checkFun
 */
export function findTreePath(tree: any[] | undefined, checkFun: (item: any) => boolean): any[] {
  if (isNil(tree) || tree.length === 0) return [];
  const findPathArr: any[] = [];
  for (const item of tree) {
    if (checkFun(item)) {
      return [item];
    }
    const childFound = findTreePath(item.children, checkFun);
    if (childFound && childFound.length > 0) {
      findPathArr.push(item, ...childFound);
    }
  }
  return findPathArr;
}

/**
 * 通过比较item字段是否等于destId，返回路径。
 * @param options
 * @param destId
 */
function findPathInner(options: any[] | undefined, destId: any, label = 'value'): any[] | undefined {
  if (isNil(options)) return undefined;
  const findPathArr: any[] = [];
  for (const o of options) {
    // first check self is desc
    if (trim(o[label]) === trim(destId)) {
      return [o];
    }

    const childFound = findPathInner(o.children, destId, label);
    if (childFound && childFound.length > 0) {
      findPathArr.push(o, ...childFound);
    }
  }
  return findPathArr;
}

/**
 * 查找路径
 * @param options
 * @param destId
 */
export function findPath(options: any[] | undefined, destId: any, label = 'value'): any[] {
  return findPathInner(options, destId, label) || [];
}

/**
 * 返回第一个满足条件的节点。FIXME：可以优化
 * @param tree
 * @param checkFun
 */
export function findNodeInTree(tree: any[] | undefined, checkFun: (item: any) => boolean): any {
  if (isNil(tree)) return undefined;
  const list = flatTreeList(tree);
  return find(list, (i) => checkFun(i));
}

/**
 * 计算key在tree中的全勾选、半勾选状态，返回全勾选的。
 * @param treeData
 * @param checkedKeys
 */
export function calCheckedKey(treeData: any[] | undefined, checkedKeys: any[]): any[] | undefined {
  // treeData尚未初始化
  if (isNil(treeData) || treeData.length === 0) return undefined;

  const flatTreeData = flatTreeSourceList(treeData);
  // console.log('flatTree', flatTree)

  const cks: any[] = [];
  each(flatTreeData, (node) => {
    // 不在勾选的keys中，过滤掉
    if (!checkedKeys.includes(node.id)) {
      return;
    }
    // 以下为在勾选的keys中的情况判断
    // 1. 节点为叶子节点，无子节点，本身勾选则为全勾选
    if (isNil(node.children) || node.children.length === 0) {
      cks.push(node.id);
      return;
    }
    // 2. 节点为父节点，判断父节点的子节点是否全勾选，如果是，则该父节点为全勾选
    const cids = node.children.map((c) => c.id) || [];
    const childrenAllCheck = allInList(cks, cids);
    if (childrenAllCheck) {
      cks.push(node.id);
    }
  });

  return cks;
}

/**
 * 遍历树，对树中的item进行操作
 * @param treeData
 * @param touchFn
 */
export function touchItem(treeData: any[], touchFn: (item: any) => void) {
  for (const item of treeData) {
    touchFn(item.sourceData);
    if (item.children?.[0]) {
      touchItem(item.children, touchFn);
    }
  }
}

/**
 * 遍历树，对树中的item进行转换
 * @param treeData
 * @param mapFn
 */
export function mapItem(treeData: any[], mapFn: (item: any) => any) {
  const tree: any[] = [];
  for (const item of treeData) {
    const newItem = mapFn(item.sourceData);

    if (item.children?.[0]) {
      newItem.children = mapItem(item.children, mapFn);
    }

    tree.push(newItem);
  }
  return tree;
}

/**
 * 指定id，在tree中查找item，找到节点后回调
 * @param tree
 * @param id
 * @param callback 找到节点后的回调
 */
export function findTreeItem(
  tree: any[],
  id: any,
  callback?: (data: any, index: number, dataList: any[]) => void,
):
  | {
      node: any;
      index: number;
      siblings: any[];
    }
  | undefined {
  for (let i = 0; i < tree.length; i += 1) {
    if (tree[i].id === id) {
      if (callback) callback(tree[i], i, tree);
      return { node: tree[i], index: i, siblings: tree };
    }
    if (tree[i].children) {
      const result = findTreeItem(tree[i].children!, id, callback);
      if (result) return result;
    }
  }
}

/**
 * Tree节点拖动排序
 * @param tree tree数据
 * @param dragKey 拖动节点key
 * @param dropKey 放置节点key
 * @param dropPosition 放置位置：-1前/0上/1下
 * @param dropToGap 是否放置到间隙位置
 * @param dropNodeChildren 放置节点children
 * @param dropNodeExpand 放置节点是否展开
 */
export function dropItem(tree: any[], dragKey: any, dropKey: any, dropPosition: number, dropToGap: boolean | undefined, dropNodeChildren: any[], dropNodeExpand: boolean): any[] {
  const data = cloneDeep(tree);
  // const { node: dropNode } = findTreeItem(tree, dropKey)!;

  // Find dragObject
  const dragInfo = findTreeItem(data, dragKey)!;
  dragInfo.siblings.splice(dragInfo.index, 1);
  const dragObj = dragInfo.node;

  const dropInfo = findTreeItem(data, dropKey)!;
  if (!dropToGap) {
    // Drop on the content放置到节点的展开子节点上，默认追加到队尾
    dropInfo.node.children = dropInfo.node.children || [];
    // where to insert 添加到头部
    dropInfo.node.children.unshift(dragObj);
  } else if (
    (dropNodeChildren || []).length > 0 && // Has children
    dropNodeExpand && // Is expanded
    dropPosition === 1 // On the bottom gap在放置节点的下间隙
  ) {
    dropInfo.node.children = dropInfo.node.children || [];
    // where to insert 添加到头部
    dropInfo.node.children.unshift(dragObj);
  } else {
    const { index: i, siblings: ar } = dropInfo;
    if (dropPosition === -1) {
      ar.splice(i, 0, dragObj);
    } else {
      ar.splice(i + 1, 0, dragObj);
    }
  }

  return data;
}

export function setTreeDisabled(treeList: any[] | undefined, disabledIds?: any[]) {
  if (isNil(disabledIds) || disabledIds.length === 0) return;

  if (isNil(treeList) || treeList.length === 0) return;
  treeList.map((i) => {
    if (disabledIds.includes(i.id)) {
      i.disabled = true;
    } else {
      i.disabled = undefined;
    }
    setTreeDisabled(i.children, disabledIds);
  });
}
