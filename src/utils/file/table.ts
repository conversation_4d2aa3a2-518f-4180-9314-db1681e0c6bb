import * as XLSX from "xlsx";

//列字段接口
export interface colKeys {
  title:string;
  dataIndex:string;
  key:string;
}

export interface newColKeys {
  title:string;
  dataIndex:string;
  key:string;
  render:string;
}

//-----------------导出表格(基础)----------------------
export function exportTable(tableCol: colKeys[], dataList: any[], tableName: string) {
  //提取表头
  const tHeader = tableCol.map(column => column.title);
  // 将数据映射到表头，只取列定义中指定的键
  const data = dataList.map(item => {
    return tableCol.map(column => item[column.dataIndex]);
  });
  // 将数据数组转换为工作表
  const ws = XLSX.utils.aoa_to_sheet([tHeader, ...data]);
  // 将工作表转换为工作簿
  const wb = XLSX.utils.book_new();
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  // 生成Excel文件
  XLSX.writeFile(wb, tableName);
}
