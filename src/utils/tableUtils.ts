import {FormSchema} from "/@/components/Form";
import { BOOLEAN_OPTIONS, FLOW_OP_TYPE } from "/@/enums/zzEnums";
import { ActionItem } from "/@/components/Table";
import type { ComponentType } from "/@/components/Form/src/types";


/**
 * gen table header query _search
 */
export function genQuerySearch(): FormSchema {
  return {
    field: '_search',
    label: '关键词',
    component: 'Input',
    componentProps: {
      placeholder: '请输入关键词查询',
      submitOnPressEnter: true,
    },
  }
}

/**
 * gen table header query keyword
 */
export function genQueryKeyword(): FormSchema {
  return {
    field: 'keyword',
    label: '关键词',
    component: 'Input',
    componentProps: {
      placeholder: '请输入关键词查询',
      submitOnPressEnter: true,
    },
  }
}

/**
 * gen table header query input
 * @param label
 * @param field
 */
export function genQueryInput(label: string, field: string): FormSchema {
  return {
    field,
    label,
    component: 'Input',
    componentProps: {placeholder: `输入${label}`, maxlength: 50, submitOnPressEnter: true,},
  }
}

export function genQueryUser(label: string, field: string): FormSchema {
  return {
    field,
    label,
    component: 'UserSelect',
    componentProps: {placeholder: `输入${label}`, maxlength: 50, submitOnPressEnter: true,},
  }
}

export function genQueryDept(label: string, field: string): FormSchema {
  return {
    field,
    label,
    component: 'DepSelect',
    componentProps: {placeholder: `输入${label}`, maxlength: 50, submitOnPressEnter: true,},
  }
}

export function genQueryCommon(label: string, field: string, component: ComponentType): FormSchema {
  return {
    field,
    label,
    component,
    componentProps: {placeholder: `输入${label}`, maxlength: 50, submitOnPressEnter: true,},
  }
}

export function genQueryCascader(label: string, field: string, api: any): FormSchema {
  return {
    field,
    label,
    component: 'FaCascader',
    componentProps: {placeholder: `输入${label}`, api, showRoot: true, submitOnPressEnter: true,},
  }
}

export function genQueryInputRequired(label: string, field: string): FormSchema {
  return {
    field,
    label,
    component: 'Input',
    componentProps: {placeholder: `输入${label}`, maxlength: 50, submitOnPressEnter: true},
    rules: [{required: true, message: `请输入${label}`, trigger: 'blur'}],
  }
}

export function genQuerySelectBool(label: string, field: string): FormSchema {
  return genQuerySelect(label, field, BOOLEAN_OPTIONS)
}

export function genQuerySelect(label: string, field: string, options: any[] = []): FormSchema {
  return {
    field,
    label,
    component: 'Select',
    componentProps: {placeholder: `选择${label}`, submitOnPressEnter: true, options},
  }
}

export function genQuerySelectRequired(label: string, field: string, options: any[] = []): FormSchema {
  return {
    field,
    label,
    component: 'Select',
    componentProps: {placeholder: `选择${label}`, submitOnPressEnter: true, options},
    rules: [{required: true, message: `请输入${label}`, trigger: 'change'}],
  }
}

// 时间
export function genQueryTimeInput(label: string, field: string): FormSchema {
  return {
    field,
    label,
    component: 'DatePicker',
    componentProps: {
      type: 'time',
      placeholder: `选择${label}`,
      format: 'yyyy-MM-dd',
      valueFormat: 'yyyy-MM-dd',
    },
  }
}

// ------------------------------- 表格按钮 -------------------------------
export function genEditBtn(record, addOrUpdateHandle): ActionItem {
  return {
    label: '编辑',
    onClick: addOrUpdateHandle.bind(null, record.id),
  }
}

export function genDeleteBtn(record, handleDelete): ActionItem {
  return {
    label: '删除',
    color: 'error',
    modelConfirm: {
      onOk: handleDelete.bind(null, record.id),
    },
  }
}

export function genDangerBtn(record, handleDelete, label = '删除'): ActionItem {
  return {
    label,
    color: 'error',
    modelConfirm: {
      onOk: handleDelete.bind(null, record.id),
    },
  }
}


// ------------------------------- 流程按钮 -------------------------------
/**
 * 流程-编辑-按钮
 * @param record
 * @param toDetail
 */
export function genFlowEditBtn(record, toDetail): ActionItem {
  return {
    label: '编辑',
    disabled: [1, 2, 5].includes(record.currentState),
    onClick: toDetail.bind(null, record, FLOW_OP_TYPE.EDIT),
  }
}

/**
 * 流程-详情-按钮
 * @param record
 * @param toDetail
 */
export function genFlowDetailBtn(record, toDetail): ActionItem {
  return {
    label: '详情',
    //disabled: !record.currentState,
    onClick: toDetail.bind(null, record, FLOW_OP_TYPE.DETAIL),
  }
}

/**
 * 流程-删除-按钮
 * @param record
 * @param handleDelete
 */
export function genFlowDeleteBtn(record, handleDelete): ActionItem {
  return {
    label: '删除',
    color: 'error',
    disabled: [1, 2, 3, 5].includes(record.currentState),
    modelConfirm: {
      onOk: handleDelete.bind(null, record.id),
    },
  }
}
