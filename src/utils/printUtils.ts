import html2canvas from 'html2canvas'
import printStyle from "/@/design/printStyle";
import {isString} from "@vue/shared";


/**
 * 打印dom元素
 * @param dom dom节点，使用ref的情况，传入ref.value
 */
export function printHtml(dom: HTMLElement,docTitle:string = "") {
  console.log('printHtml docTitle',docTitle)
  const print = dom.innerHTML + printStyle;
  const iframe: any = document.createElement('IFRAME');
  iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');
  document.body.appendChild(iframe);
  let doc = iframe.contentWindow.document;
  iframe.onload = function () {
    if(isString(docTitle)) document.title = docTitle;
    iframe.contentWindow.print();
    document.body.removeChild(iframe);
  };
  doc.write(print);
  doc.close();
}

export function printCanvas(canvas: HTMLCanvasElement) {
  // 创建一个新的窗口来打印
  const printWindow = window.open('', '_blank');
  // 将画布添加到新窗口中
  printWindow!.document.body.appendChild(canvas);
  // 打印新窗口的内容
  printWindow!.print();
  // 关闭新窗口
  printWindow!.close();
}

/**
 * HTML元素转换为图片
 * @param dom dom节点，使用ref的情况，传入ref.value
 */
export function htmlToCanvas(dom:HTMLElement) {
  const style = document.createElement('style');
  document.head.appendChild(style);
  style.sheet?.insertRule('body > div:last-child img { display: inline-block; }');

  return html2canvas(dom, {
    scale: window.devicePixelRatio, // 确保渲染精确
  }).then(function(canvas) {
    style.remove();
    return canvas;
  });
}
