
export function isNil(value: any): boolean {
  return value === null || value === undefined;
}

export function trim(value: string): string {
  return isNil(value) ? '' : value.toString().trim();
}

/**
 * 复制文本到剪贴板 FIXME: not working
 */
export function copyText(text: string): void {
  if (trim(text) === '') {
    return;
  }
  const textarea = document.createElement('textarea');
  textarea.setAttribute('readonly', 'readonly');
  textarea.innerText = text;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand('copy');
  textarea.remove();
}

/**
 * 判断checkList是否全部在allList中
 * @param allList
 * @param checkList
 */
export function allInList(allList: any[], checkList: any[]): boolean {
  if (isNil(allList) || allList.length === 0) return false;
  if (isNil(checkList) || checkList.length === 0) return false;

  for (const check of checkList) {
    if (allList.includes(check)) {
      return false;
    }
  }
  return true;
}

export function stopPropagation(e: MouseEvent) {
  e.stopPropagation();
}

export function tryParseJSON(v: any, defaultJson = {}) {
  try {
    return JSON.parse(v)
  } catch (e) {
    console.error(e)
  }
  return defaultJson;
}
