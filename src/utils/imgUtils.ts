/**
 * 压缩base64图片的函数
 * example: let newBase64 = await compressImg(base64, 1, 1, "image/webp", 100, 120)
 *
 * @param {string} base64 - 图片的base64编码
 * @param {number} multiple - 缩放比例
 * @param {number} quality - 输出图片的质量（0.0到1.0之间）
 * @param {string} format - 输出图片的格式，默认为"image/webp" 可选参数 image/png、image/jpeg、image/webp 等
 * @param {number} min - 压缩后图片的最小KB大小
 * @param {number} max - 压缩后图片的最大KB大小
 * @returns {Promise<string>} - 返回压缩后的图片的base64编码
 */
export function compressImg(base64, multiple, quality, format = "image/webp", min, max) {
  return new Promise((resolve, reject) => {
    try {
      // 如果base64为空，则直接返回
      if (!base64) {
        resolve(base64);
        return;
      }
      // 如果是data:image/svg+xml;base64,开头的矢量图片，直接返回
      if (base64.indexOf('image/svg+xml') > -1) {
        resolve(base64);
        return;
      }
      // 创建一个新的Image对象
      let newImage = new Image();
      // 设置Image对象的src为传入的base64编码
      newImage.src = base64;
      // 设置crossOrigin属性为'Anonymous'，用于处理跨域图片
      newImage.setAttribute('crossOrigin', 'Anonymous');

      // 定义图片的宽度、高度和缩放比例
      let imgWidth, imgHeight, proportion;

      // 当图片加载完成后执行的操作
      newImage.onload = function() {
        // 计算缩放后的宽度
        proportion = this.width * multiple;
        // 记录原始宽度和高度
        imgWidth = this.width;
        imgHeight = this.height;

        // 创建一个canvas元素
        let canvas = document.createElement('canvas');
        // 获取canvas的2D渲染上下文
        let ctx = canvas.getContext('2d');

        // 设置canvas的宽高，按照等比例缩放
        // 等比例缩小放大
        canvas.width = proportion;
        canvas.height = proportion * (imgHeight / imgWidth);

        // 清除canvas上的内容
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 在canvas上绘制缩放后的图片
        ctx.drawImage(this, 0, 0, canvas.width, canvas.height);

        // 将canvas转换为base64编码的图片
        let smallBase64 = canvas.toDataURL(format, quality);

        // 如果设置了最小KB大小，并且压缩后的图片大小小于最小KB大小，则增加质量并重新压缩
        if (min && smallBase64.length / 1024 < min) {
          while (smallBase64.length / 1024 < min && quality < 1) {
            quality += 0.01;
            smallBase64 = canvas.toDataURL(format, quality);
          }
        }
        // 如果设置了最大KB大小，并且压缩后的图片大小大于最大KB大小，则减小质量并重新压缩
        else if (max && smallBase64.length / 1024 > max) {
          while (smallBase64.length / 1024 > max && quality > 0) {
            quality -= 0.01;
            smallBase64 = canvas.toDataURL(format, quality);
          }
        }

        // 将压缩后的图片的base64编码作为Promise的解决值返回
        resolve(smallBase64);
      };
    } catch (error:any) {
      reject(error)
      throw new Error('error', error);
    }
  });
}
