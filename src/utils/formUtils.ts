import { FormSchema } from '/@/components/Form';
import { RuleType } from 'ant-design-vue/lib/form/interface';
import type { ComponentType } from '/@/components/Form/src/types';
import dayjs from 'dayjs';
import { BOOL_OPTIONS, BOOLEAN_OPTIONS } from '/@/enums/zzEnums';
import { organizeTreeApi } from '/@/api';

export function genCommon(label: string, field: string, component: ComponentType, required = true, type: RuleType | 'array' = 'string'): FormSchema {
  return {
    field,
    label,
    component,
    componentProps: { placeholder: `请输入${label}` },
    rules: [{ required, message: `${label}不能为空`, type }],
  };
}

export function genInput(label: string, field: string, required = true, submitOnPressEnter = false): FormSchema {
  return {
    field,
    label,
    component: 'Input',
    componentProps: { placeholder: `请输入${label}`, submitOnPressEnter },
    rules: [{ required, trigger: 'blur', message: `${label}不能为空` }],
  };
}

export function genEditor(label: string, field: string, required = false, submitOnPressEnter = false): FormSchema {
  return {
    field,
    label,
    component: 'Editor',
    componentProps: { placeholder: `请输入${label}`, submitOnPressEnter },
    rules: [{ required, trigger: 'blur', message: `${label}不能为空` }],
  };
}

export function genTextarea(label: string, field: string, required = true, submitOnPressEnter = false): FormSchema {
  return {
    field,
    label,
    component: 'Textarea',
    componentProps: { placeholder: `请输入${label}`, submitOnPressEnter, autoSize: true },
    rules: [{ required, trigger: 'blur', message: `${label}不能为空` }],
  };
}

// 生成上传图片的表单项
export function genUploadImage(label: string, field: string, required = false): FormSchema {
  return {
    field,
    label,
    component: 'UploadImg',
    componentProps: {
      accept: 'image/*',
    },
    rules: [{ required, message: `${label}不能为空` }],
  };
}

export function genInputNumber(label: string, field: string, required = true, suffix = ''): FormSchema {
  return {
    field,
    label,
    component: 'Input',
    componentProps: { type: 'number', placeholder: `请输入${label}`, suffix },
    rules: [{ required, trigger: 'blur', message: `${label}不能为空` }],
  };
}

export function genSelect(label: string, field: string, required = true, type: RuleType | 'array' = 'string', options: any[] = []): FormSchema {
  return {
    field,
    label,
    component: 'Select',
    componentProps: { placeholder: `请选择${label}`, options },
    rules: [{ required, type, trigger: 'change', message: `${label}不能为空` }],
  };
}

export function genUser(label: string, field: string, required = true, type: RuleType | 'array' = 'string', componentProps: any = {}): FormSchema {
  return {
    field,
    label,
    component: 'UserSelect',
    componentProps: { placeholder: `请选择${label}`, ...componentProps },
    rules: [{ required, type, trigger: 'change', message: `${label}不能为空` }],
  };
}

export function genUsers(label: string, field: string, required = true): FormSchema {
  return {
    field,
    label,
    component: 'UsersSelect',
    componentProps: { placeholder: `请选择${label}` },
    rules: [{ required, trigger: 'change', message: `${label}不能为空` }],
  };
}

/**
 * 选择部门
 * @param label
 * @param field
 * @param required
 * @param type
 */
export function genDeptCascade(label: string, field: string, required = true, type: RuleType = 'string', componentProps: any = {}): FormSchema {
  return {
    field,
    label,
    component: 'FaCascader',
    componentProps: { placeholder: `请选择${label}`, api: organizeTreeApi, showRoot: false, ...componentProps },
    rules: [{ required, type, trigger: 'change', message: `${label}不能为空` }],
  };
}

export function genDept(label: string, field: string, required = true, type: RuleType = 'string'): FormSchema {
  return {
    field,
    label,
    component: 'DepSelect',
    componentProps: { placeholder: `请选择${label}` },
    rules: [{ required, type, trigger: 'change', message: `${label}不能为空` }],
  };
}

export function genSelectBoolInt(label: string, field: string, required = true, type: RuleType = 'string'): FormSchema {
  return {
    field,
    label,
    component: 'Select',
    componentProps: { placeholder: `请选择${label}`, options: BOOLEAN_OPTIONS },
    rules: [{ required, type, trigger: 'change', message: `${label}不能为空` }],
  };
}

export function genSelectBool(label: string, field: string, required = true, type: RuleType = 'boolean'): FormSchema {
  return {
    field,
    label,
    component: 'Select',
    componentProps: { placeholder: `请选择${label}`, options: BOOL_OPTIONS },
    rules: [{ required, type, trigger: 'change', message: `${label}不能为空` }],
  };
}

export function genSelectMulti(label: string, field: string, required = true, options: any[] = []): FormSchema {
  return {
    field,
    label,
    component: 'Select',
    componentProps: { placeholder: `请选择${label}`, options, multiple: true },
    rules: [{ required, type: 'array', trigger: 'change', message: `${label}不能为空` }],
  };
}

/**
 * 使用系统数据字典作为下拉选择
 * @param label
 * @param field
 * @param required
 * @param type
 * @param options
 */
export function genDictSelect(label: string, field: string, required = true, dictName: string): FormSchema {
  return {
    field,
    label,
    component: 'FaDictSelect',
    componentProps: { placeholder: `请选择${label}`, dictName },
    rules: [{ required, trigger: 'change', message: `${label}不能为空` }],
  };
}

export function genDate(label: string, field: string, required = true): FormSchema {
  return {
    field,
    label,
    component: 'DatePicker',
    componentProps: { format: 'YYYY-MM-DD' },
    rules: [{ required, message: `${label}不能为空` }],
  };
}

export function genDateTime(label: string, field: string, required = true): FormSchema {
  return {
    field,
    label,
    component: 'DatePicker',
    componentProps: { format: 'YYYY-MM-DD HH:mm:ss' },
    rules: [{ required, message: `${label}不能为空` }],
  };
}

export function genTime(label: string, field: string): FormSchema {
  return {
    field,
    label,
    component: 'TimePicker',
    componentProps: {
      format: 'HH:mm:ss',
      placeholder: '选择时间',
    },
  }
}

export function parseDate(value: string) {
  return dayjs(value);
}

export function now(format = 'YYYY-MM-DD') {
  return dayjs().format(format);
}

export function nowTimestamp() {
  return dayjs().unix() * 1000;
}

export function formatPercentage(divisor, dividend) {
  const percentage = ((divisor / dividend) * 100).toFixed(1);
  return `${percentage}%`;
}
