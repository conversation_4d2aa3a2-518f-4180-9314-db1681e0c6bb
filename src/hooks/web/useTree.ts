import { computed, onMounted, ref } from 'vue';
import { FaTreeUtils } from '/@/utils/tree';

export const Constant = {
  /** 约定：tree结构数据，根结点的ID默认为0 */
  TREE_SUPER_ROOT_ID: 0,
  TREE_SUPER_ROOT_LABEL: '根节点',
};

export const ROOT_DEFAULT = {
  id: Constant.TREE_SUPER_ROOT_ID,
  name: Constant.TREE_SUPER_ROOT_LABEL,
  parentId: -1,
  sort: 0,
  sourceData: undefined,
  isLeaf: false,
  hasChildren: true,
};

interface Options {
  rootId?: number | string;
  rootName?: string;
  showRoot?: boolean;
  disabledIds?: any[]; // 禁止选择的选项IDs
  maxLevel?: number; // 最大的展示层级，超过这个层级不展示
}

export function useTree(api: any, {rootId = '0' as any, rootName = '根节点', showRoot, disabledIds, maxLevel}: Options) {
  const treeData = ref<any[]>([]);

  function fetchData() {
    api.allTree({level: maxLevel}).then((res) => {
      let treeArr = res.data;
      if (showRoot) {
        treeArr = [{...ROOT_DEFAULT, id: rootId, name: rootName, level: 0, children: res.data} as any];
      }
      // TODO tree disabledIds
      // setTreeDisabled(treeArr, disabledIds);
      treeData.value = treeArr;
    });
  }

  const options = computed(() => {
    // console.log('treeData.value', treeData.value);
    FaTreeUtils.setTreeDisabled(treeData.value, disabledIds);
    return treeData.value;
  });

  onMounted(() => {
    console.log('useTree.ts onMounted')
    fetchData();
  });
  return {options};
}
