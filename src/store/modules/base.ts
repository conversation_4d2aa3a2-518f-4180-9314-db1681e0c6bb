import { defineStore } from 'pinia';
import { getDictionaryAll } from '/@/api/systemData/dictionary';
interface DicChildItem {
  isTree?: number;
  id: string;
  enCode: string;
  fullName: string;
}
interface DicItem extends DicChildItem {
  dictionaryList: DicChildItem[];
}
interface BaseState {
  dictionaryList: DicItem[];
}

// 定义格式化后的字典项接口
interface FormattedDicItem {
  fullName: string;
  id: string;
}

export const useBaseStore = defineStore({
  id: 'app-base',
  state: (): BaseState => ({
    dictionaryList: [],
  }),
  getters: {
    getDicList(): DicItem[] {
      return this.dictionaryList;
    },
  },
  actions: {
    resetState() {
      this.dictionaryList = [];
    },
    setDictionaryList(list: DicItem[] = []) {
      this.dictionaryList = list;
    },
    async getDictionaryAll(): Promise<DicItem[]> {
      try {
        if (this.dictionaryList.length) {
          return this.dictionaryList;
        } else {
          const res = await getDictionaryAll();
          if (!res) return [];
          this.dictionaryList = res.data.list;
          return res.data.list;
        }
      } catch (error) {
        return [];
      }
    },
    async getDictionaryData(encode: string, id: string = ''): Promise<DicChildItem[] | DicChildItem> {
      try {
        let list: DicItem[] = [],
          data: Partial<DicItem> = {},
          json: DicChildItem[] | DicChildItem = [];
        if (!this.dictionaryList.length) {
          list = await this.getDictionaryAll();
        } else {
          list = this.dictionaryList;
        }
        if (encode) {
          let arr = list.filter(o => o.enCode === encode);
          if (!arr.length) return [];
          data = arr[0];
          if (!id) {
            json = data.dictionaryList as DicChildItem[];
          } else {
            let rowData: DicChildItem[] = [];
            if (!data.isTree) {
              rowData = (data.dictionaryList as DicChildItem[]).filter(o => o.id === id);
            } else {
              function findData(list) {
                for (let i = 0; i < list.length; i++) {
                  const e = list[i];
                  if (e.id === id) {
                    rowData[0] = e;
                    break;
                  }
                  if (e.children && e.children.length) {
                    findData(e.children);
                  }
                }
              }
              findData(data.dictionaryList);
            }
            if (rowData.length) {
              json = rowData[0];
            } else {
              json = {
                id: '',
                fullName: '',
                enCode: '',
              };
            }
          }
          return json;
        }
        return json;
      } catch (error) {
        return [];
      }
    },
    /**
     * 获取格式化后的字典数据，将enCode作为id返回
     * @param encode 字典编码
     * @returns 格式化后的字典数据数组 { fullName: string, id: string }[]
     */
    async getFormattedDictionaryData(encode: string): Promise<FormattedDicItem[]> {
      try {
        const dicData = await this.getDictionaryData(encode);
        if (Array.isArray(dicData) && dicData.length) {
          return dicData.map(item => ({
            fullName: item.fullName,
            id: item.enCode // 使用enCode作为id
          }));
        }
        return [];
      } catch (error) {
        console.error('获取格式化字典数据失败', error);
        return [];
      }
    },
    async getDicDataSelector(value: string, key: string = 'id'): Promise<DicChildItem[]> {
      try {
        let list: DicItem[] = [],
          data: Partial<DicItem> = {},
          json: DicChildItem[] = [];
        if (!this.dictionaryList.length) {
          list = await this.getDictionaryAll();
        } else {
          list = this.dictionaryList;
        }
        if (!value) return [];
        let arr = list.filter(o => o[key] === value);
        if (!arr.length) return [];
        data = arr[0];
        json = data.dictionaryList as DicChildItem[];
        return json;
      } catch (error) {
        return [];
      }
    },
  },
});
