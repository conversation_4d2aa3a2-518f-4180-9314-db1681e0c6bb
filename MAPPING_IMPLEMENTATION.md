# 人员列表字段映射实现说明

## 概述
本次实现解决了人员列表中性别、民族、学历、部门、岗位等字段只显示ID而不显示对应名称的问题，同时添加了时间字段的格式化功能。

## 实现的功能

### 1. 字段映射
- **性别 (gender)**: 静态映射 - 1:男, 2:女, 3:保密
- **民族 (nation)**: 数据字典映射 - dictionaryType: 'b6cd65a763fa45eb9fe98e5057693e40'
- **学历 (education)**: 数据字典映射 - dictionaryType: '6a6d6fb541b742fbae7e8888528baa16'
- **部门 (organizeId)**: 部门API映射
- **岗位 (positionId)**: 岗位API映射

### 2. 时间字段格式化
- **培训时间 (trainingTime)**
- **生日 (birthday)**
- **进入项目时间 (goProjectTime)**
- **毕业时间 (graduationTime)**
- **参加工作时间 (joinWorkTime)**
- **进入五公司时间 (joinFiveFirmTime)**
- **撤场时间 (removeTime)**

## 技术实现

### 1. 数据获取和缓存
在 `src/views/masterListUser/index.vue` 中：
- 添加了映射数据的缓存状态
- 实现了 `loadMappingData()` 函数来预加载所有映射数据
- 利用 Pinia store 管理数据字典数据

### 2. 数据转换
在 `afterFetch` 回调中：
- 使用 `dynamicText()` 函数处理普通字段映射
- 使用 `dynamicTreeText()` 函数处理树形数据映射
- 使用 `dayjs` 进行时间格式化

### 3. 列配置修改
在 `src/views/masterListUser/helper/columnList.ts` 中：
- 将需要映射的字段的 `prop` 属性改为映射后的字段名
- 例如：`gender` → `genderText`, `nation` → `nationText`

## 性能优化

### 1. 缓存机制
- 映射数据在组件初始化时一次性加载
- 避免每次渲染时重复请求API
- 利用现有的 Pinia store 缓存数据字典

### 2. 错误处理
- 添加了时间格式化的错误处理
- 对空值进行了适当的处理
- 添加了控制台警告信息

## 文件修改清单

### 主要文件
1. `src/views/masterListUser/index.vue`
   - 添加了映射数据获取逻辑
   - 实现了数据转换功能
   - 添加了时间格式化

2. `src/views/masterListUser/helper/columnList.ts`
   - 修改了所有需要映射字段的 `prop` 属性
   - 确保表格显示映射后的文本

### 依赖导入
- 添加了部门和岗位API的导入
- 添加了 Pinia store 的导入
- 添加了工具函数的导入
- 添加了 dayjs 时间处理库

## 使用的工具函数

### 1. dynamicText(value, options)
用于处理普通的ID到名称的映射转换

### 2. dynamicTreeText(value, options)
用于处理树形结构数据的映射转换

### 3. dayjs
用于时间格式化，统一格式为 'YYYY-MM-DD'

## 效果
实现后，列表中将显示：
- 性别：显示"男"、"女"、"保密"而不是1、2、3
- 民族：显示具体民族名称而不是ID
- 学历：显示具体学历名称而不是ID
- 部门：显示部门名称而不是部门ID
- 岗位：显示岗位名称而不是岗位ID
- 时间：显示格式化的日期（YYYY-MM-DD）而不是时间戳

## H5页面映射实现

### 文件：src/views/h5/out/UserInfoUpdate.vue

#### 新增功能
1. **部门映射**：将 organizeId 映射为 organizeName
2. **岗位映射**：将 positionId 映射为 positionName
3. **岗位序列映射**：将 positionSequence ID 映射为对应名称
4. **岗位序列字段改为只读**：从下拉选择改为只读输入框

#### 实现方式
- 添加了 `mapDepartmentAndPosition()` 函数处理映射逻辑
- 在用户数据获取后自动进行映射转换
- 在组件挂载时预加载部门、岗位和岗位序列数据
- 添加了错误处理和默认值处理
- 岗位序列字段改为只读，不再包含在提交数据中

#### 技术细节
- 使用 `dynamicTreeText()` 函数处理树形数据映射
- 支持单个ID和数组ID的处理
- 映射失败时显示默认值（"未知部门"、"未知岗位"）

## 注意事项
1. 确保数据字典ID正确配置
2. 部门和岗位API需要返回正确的数据结构
3. 时间字段需要是有效的日期格式
4. 如果映射数据加载失败，会在控制台显示错误信息
5. H5页面的部门和岗位字段为只读，映射后的名称会自动显示
