# message消息弹框

## basic
```typescript
import { useMessage } from "/@/hooks/web/useMessage";
const { createMessage } = useMessage();

createMessage.success(res.msg);
```

## confirm modal
```typescript
import { useMessage } from "/@/hooks/web/useMessage";
const { createConfirm } = useMessage();
createConfirm({
  iconType: 'warning',
  title: 'title',
  content: '确定要xxx吗?',
  onOk: () => {
  },
});
```

# 自定义业务消息弹框代码开发说明
需要配置代码路径
1. 顶部站内消息侧滑组件：`src/layouts/default/header/components/MessageDrawer.vue`
2. 站内消息页面：`src/views/basic/messageRecord/index.vue`
3. APP：'pages\message\message\index.vue'
