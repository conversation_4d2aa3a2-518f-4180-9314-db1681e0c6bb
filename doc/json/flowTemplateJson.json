{"type": "start", "nodeId": "2f10d3", "content": "所有人", "properties": {"titleType": 0, "isComment": false, "approveMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 3}, "endMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 3}, "initiator": ["537554313522708741--department", "537554115836772613--department", "537543711358189829--department", "537543616688554245--department", "537543506265112837--department"], "errorRule": 1, "endFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "submitBtnText": "提交", "title": "流程发起", "rejectMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 3}, "isCustomCopy": false, "overTimeMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 3}, "hasPressBtn": true, "hasRevokeBtn": true, "defaultContent": "{发起用户名}的{流程名称}", "printId": ["553126468872307397"], "copyMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 3}, "pressBtnText": "催办", "formName": "病假申请", "saveBtnText": "暂存", "hasOpinion": true, "initFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "flowRecallFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "noticeFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "errorRuleUser": [], "printBtnText": "打印", "formOperates": [{"read": true, "jnpfKey": "uploadImg", "dataType": "array", "name": "图片上传", "id": "uploadImgField102", "requiredDisabled": false, "write": true, "required": false}], "noticeConfig": {"overNotice": false, "overEventTime": 5, "overEvent": false, "on": 0, "firstOver": 1, "overTimeDuring": 2}, "isBatchApproval": false, "hasSubmitBtn": true, "formId": "538351617364199173", "waitMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 3}, "hasSaveBtn": true, "overTimeConfig": {"overNotice": false, "overEventTime": 5, "overAutoApproveTime": 5, "overEvent": false, "on": 0, "firstOver": 0, "overTimeDuring": 2, "overAutoApprove": false}, "funcConfigRule": 0, "revokeBtnText": "撤回", "hasPrintBtn": true, "summaryType": 0, "extraCopyRule": 1, "isSummary": false, "timeLimitConfig": {"duringDeal": 24, "nodeLimit": 0, "formField": "", "on": 0}, "noticeMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 3}, "titleContent": "", "formFieldList": [{"folder": "", "__config__": {"jnpfKey": "uploadImg", "label": "图片上传", "required": false}, "fileSize": 10, "isAccount": 0, "limit": 9, "__vModel__": "uploadImgField102", "fullName": "图片上传", "sizeUnit": "MB", "tipText": "", "disabled": false, "id": "uploadImgField102", "pathType": "defaultPath"}], "overTimeFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "circulateUser": [], "hasSign": true}, "childNode": {"prevId": "2f10d3", "type": "approver", "nodeId": "3ce25e", "content": "请设置审批人", "properties": {"hasFreeApproverBtnText": "加签", "rejectMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 2}, "overTimeMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 2}, "rejectFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "printId": ["553126468872307397"], "countersignRatio": 100, "rejectBtnText": "退回", "saveBtnText": "暂存", "hasOpinion": true, "assigneeType": 6, "formOperates": [{"read": true, "jnpfKey": "uploadImg", "dataType": "array", "name": "图片上传", "id": "uploadImgField102", "requiredDisabled": false, "write": false, "required": false}], "printBtnText": "打印", "agreeRules": [], "formId": "538351617364199173", "extraCopyRule": 1, "hasRejectBtn": true, "hasAgreeRule": false, "noticeMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 2}, "recallFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "assignList": [{"ruleList": [{"parentField": "uploadImgField102", "childField": "uploadImgField102", "childFieldOptions": []}], "formFieldList": [{"folder": "", "__config__": {"jnpfKey": "uploadImg", "label": "图片上传", "required": false}, "fileSize": 10, "isAccount": 0, "limit": 9, "__vModel__": "uploadImgField102", "fullName": "图片上传", "sizeUnit": "MB", "tipText": "", "disabled": false, "id": "uploadImgField102", "pathType": "defaultPath"}], "title": "流程发起", "nodeId": "2f10d3"}], "counterSign": 0, "rejectType": 1, "hasAuditBtn": true, "userType": "role", "overTimeFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "circulateUser": [], "nodeId": "", "formFieldType": 1, "approveMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 2}, "approvers": ["537553221527273733--user"], "description": "", "extraRule": 1, "managerLevel": 1, "rejectStep": "0", "title": "审批节点3ce25e", "isCustomCopy": false, "transferBtnText": "转审", "hasTransferBtn": true, "prevNodeList": [], "hasRevokeBtn": true, "isInitiatorCopy": false, "formName": "病假申请", "copyMsgConfig": {"msgName": "", "msgId": "", "templateJson": [], "on": 2}, "noticeFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "hasFreeApproverBtn": false, "noticeConfig": {"overNotice": false, "overEventTime": 5, "overEvent": false, "on": 2, "firstOver": 1, "overTimeDuring": 2}, "hasSaveBtn": false, "overTimeConfig": {"overNotice": false, "overEventTime": 5, "overAutoApproveTime": 5, "overEvent": false, "on": 2, "firstOver": 0, "overTimeDuring": 2, "overAutoApprove": false}, "getUserUrl": "", "revokeBtnText": "撤回", "hasPrintBtn": true, "auditBtnText": "通过", "formField": "", "timeLimitConfig": {"duringDeal": 24, "nodeLimit": 0, "formField": "", "on": 2}, "noApproverHandler": true, "approveFuncConfig": {"interfaceId": "", "interfaceName": "", "templateJson": [], "on": false}, "progress": "50", "formFieldList": [{"folder": "", "__config__": {"jnpfKey": "uploadImg", "label": "图片上传", "required": false}, "fileSize": 10, "isAccount": 0, "limit": 9, "__vModel__": "uploadImgField102", "fullName": "图片上传", "sizeUnit": "MB", "tipText": "", "disabled": false, "id": "uploadImgField102", "pathType": "defaultPath"}], "departmentLevel": 1, "hasSign": true}}}