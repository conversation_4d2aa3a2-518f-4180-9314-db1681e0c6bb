# popup 弹框

## 基础用法
### 定义Popup组件
```vue
<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit">
    <div>hello 弹框内部组件</div>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { BasicForm, FormSchema, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';

const id = ref('');

const getTitle = computed(() => (!unref(id) ? '新建App' : '编辑App'));
const emit = defineEmits(['register', 'reload']);
const {createMessage} = useMessage();
const [registerForm, {setFieldsValue, validate, resetFields, updateSchema, clearValidate}] = useForm({labelWidth: 100, schemas: schemas});
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);

async function init(data) {
}
</script>

```

### 使用Popup组件
```vue
<template>
    <Form @register="registerForm" @reload="reload"/>
</template>

<script lang="ts" setup>
import Form from "./Form.vue";
import {usePopup} from "/@/components/Popup";

const [registerForm, {openPopup: openForm}] = usePopup();
</script>
```

## 全屏弹框
```vue
```
