<mxfile host="app.diagrams.net" modified="2024-04-15T09:49:29.637Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="WGb56utbQ_UsmcJMPt1J" version="24.2.5" type="device">
  <diagram id="Bc65p5hNepcSEBBccv43" name="Page-1">
    <mxGraphModel dx="1221" dy="618" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="2" target="3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5" value="审批通过：" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="4" vertex="1" connectable="0">
          <mxGeometry x="-0.1846" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2" value="人员信息申请表" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3" value="用户参与培训记录表" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="7" target="3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="15" value="一对多：一个用户有多个培训记录表" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="13" vertex="1" connectable="0">
          <mxGeometry x="0.0815" relative="1" as="geometry">
            <mxPoint y="15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="Fw7FQY3ye5pknCCkqtIa-23">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="720" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7" value="用户表" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="470" y="430" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8" value="1. 用户A -&amp;gt; 申请表 -&amp;gt; 录入培训名单（还没有关联培训计划）&lt;br&gt;2. 用户可以多次参加培训&amp;nbsp;&lt;br&gt;3. 用户A：&lt;br&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;1. 2024-01-01 一级培训 通过&lt;br&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;2. 2024-02-01 一级培训 培训中&lt;br&gt;4. 用户培训记录-关联-培训计划。&lt;br&gt;5. 管理员需要先在后台创建培训计划&lt;br&gt;6. 培训名单管理：可以修改用户参加培训计划，也可以手动添加&lt;br&gt;&lt;br&gt;1. 用户首先在系统创建账户，则可以登录系统。" style="shape=note;strokeWidth=2;fontSize=14;size=20;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontColor=#666600;align=left;" parent="1" vertex="1">
          <mxGeometry x="890" y="310" width="350" height="230" as="geometry" />
        </mxCell>
        <mxCell id="16" value="新用户A" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="560" y="430" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Fw7FQY3ye5pknCCkqtIa-16">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="730" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="Fw7FQY3ye5pknCCkqtIa-16" target="Fw7FQY3ye5pknCCkqtIa-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="Fw7FQY3ye5pknCCkqtIa-16" target="3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-16" value="培训计划表" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="440" y="210" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-23" value="考试成绩表" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="670" y="340" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Fw7FQY3ye5pknCCkqtIa-26">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="820" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-26" value="试卷表" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="670" y="140" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-29" value="试卷试题中间表" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="820" y="140" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="Fw7FQY3ye5pknCCkqtIa-30" target="Fw7FQY3ye5pknCCkqtIa-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fw7FQY3ye5pknCCkqtIa-30" value="试题表" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="820" y="250" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
