import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/base/ext/zzProjectMedia',
}

// 分页
export function page(data) {
  return defHttp.post({ url: Api.Prefix + '/page', data });
}

// 删除
export function remove(id) {
  return defHttp.delete({ url: Api.Prefix + '/remove/' + id });
}

// 查询
export function getById(id) {
  return defHttp.get({ url: Api.Prefix + '/getById/' + id });
}

// 批量查询
export function getByIds(ids) {
  return defHttp.post({ url: Api.Prefix + '/getByIds/', data: ids });
}

// 新建
export function save(data) {
  return defHttp.post({ url: Api.Prefix + '/save', data });
}

// 修改
export function update(data) {
  return defHttp.post({ url: Api.Prefix + '/update', data });
}
