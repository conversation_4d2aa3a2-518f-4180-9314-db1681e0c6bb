<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新建</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import * as zzProjectMediaApi from '/@/api/extend/zzProjectMedia';
import { useMessage } from '/@/hooks/web/useMessage';
import { usePopup } from '/@/components/Popup';
import Form from './Form.vue'

defineOptions({ name: 'extend-zz-project-media' });

const { t } = useI18n();
const { createMessage } = useMessage();
const columns: BasicColumn[] = [
  { title: '照片题名', dataIndex: 'title' },
];
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerTable, { reload }] = useTable({
  api: zzProjectMediaApi.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      {
        field: 'keyword',
        label: t('common.keyword'),
        component: 'Input',
        componentProps: {
          placeholder: t('common.enterKeyword'),
          submitOnPressEnter: true,
        },
      },
    ],
  },
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record:any): ActionItem[] {
  return [
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id),
    },
    {
      label: t('common.delText'),
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}

function handleDelete(id:any) {
  zzProjectMediaApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
function addOrUpdateHandle(id = '') {
  openFormPopup(true, { id });
}
</script>
