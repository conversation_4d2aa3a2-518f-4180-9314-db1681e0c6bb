<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit">
    <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange" class="!px-10px !mt-10px">
    </BasicForm>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { BasicForm, FormSchema, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import * as zzProjectMedia from "/@/api/extend/zzProjectMedia";

const id = ref('');

const schemas: FormSchema[] = [
  {
    field: 'title',
    label: '照片题名',
    component: 'Input',
    componentProps: {},
    rules: [{required: true, trigger: 'blur', message: '照片题名不能为空'}],
  },
];
const getTitle = computed(() => (!unref(id) ? '新建工程影像' : '编辑工程影像'));
const emit = defineEmits(['register', 'reload']);
const {createMessage} = useMessage();
const [registerForm, {setFieldsValue, validate, resetFields}] = useForm({labelWidth: 100, schemas: schemas});
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);

async function init(data:any) {
  resetFields();
  id.value = data.id;
  setFieldsValue({
    fileId: [],
  });
  if (id.value) {
    changeLoading(true);
    zzProjectMedia.getById(id.value).then(res => {
      const data = {
        ...res.data,
      };
      setFieldsValue(data);
      changeLoading(false);
    });
  }
}

function handleFieldValueChange(field:any, value:any) {
  console.log('field', field, 'value', value);
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
    fileId: JSON.stringify(values.fileId),
  };
  const formMethod = id.value ? zzProjectMedia.update : zzProjectMedia.save;
  formMethod(query)
    .then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closePopup();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
