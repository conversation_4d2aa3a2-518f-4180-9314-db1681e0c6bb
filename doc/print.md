# print打印
使用插件`printjs`

https://printjs.crabbly.com/#documentation

# 注意
## 打印样式错误问题
需要将vue中使用到的样式，复制到`src/design/printStyle.ts`文件中，否则打印的内容会丢失样式。

## 打印PDF
```html
<button type="button" onclick="printJS('docs/printjs.pdf')">
  Print PDF
</button>
```

## 打印HTML
```html
 <form method="post" action="#" id="printJS-form">
    ...
 </form>

 <button type="button" onclick="printJS('printJS-form', 'html')">
    Print Form
 </button>
```

## 打印Image
```html
<img src="images/print-01.jpg" />

printJS('images/print-01-highres.jpg', 'image')

printJS({
  printable: ['images/print-01-highres.jpg', 'images/print-02-highres.jpg', 'images/print-03-highres.jpg'],
  type: 'image',
  header: 'Multiple Images',
  imageStyle: 'width:50%;margin-bottom:20px;'
})
```

## html2canvas
[https://blog.csdn.net/smouns_/article/details/132226237](利用html2canvas下载图片(全前端处理))

```typescript
<div id="card" />
  
import { htmlToCanvas } from "/@/utils/printUtils";

htmlToCanvas(document.querySelector("#card")!)
```
