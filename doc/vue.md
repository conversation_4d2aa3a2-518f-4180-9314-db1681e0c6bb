# vue

## 组件属性定义
1. 定义属性
```typescript
const props = defineProps(['paperId']); // 暴露给外部传入的属性
console.log('props', props.paperId)
```

2. 外部组件传入属性值
```vue
<QuestionConfigFormNew :paper-id="123"/>
```

## 定义触发事件
```typescript
const emit = defineEmits(['change']);

function onChange(value) {
  emit('change', value);
}
```

## 组件暴露方法定义
1. 定义方法
```typescript
defineExpose({init}); // 暴露给外部调用的方法
```

2. 外部组件调用方法
```vue
<template>
  <ComponentX ref />
</template>

<script type="ts" setup>
  const componentRef = ref<any>(null); // ref
  
  
</script>
```

# 常见问题
1. [vue3 父组件数组更新，子组件没变化问题解决](https://blog.csdn.net/ejunda/article/details/141261318)
