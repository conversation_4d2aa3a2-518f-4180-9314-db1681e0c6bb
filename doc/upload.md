# upload文件上传

## 七牛云直传

参考代码：`src/views/extend/formDemo/fieldForm3/index.vue`

演示页面：http://localhost:3100/extend/formDemo/fieldForm3

### 七牛云上传代码示例
```typescript
function customRequest(req: any) {
  // console.log('file', req)
  const {file, onError, onProgress, onSuccess} = req;
  // setLoading(true);
  fetchUploadFileQiniu(
    file,
    props.prefix,
    file.name,
    (path, res) => {
      console.log('UploadFileQiniu.vue#onSuccess', path, res)
      // if (onChange) onChange(path);
      onSuccess({...res, path}, file);

      // save file record by url
      fileSaveApi.uploadFromUrlQiniu(path).then(res => {
        fileList.value.push({
          fileId: res.data.id,
          name: file.name,
          url: path,
          fileSize: file.size,
        });
        emit('update:value', unref(fileList));
        emit('change', unref(fileList));
        formItemContext.onFieldChange();
      })
    },
    (res) => {
      const {percent} = res.total;
      onProgress({percent}, file);
      percentRef.value = tryToFixed(percent, 0);
    },
    (res) => {
      onError(new Error(res), file);
    }
  );
}
```

### 走本地服务器中转上传七牛云上传代码示例
```typescript
function customRequest(req: any) {
  // console.log('file', req)
  const {file, onError, onProgress, onSuccess} = req;
  // setLoading(true);
  fetchUploadFileLocal(
    file,
    props.prefix,
    file.name,
    (path, res) => {
      console.log('UploadFileQiniu.vue#onSuccess', path, res)
      // if (onChange) onChange(path);
      onSuccess({...res, path}, file);

      fileList.value.push({
        fileId: res.id,
        name: file.name,
        url: path,
        fileSize: file.size,
      });
      emit('update:value', unref(fileList));
      emit('change', unref(fileList));
      formItemContext.onFieldChange();
    },
    (res) => {
      const percent = res.progress * 100;
      onProgress({percent}, file);
      percentRef.value = tryToFixed(percent, 0);
    },
    (res) => {
      onError(new Error(res), file);
    }
  );
}
```
