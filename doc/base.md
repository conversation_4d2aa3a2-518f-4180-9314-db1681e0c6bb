# base系统框架

## 使用的组件

| 项目             | 分支        | 说明 |
|----------------|-----------| --- |
| vue            | vue       | https://cn.vuejs.org/ |
| ant-design-vue | UI组件库     | https://3x.antdv.com/docs/vue/changelog-cn |
| qiniu-js       | 七牛云JS SDK | https://developer.qiniu.com/kodo/1283/javascript |
| vuedraggable   | 组件拖动库     | https://github.com/SortableJS/Vue.Draggable |
| datav          | 可视化组件     | https://datav-vue3.netlify.app/ |

## 自定义组件并全局注册

1. 定义组件参考：`src/components/Fa/common`
2. 全局注册组件：`src/components/registerGlobComp.ts`
3. 组件一定要定义名称：`defineOptions({ name: 'FaDictShow' });`

## code block
```typescript
changeLoading(true)
changeLoading(false)
.catch(() => changeLoading(false));
createMessage.success(res.msg)
```
