# modal弹框

## 简单提示弹框
### 信息提示框
```typescript
import { useMessage } from '/@/hooks/web/useMessage';

const {createMessage} = useMessage();

createMessage.success('success');
createMessage.error('error info');
```

### 确认弹框
```typescript
const {createConfirm} = useMessage();

createConfirm({
  iconType: 'info',
  title: '提示',
  content: '首次登录，请修改初始密码。',
  onOk: () => {
    router.replace('/resetPwd')
  },
  cancelButtonProps: { style: {display: 'none'} },
});
```

#### 使用async
```typescript
const {simpleConfirm} = useMessage();

async function generate(id: any) {
  await simpleConfirm('生成证件会生成证件编号，是否确认？')
  openGenModal(true, {ids: [id]}); // 打开弹窗
}
```

## modal简单实现代码
### modal组件定义
```vue
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit">
    <div>hello 弹框内部组件</div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';

const emit = defineEmits(['register', 'reload']); // 需要暴露的事件
const [registerModal, { changeLoading, closeModal, changeOkLoading }] = useModalInner(init);

function init(data:any) {
  // 打印外部传给modal的参数
  console.log('init', data)
  changeLoading(true)
  setTimeout(() => {
    changeLoading(false)
  }, 1000)
}

async function handleSubmit() {
  changeOkLoading(true)
  setTimeout(() => {
    changeOkLoading(false)
    closeModal();
    emit('reload'); // 发布reload事件，外部组件接受此事件
  }, 1000)
}
</script>
```

### 使用modal的组件
```vue
<template>
  <!-- 添加modal组件 -->
  <Form @register="registerForm" @reload="reloadTable" />
</template>

<script lang="ts" setup>
import { useModal } from '/@/components/Modal'; // 弹框帮助类
import Form from './Form.vue'; // 组件的文件地址引入

const [registerForm, { openModal: openFormModal }] = useModal(); // 使用registerForm注册弹框，openFormModal打开弹框

/** 新增or更新方法 */
function addOrUpdateHandle(id = '') {
  openFormModal(true, { id }); // 打开弹窗
}
</script>
```

## modal高级用法
### 全屏弹框
```vue
<template>
  <BasicModal
    defaultFullscreen class="jnpf-full-modal full-modal"
  >
  </BasicModal>
</template>
```
