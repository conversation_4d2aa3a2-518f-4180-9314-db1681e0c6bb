# form表单

## FormItem各类型定义

[参考代码](../src/views/extend/formDemo/fieldForm1/schemaData.tsx)

```typescript
const schemas: FormSchema[] = [
  // input
  {
    field: 'title',
    label: '照片题名',
    component: 'Input',
    componentProps: {},
    rules: [{required: true, trigger: 'blur', message: '照片题名不能为空'}],
  },
  // datepicker show-time
  {
    field: 'makeDate',
    label: '拍摄时间',
    component: 'DatePicker',
    componentProps: { format: 'YYYY-MM-DD HH:mm:ss' },
    rules: [{required: true, trigger: 'blur', message: '拍摄时间不能为空'}],
  },
  // upload
  {
    field: 'fileId',
    label: '影像文件',
    component: 'FaUploadFileQiniu',
    componentProps: {
      // onSuccess: (file:any) => { setFieldsValue({ title: file.originalFilename }) }
    },
    rules: [{required: true, message: '影像文件不能为空'}],
  },
  {
    field: 'fieldSelect',
    label: '下拉选择',
    component: 'Select',
    componentProps: {
      options: [
        { fullName: '启用', id: 1 },
        { fullName: '锁定', id: 2 },
        { fullName: '禁用', id: 0 },
      ],
    },
  },
]

const [registerSearchForm, { updateSchema, resetFields, submit: searchFormSubmit }] = useForm({
  baseColProps: { span: 6 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
});
```

## 简单表单

```vue
```

### 设置表单字段
1. 关于时间类型字段，如果返回YYYY-MM-DD格式的字符串，则需要转换为unix时间戳；如果返回的已经是unix时间戳，则不需要转换；

```typescript
setFieldsValue({
  ...res.data,
  planStartTime: res.data.planStartTime ? dayjs(res.data.planStartTime, 'YYYY-MM-DD').valueOf() : undefined,
  planEndTime: res.data.planEndTime ? dayjs(res.data.planEndTime, 'YYYY-MM-DD').valueOf() : undefined,
});
```

## 自定义组件
### 触发表单Item值value更新
参考代码：[FaCascader.vue](../src/components/Jnpf/Cascader/src/FaCascader.vue)

```typescript
import {Form} from 'ant-design-vue';

# 注入Form实例
const formItemContext = Form.useInjectFormItemContext();

watch(innerValue, (newValue) => {
  const lastValue = newValue && newValue.length > 0 ? newValue[newValue.length - 1] : undefined;
  // const lastItem = selectedOptions && selectedOptions.length > 0? selectedOptions[selectedOptions.length - 1] as Fa.TreeNode<RecordType, KeyType> : undefined;
  // console.log('FaCascader.vue innerValue change to', newValue, 'lastValue', lastValue)
  emit('update:value', lastValue);
  emit('change', lastValue);
  # 触发Form更新
  formItemContext.onFieldChange();
});
```

## BasicForm使用slot自定义FormItem组件
1. 定义slot
2. 在元素template使用#slotName注入自定义组件

```vue
<BasicForm @register="registerForm">
  <template #eduFileId="{ model, field }">
    <a-input v-model:value="model[field]" placeholder="请选择课件" disabled>
      <template #addonAfter>
        <span class="cursor-pointer" @click="handleChange">选择课件</span>
      </template>
    </a-input>
  </template>
</BasicForm>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { genCommon } from "/@/utils/formUtils";
  
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      {
        ...genCommon('课件', 'eduFileId', 'Input'),
        slot: 'eduFileId', // 1. 定义slot
      },
    ],
  });
  
  function handleChange() {
    setFieldsValue({ eduFileId: 1 })
  }
</script>
```

## 监听表格字段变化
```vue
<BasicForm @register="registerForm" @field-value-change="handleValueChange" />

<script lang="ts" setup>
function handleValueChange(key:any, value:any) {
  console.log(key, value)
}
</script>
```
