# drawer
## basic usage
```vue
<ChatDrawer @register="registerChatDrawer" />

import { useDrawer } from '/@/components/Drawer';
import ChatDrawer from './components/chat/ChatDrawer.vue';

const [registerChatDrawer, { openDrawer: openChatDrawer }] = useDrawer();
```

### ChatDrawer
```vue
<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="内部聊天" width="280px" class="full-drawer chat-drawer">
    
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';

const emit = defineEmits(['register']);
const [registerDrawer, { changeLoading }] = useDrawerInner(init);

function init(data:any) {
  console.log('init', data)
}
</script>
<style lang="less">
</style>
```
